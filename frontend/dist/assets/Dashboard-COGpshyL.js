import{c as de,b as R,u as re,r as k,a as oe,j as e,L as K}from"./index-4-jyR6ws.js";import{j as G,e as M,k as H,l as ie,m as xe,n as z,o as me,p as J,q as he,r as D,s as V,t as ye,u as ge,v as X,w as pe,x as B,i as Z,y as P,z as ee,c as ue,A as te,B as se,C as be,D as fe,E as I,G as q,H as U}from"./index-yc4-8FIA.js";import{g as W,R as ke,a as ae,b as je,c as Ne,d as ve,e as we}from"./dateTimeService-DCOXGdMv.js";const Se={totalSales:0,totalRevenue:0,todaySales:0,totalDebts:0,unpaidDebts:0,lowStockCount:0,recentSales:[],topProducts:[]},E=de((o,p)=>({stats:Se,lowStockProducts:[],salesTrends:[],isLoading:!1,isStockLoading:!1,isTrendsLoading:!1,error:null,refreshInterval:null,autoRefresh:!1,selectedDateRange:"today",fetchStats:async()=>{try{o({isLoading:!0,error:null});const s=await R.get("/api/dashboard/stats");o({stats:s.data,isLoading:!1})}catch(s){console.error("Error fetching dashboard stats:",s),o({error:s instanceof Error?s.message:"Failed to fetch dashboard stats",isLoading:!1})}},fetchLowStockProducts:async()=>{try{o({isStockLoading:!0,error:null}),console.log("Fetching zero stock products...");const s=await R.get("/api/products/",{params:{zero_stock:!0,limit:10}});if(console.log("Low stock products response:",s.data),Array.isArray(s.data)){const h=s.data.map(y=>({id:y.id,name:y.name,currentStock:y.quantity,minRequired:y.min_quantity,unit:y.unit,category:y.category}));console.log("Transformed stock alerts:",h),o({lowStockProducts:h,isStockLoading:!1})}else console.error("Response data is not an array:",s.data),o({lowStockProducts:[],isStockLoading:!1})}catch(s){console.error("Error fetching low stock products:",s),o({error:s instanceof Error?s.message:"Failed to fetch low stock products",isStockLoading:!1})}},fetchSalesTrends:async s=>{try{o({isTrendsLoading:!0,error:null}),console.log(`Fetching sales trends for period: ${s}`);const y=(await R.get(`/api/dashboard/sales-trends/${s}`)).data.map(i=>{if(!i.date)return console.warn("Item missing date:",i),null;let b=0;return typeof i.amount=="number"?b=i.amount:typeof i.total=="number"?(b=i.total,console.log(`Using 'total' instead of 'amount' for ${i.date}: ${b}`)):console.warn(`Invalid amount for ${i.date}, defaulting to 0`),{date:i.date,amount:b}}).filter(Boolean);console.log("Normalized sales trends data:",y),o({salesTrends:y,isTrendsLoading:!1})}catch(h){console.error("Error fetching sales trends:",h),o({error:h instanceof Error?h.message:"Failed to fetch sales trends",isTrendsLoading:!1})}},updateStockLevel:async(s,h)=>{try{o({isStockLoading:!0,error:null});const y=await R.put(`/api/products/${s}/stock`,{quantity:h});return console.log("Stock update response:",y.data),await p().fetchLowStockProducts(),await p().fetchStats(),o({isStockLoading:!1}),y.data}catch(y){throw console.error("Error updating stock level:",y),o({error:y instanceof Error?y.message:"Failed to update stock level",isStockLoading:!1}),y}},toggleAutoRefresh:()=>{const{autoRefresh:s,refreshInterval:h}=p();if(h!==null&&window.clearInterval(h),s)o({autoRefresh:!1,refreshInterval:null});else{const y=window.setInterval(()=>{p().fetchStats(),p().fetchLowStockProducts();const{selectedDateRange:i}=p();let b;switch(i){case"today":b="day";break;case"month":b="month";break;case"year":b="year";break;default:b="week"}p().fetchSalesTrends(b)},6e4);o({autoRefresh:!0,refreshInterval:y})}},setRefreshInterval:s=>{const{refreshInterval:h}=p();if(h!==null&&window.clearInterval(h),s===null){o({refreshInterval:null});return}const y=window.setInterval(()=>{p().fetchStats(),p().fetchLowStockProducts();const{selectedDateRange:i}=p();let b;switch(i){case"today":b="day";break;case"month":b="month";break;case"year":b="year";break;default:b="week"}p().fetchSalesTrends(b)},s);o({refreshInterval:y,autoRefresh:!0})},setDateRange:s=>{o({selectedDateRange:s});let h;switch(s){case"today":h="day";break;case"month":h="month";break;case"year":h="year";break;default:h="week"}p().fetchSalesTrends(h)}})),Ce=()=>{const{salesTrends:o,isTrendsLoading:p,selectedDateRange:s,setDateRange:h,fetchSalesTrends:y}=E(),{user:i}=re(),g=(()=>{if(!o||o.length===0)return[];try{const r=o.map(a=>{let c=0;return typeof a.amount=="number"?c=a.amount:typeof a.total=="number"?c=a.total:c=0,{date:a.date,amount:c}});if(s==="today"){const a=new Map;for(let x=0;x<=23;x++)a.set(x,0);r.forEach(x=>{try{if(!x.date)return;const m=x.date,n=parseInt(m.split(":")[0]);!isNaN(n)&&n>=0&&n<=23&&a.set(n,x.amount)}catch{}});const c=Array.from(a.entries()).map(([x,m])=>({date:`${x.toString().padStart(2,"0")}:00`,amount:m}));return c.sort((x,m)=>{const n=parseInt(x.date.split(":")[0]),u=parseInt(m.date.split(":")[0]);return n-u}),c}else if(s==="week"||s==="month"){const a=new Map;Ne(s==="week"?7:30).forEach(n=>{a.set(n,0)}),r.forEach(n=>{try{if(!n.date)return;let u=n.date;if(a.has(u))a.set(u,n.amount);else try{const l=new Date(n.date);if(!isNaN(l.getTime())){const $=W(l);a.has($)&&a.set($,n.amount)}}catch{}}catch{}});const m=Array.from(a.entries()).map(([n,u])=>({date:n,amount:u}));return m.sort((n,u)=>n.date.localeCompare(u.date)),m}else if(s==="year"){const a=new Map;ve(12).forEach(m=>{a.set(m,0)}),r.forEach(m=>{try{if(!m.date)return;let n=m.date;if(a.has(n))a.set(n,m.amount);else try{const u=new Date(m.date);if(!isNaN(u.getTime())){const l=we(u);a.has(l)&&a.set(l,m.amount)}}catch{}}catch{}});const x=Array.from(a.entries()).map(([m,n])=>({date:m,amount:n}));return x.sort((m,n)=>m.date.localeCompare(n.date)),x}return r}catch{return[]}})(),j=async r=>{h(r);let a;switch(r){case"today":a="day";break;case"week":a="week";break;case"month":a="month";break;case"year":a="year";break;default:a="day"}try{await y(a)}catch{}},L=()=>{switch(s){case"today":return"مبيعات اليوم";case"week":return"مبيعات الأسبوع";case"month":return"مبيعات الشهر";case"year":return"مبيعات السنة";default:return"مبيعات الأسبوع"}},[v,w]=k.useState(!1),F=()=>{switch(s){case"today":return e.jsx(I,{className:"ml-1.5"});case"week":return e.jsx(q,{className:"ml-1.5"});case"month":return e.jsx(P,{className:"ml-1.5"});case"year":return e.jsx(U,{className:"ml-1.5"});default:return e.jsx(I,{className:"ml-1.5"})}},T=()=>{switch(s){case"today":return"اليوم";case"week":return"الأسبوع";case"month":return"الشهر";case"year":return"السنة";default:return"اليوم"}};return e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-800 dark:text-gray-100",children:L()}),e.jsxs("div",{className:"hidden md:flex space-x-2 space-x-reverse",children:[e.jsxs("button",{onClick:()=>j("today"),className:`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${s==="today"?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(I,{className:"ml-1.5"}),e.jsx("span",{children:"اليوم"})]}),e.jsxs("button",{onClick:()=>j("week"),className:`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${s==="week"?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(q,{className:"ml-1.5"}),e.jsx("span",{children:"الأسبوع"})]}),e.jsxs("button",{onClick:()=>j("month"),className:`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${s==="month"?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(P,{className:"ml-1.5"}),e.jsx("span",{children:"الشهر"})]}),e.jsxs("button",{onClick:()=>j("year"),className:`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${s==="year"?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(U,{className:"ml-1.5"}),e.jsx("span",{children:"السنة"})]})]}),e.jsxs("div",{className:"md:hidden relative",children:[e.jsxs("button",{onClick:()=>w(!v),className:"flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300",children:[F(),e.jsx("span",{children:T()}),e.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:v?"M5 15l7-7 7 7":"M19 9l-7 7-7-7"})})]}),v&&e.jsxs("div",{className:"absolute left-0 mt-2 w-40 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-10 border border-gray-200 dark:border-gray-700",children:[e.jsxs("button",{onClick:()=>{j("today"),w(!1)},className:`flex items-center px-3 py-2 w-full text-right text-sm transition-colors ${s==="today"?"bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}`,children:[e.jsx(I,{className:"ml-1.5"}),e.jsx("span",{children:"اليوم"})]}),e.jsxs("button",{onClick:()=>{j("week"),w(!1)},className:`flex items-center px-3 py-2 w-full text-right text-sm transition-colors ${s==="week"?"bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}`,children:[e.jsx(q,{className:"ml-1.5"}),e.jsx("span",{children:"الأسبوع"})]}),e.jsxs("button",{onClick:()=>{j("month"),w(!1)},className:`flex items-center px-3 py-2 w-full text-right text-sm transition-colors ${s==="month"?"bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}`,children:[e.jsx(P,{className:"ml-1.5"}),e.jsx("span",{children:"الشهر"})]}),e.jsxs("button",{onClick:()=>{j("year"),w(!1)},className:`flex items-center px-3 py-2 w-full text-right text-sm transition-colors ${s==="year"?"bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}`,children:[e.jsx(U,{className:"ml-1.5"}),e.jsx("span",{children:"السنة"})]})]})]})]}),p?e.jsx("div",{className:"flex justify-center items-center h-64 md:h-80",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"})}):e.jsxs("div",{children:[e.jsx("div",{className:"md:hidden flex justify-center items-center mb-3 bg-gray-50 dark:bg-gray-700 rounded-lg p-2",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-primary-600 mr-1"}),e.jsx("span",{className:"text-xs text-gray-700 dark:text-gray-300",children:"المبيعات"})]})}),e.jsx("div",{className:"h-80 md:h-64",children:e.jsx(ke,{type:"area",height:"100%",width:"100%",series:[{name:"المبيعات",data:g.map(r=>{const a=typeof r.amount=="number"?r.amount:0,c=Math.round(a*100)/100;return console.log(`Chart data point: ${r.date} = ${c}`),c})}],options:{chart:{type:"area",fontFamily:"Tajawal, sans-serif",toolbar:{show:!1},zoom:{enabled:!1},animations:{enabled:!0,speed:800,animateGradually:{enabled:!0,delay:150},dynamicAnimation:{enabled:!0,speed:350}},background:"transparent",locales:[{name:"ar",options:{months:["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"],shortMonths:["ي","ف","م","أ","م","ي","ي","أ","س","أ","ن","د"],days:["الأحد","الإثنين","الثلاثاء","الأربعاء","الخميس","الجمعة","السبت"],shortDays:["أحد","إثن","ثلا","أرب","خمي","جمع","سبت"]}}],defaultLocale:"ar"},colors:["#4F46E5"],fill:{type:"gradient",gradient:{shadeIntensity:1,opacityFrom:.4,opacityTo:.1,stops:[0,90,100]}},dataLabels:{enabled:!1},stroke:{curve:"smooth",width:3},grid:{borderColor:document.documentElement.classList.contains("dark")?"rgba(107, 114, 128, 0.2)":"rgba(107, 114, 128, 0.1)",row:{colors:["transparent","transparent"],opacity:.5},padding:{top:0,right:0,bottom:0,left:10},xaxis:{lines:{show:!0}},yaxis:{lines:{show:!0}}},markers:{size:4,colors:["#fff"],strokeColors:"#4F46E5",strokeWidth:2,hover:{size:7}},xaxis:{categories:g.map(r=>r.date),labels:{style:{colors:"#6B7280",fontSize:window.innerWidth<768?"10px":"12px"},formatter:r=>{if(!r)return"";try{if(s==="today")return r;if(s==="week"||s==="month"){if(r.includes("-")){const a=r.split("-");if(a.length===3)return a[2]}return r}else{if(r.includes("-")){const a=r.split("-");if(a.length===2){const c=parseInt(a[1]);if(!isNaN(c)&&c>=1&&c<=12)return je(c)}}return r}}catch{return r}}},axisBorder:{show:!0,color:"rgba(107, 114, 128, 0.3)"},axisTicks:{show:!0,color:"rgba(107, 114, 128, 0.3)"},tickAmount:s==="today"?8:void 0},yaxis:{labels:{style:{colors:"#6B7280",fontSize:window.innerWidth<768?"10px":"12px"},formatter:r=>r.toFixed(0)}},tooltip:{theme:document.documentElement.classList.contains("dark")?"dark":"light",x:{formatter:(r,{dataPointIndex:a})=>{try{if(a<0||a>=g.length)return"";const c=g[a].date;if(!c)return"";if(s==="today")return`الساعة ${c}`;if(s==="week"||s==="month"){if(c.includes("-")){const x=c.split("-");if(x.length===3){const m=x[0],n=parseInt(x[1]),u=parseInt(x[2]);if(!isNaN(n)&&!isNaN(u)&&n>=1&&n<=12&&u>=1&&u<=31)return`${u} ${ae(n)} ${m}`}}return`اليوم ${c}`}else{if(c.includes("-")){const x=c.split("-");if(x.length===2){const m=x[0],n=parseInt(x[1]);if(!isNaN(n)&&n>=1&&n<=12)return`${ae(n)} ${m}`}}return c}}catch{return""}}},y:{formatter:r=>`${r.toLocaleString()} د.ل`}},responsive:[{breakpoint:768,options:{chart:{height:280},markers:{size:3},xaxis:{labels:{style:{fontSize:"10px"},rotate:0,offsetY:0}},yaxis:{labels:{style:{fontSize:"10px"},formatter:r=>r>=1e3?`${(r/1e3).toFixed(1)}K`:r.toFixed(0)}},legend:{position:"bottom",offsetY:0}}}]}})}),e.jsx("div",{className:"md:hidden mt-3 bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm border border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"أعلى قيمة"}),e.jsxs("p",{className:"font-bold text-primary-700 dark:text-primary-300",children:[Math.max(...g.map(r=>typeof r.amount=="number"?r.amount:0)).toLocaleString()," د.ل"]}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400 block mt-1",children:(i==null?void 0:i.role)==="admin"?"جميع المستخدمين":"مبيعاتك فقط"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"متوسط المبيعات"}),e.jsxs("p",{className:"font-bold text-primary-700 dark:text-primary-300",children:[Math.round(g.reduce((r,a)=>r+(typeof a.amount=="number"?a.amount:0),0)/(g.length||1)).toLocaleString()," د.ل"]}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400 block mt-1",children:(i==null?void 0:i.role)==="admin"?"جميع المستخدمين":"مبيعاتك فقط"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"إجمالي المبيعات"}),e.jsxs("p",{className:"font-bold text-primary-700 dark:text-primary-300",children:[g.reduce((r,a)=>r+(typeof a.amount=="number"?a.amount:0),0).toLocaleString()," د.ل"]}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400 block mt-1",children:(i==null?void 0:i.role)==="admin"?"جميع المستخدمين":"مبيعاتك فقط"})]})]})})]})]})},_=(o,p)=>o==="آجل"?{text:"آجل",colorClass:"bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300"}:p==="partial"?{text:"جزئي",colorClass:"bg-orange-50 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300"}:o==="بطاقة"?{text:"بطاقة",colorClass:"bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"}:{text:"نقدي",colorClass:"bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300"},De=()=>{const{fetchStats:o,fetchLowStockProducts:p,fetchSalesTrends:s,updateStockLevel:h,toggleAutoRefresh:y,autoRefresh:i,lowStockProducts:b}=E(),{user:g}=re(),[j,L]=k.useState(!1),[v,w]=k.useState(null),[F,T]=k.useState(0),[r,a]=k.useState(!1),[c]=k.useState(null),[x,m]=k.useState(""),[n,u]=k.useState(!1),[l,$]=k.useState(null),[ne,A]=k.useState(!0),S=oe();k.useEffect(()=>((async()=>{A(!0);try{await o(),await p(),await s("day");const{stats:d}=E.getState(),Y={stats:{totalSales:d.totalSales||0,todaySales:d.todaySales||0,totalDebts:d.totalDebts||0,unpaidDebts:d.unpaidDebts||0,lowStockProducts:d.lowStockCount||0},recentSales:d.recentSales.map(f=>({id:f.id,date:new Date(f.createdAt).toLocaleString(),total:f.total,items:f.items,payment_method:f.payment_method||"نقدي",amount_paid:f.amount_paid||0,total_amount:f.total_amount||f.total,payment_status:f.payment_status||"paid"})),topProducts:d.topProducts.map(f=>({id:f.id,name:f.name,sold:f.quantity,revenue:f.total}))};$(Y),A(!1)}catch{$({stats:{totalSales:0,todaySales:0,totalDebts:0,unpaidDebts:0,lowStockProducts:0},recentSales:[],topProducts:[]}),A(!1)}})(),()=>{const{refreshInterval:d}=E.getState();d!==null&&window.clearInterval(d)}),[o,p,s]),k.useEffect(()=>{if(n){const t=setTimeout(()=>{u(!1)},3e3);return()=>clearTimeout(t)}},[n]);const N=t=>{t!=="dashboard"&&S(`/${t}`)},ce=async()=>{if(v&&F>=0)try{await h(v.id,F),L(!1),w(null),u(!0),p()}catch{alert("حدث خطأ أثناء تحديث المخزون. يرجى المحاولة مرة أخرى.")}},C=t=>{switch(t){case"new-sale":S("/pos");break;case"add-product":S("/products/new");break;case"update-inventory":S("/products?filter=low_stock");break;case"view-sales":S("/sales");break;case"print-report":S("/reports/sales");break}a(!1)},O=()=>{x.trim()&&S("/products",{state:{search:x.trim()}})},Q=()=>{S("/products",{state:{zeroStock:!0}})},le=l?[{title:"إجمالي المبيعات",value:`${l.stats.totalSales.toLocaleString()} عملية`,icon:e.jsx(G,{className:"text-primary-500"}),change:0,changeText:(g==null?void 0:g.role)==="admin"?"إجمالي عمليات البيع لجميع المستخدمين":"إجمالي عمليات البيع الخاصة بك"},{title:"مبيعات اليوم",value:`${l.stats.todaySales.toLocaleString()} د.ل`,icon:e.jsx(M,{className:"text-success-500"}),change:0,changeText:(g==null?void 0:g.role)==="admin"?"إجمالي مبيعات اليوم لجميع المستخدمين":"إجمالي مبيعات اليوم الخاصة بك"},{title:"ديون اليوم",value:`${l.stats.totalDebts.toLocaleString()} د.ل`,icon:e.jsx(H,{className:"text-warning-500"}),change:0,changeText:(g==null?void 0:g.role)==="admin"?"ديون اليوم لجميع المستخدمين":"ديون اليوم التي أنشأتها"},{title:"منتجات منخفضة المخزون",value:l.stats.lowStockProducts,icon:e.jsx(ie,{className:"text-warning-500"}),change:0,changeText:"تحتاج إلى تجديد المخزون"}]:[];return ne?e.jsx("div",{className:"flex items-center justify-center min-h-screen dark:bg-gray-900",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 dark:border-primary-400"})}):e.jsxs("div",{className:"container mx-auto px-4 py-6",children:[n&&e.jsxs("div",{className:"fixed top-5 right-5 bg-white dark:bg-gray-800 text-success-700 dark:text-success-300 px-5 py-4 rounded-xl shadow-lg transition-all z-50 flex items-center border-r-4 border-success-500 dark:border-success-400 animate-fadeIn",children:[e.jsx("div",{className:"bg-success-100 dark:bg-success-900/30 p-2 rounded-full mr-3",children:e.jsx(xe,{className:"text-success-600 dark:text-success-300"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-bold text-secondary-900 dark:text-secondary-100",children:"تم بنجاح"}),e.jsx("p",{className:"text-secondary-600 dark:text-secondary-400 text-sm",children:"تم تحديث المخزون بنجاح"})]})]}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-8 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4",children:[e.jsx("div",{className:"flex items-center min-w-0 flex-1",children:e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center",children:[e.jsx(z,{className:"ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:"لوحة التحكم"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block",children:"نظرة شاملة على أداء النظام والمبيعات"})]})}),e.jsx("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap",children:e.jsxs("button",{onClick:()=>y(),className:`flex items-center gap-2 px-3 sm:px-4 lg:px-5 py-2.5 rounded-lg transition-all shadow-md hover:shadow-lg font-medium ${i?"bg-primary-600 text-white hover:bg-primary-700":"bg-white/50 dark:bg-gray-700/50 text-gray-600 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-700 backdrop-blur-sm border border-gray-200 dark:border-gray-600"}`,children:[e.jsx(me,{className:`text-sm ${i?"animate-spin":""}`}),e.jsx("span",{className:"hidden sm:inline lg:inline",children:i?"إيقاف التحديث التلقائي":"تفعيل التحديث التلقائي"}),e.jsx("span",{className:"sm:hidden lg:hidden",children:i?"إيقاف":"تفعيل"})]})})]})})}),e.jsx("div",{className:"bg-primary-50 dark:bg-primary-900/20 rounded-xl shadow-soft p-5 mb-8",children:e.jsxs("div",{className:"flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-primary-800 dark:text-primary-300",children:"إجراءات سريعة"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 w-full sm:w-auto",children:[e.jsxs("button",{onClick:()=>C("new-sale"),className:"btn-action",children:[e.jsx("div",{className:"btn-action-icon",children:e.jsx(J,{})}),e.jsx("span",{children:"عملية بيع جديدة"})]}),e.jsxs("button",{onClick:()=>C("add-product"),className:"btn-action",children:[e.jsx("div",{className:"btn-action-icon",children:e.jsx(he,{})}),e.jsx("span",{children:"إضافة منتج"})]}),e.jsxs("button",{onClick:Q,className:"btn-action",children:[e.jsx("div",{className:"btn-action-icon",children:e.jsx(D,{})}),e.jsx("span",{children:"إدارة المخزون"})]}),e.jsx("button",{className:"btn-action overflow-hidden p-0",children:e.jsxs("div",{className:"flex items-center w-full h-full",children:[e.jsx("input",{type:"text",placeholder:"بحث عن منتج...",value:x,onChange:t=>m(t.target.value),onKeyUp:t=>t.key==="Enter"&&O(),className:"bg-white dark:bg-gray-800 border-0 outline-none py-3 px-4 w-full h-full text-primary-700 dark:text-primary-300 focus:ring-0"}),e.jsx("div",{onClick:O,className:"bg-primary-600 hover:bg-primary-700 text-white h-full py-3 px-4 flex items-center justify-center cursor-pointer",children:e.jsx(V,{})})]})})]})]})}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:le.map((t,d)=>e.jsxs("div",{className:"touch-card hover:shadow-lg transition-all duration-200",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-500 dark:text-secondary-400 text-sm font-medium mb-1",children:t.title}),e.jsx("p",{className:"text-3xl font-bold text-secondary-900 dark:text-secondary-100",children:t.value})]}),e.jsx("div",{className:"p-4 rounded-full bg-primary-50 dark:bg-primary-900/30 text-xl",children:t.icon})]}),e.jsxs("div",{className:`flex items-center text-sm ${t.change>0?"text-success-600 dark:text-success-400":t.change<0?"text-danger-600 dark:text-danger-400":"text-secondary-500 dark:text-secondary-400"}`,children:[t.change>0?e.jsx(ye,{className:"ml-1"}):t.change<0?e.jsx(ge,{className:"ml-1"}):null,e.jsxs("span",{children:[t.change!==0?`${Math.abs(t.change)}% `:"",t.changeText]})]})]},d))}),e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"nav-cards-grid",children:[e.jsxs("button",{onClick:()=>N("pos"),className:"touch-card nav-card p-4 sm:p-5 flex flex-col items-center text-center h-full",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-3 sm:p-4 rounded-full mb-2 sm:mb-3",children:e.jsx(J,{className:"text-xl sm:text-2xl"})}),e.jsx("h3",{className:"font-bold text-base sm:text-lg mb-1 dark:text-white",children:"نقطة البيع"}),e.jsx("p",{className:"text-secondary-500 dark:text-secondary-400 text-xs sm:text-sm",children:"إنشاء وإدارة المبيعات"})]}),e.jsxs("button",{onClick:()=>N("products"),className:"touch-card nav-card p-4 sm:p-5 flex flex-col items-center text-center h-full",children:[e.jsx("div",{className:"bg-secondary-100 dark:bg-secondary-900/30 text-secondary-600 dark:text-secondary-300 p-3 sm:p-4 rounded-full mb-2 sm:mb-3",children:e.jsx(D,{className:"text-xl sm:text-2xl"})}),e.jsx("h3",{className:"font-bold text-base sm:text-lg mb-1 dark:text-white",children:"المنتجات"}),e.jsx("p",{className:"text-secondary-500 dark:text-secondary-400 text-xs sm:text-sm",children:"إدارة المنتجات والمخزون"})]}),e.jsxs("button",{onClick:()=>N("sales"),className:"touch-card nav-card p-4 sm:p-5 flex flex-col items-center text-center h-full",children:[e.jsx("div",{className:"bg-success-100 dark:bg-success-900/30 text-success-600 dark:text-success-300 p-3 sm:p-4 rounded-full mb-2 sm:mb-3",children:e.jsx(G,{className:"text-xl sm:text-2xl"})}),e.jsx("h3",{className:"font-bold text-base sm:text-lg mb-1 dark:text-white",children:"المبيعات"}),e.jsx("p",{className:"text-secondary-500 dark:text-secondary-400 text-xs sm:text-sm",children:"سجل وتفاصيل المبيعات"})]}),e.jsxs("button",{onClick:()=>N("customers"),className:"touch-card nav-card p-4 sm:p-5 flex flex-col items-center text-center h-full",children:[e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300 p-3 sm:p-4 rounded-full mb-2 sm:mb-3",children:e.jsx(X,{className:"text-xl sm:text-2xl"})}),e.jsx("h3",{className:"font-bold text-base sm:text-lg mb-1 dark:text-white",children:"العملاء"}),e.jsx("p",{className:"text-secondary-500 dark:text-secondary-400 text-xs sm:text-sm",children:"إدارة بيانات العملاء"})]}),e.jsxs("button",{onClick:()=>N("debts"),className:"touch-card nav-card p-4 sm:p-5 flex flex-col items-center text-center h-full",children:[e.jsx("div",{className:"bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-300 p-3 sm:p-4 rounded-full mb-2 sm:mb-3",children:e.jsx(H,{className:"text-xl sm:text-2xl"})}),e.jsx("h3",{className:"font-bold text-base sm:text-lg mb-1 dark:text-white",children:"المديونية"}),e.jsx("p",{className:"text-secondary-500 dark:text-secondary-400 text-xs sm:text-sm",children:"إدارة ديون العملاء"})]}),(g==null?void 0:g.role)==="admin"&&e.jsxs("button",{onClick:()=>N("users"),className:"touch-card nav-card p-4 sm:p-5 flex flex-col items-center text-center h-full",children:[e.jsx("div",{className:"bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-300 p-3 sm:p-4 rounded-full mb-2 sm:mb-3",children:e.jsx(X,{className:"text-xl sm:text-2xl"})}),e.jsx("h3",{className:"font-bold text-base sm:text-lg mb-1 dark:text-white",children:"المستخدمين"}),e.jsx("p",{className:"text-secondary-500 dark:text-secondary-400 text-xs sm:text-sm",children:"إدارة المستخدمين"})]}),e.jsxs("button",{onClick:()=>N("reports"),className:"touch-card nav-card p-4 sm:p-5 flex flex-col items-center text-center h-full",children:[e.jsx("div",{className:"bg-warning-100 dark:bg-warning-900/30 text-warning-600 dark:text-warning-300 p-3 sm:p-4 rounded-full mb-2 sm:mb-3",children:e.jsx(z,{className:"text-xl sm:text-2xl"})}),e.jsx("h3",{className:"font-bold text-base sm:text-lg mb-1 dark:text-white",children:"التقارير"}),e.jsx("p",{className:"text-secondary-500 dark:text-secondary-400 text-xs sm:text-sm",children:"إحصائيات وتحليلات"})]}),(g==null?void 0:g.role)==="admin"&&e.jsxs("button",{onClick:()=>N("settings"),className:"touch-card nav-card p-4 sm:p-5 flex flex-col items-center text-center h-full",children:[e.jsx("div",{className:"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 p-3 sm:p-4 rounded-full mb-2 sm:mb-3",children:e.jsx(pe,{className:"text-xl sm:text-2xl"})}),e.jsx("h3",{className:"font-bold text-base sm:text-lg mb-1 dark:text-white",children:"الإعدادات"}),e.jsx("p",{className:"text-secondary-500 dark:text-secondary-400 text-xs sm:text-sm",children:"إعدادات النظام والمتجر"})]})]})}),e.jsx(Ce,{}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"touch-card",children:[e.jsxs("div",{className:"flex justify-between items-center mb-5",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-success-100 dark:bg-success-900/30 text-success-600 dark:text-success-300 p-2 rounded-lg ml-3",children:e.jsx(M,{className:"text-xl"})}),e.jsx("h2",{className:"font-bold text-xl text-secondary-900 dark:text-secondary-100",children:"آخر المبيعات"})]}),e.jsxs("button",{onClick:()=>N("sales"),className:"text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium flex items-center gap-1 transition-colors duration-200 px-2 py-1 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20",children:[e.jsx("span",{children:"عرض الكل"}),e.jsx(B,{className:"text-sm"})]})]}),e.jsx("div",{className:"hidden md:block",children:e.jsx("div",{className:"overflow-x-auto custom-scrollbar",children:e.jsxs("table",{className:"touch-table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{className:"text-right",children:"#"}),e.jsx("th",{className:"text-right",children:"التاريخ"}),e.jsx("th",{className:"text-right",children:"العناصر"}),e.jsx("th",{className:"text-right",children:"نوع الدفع"}),e.jsx("th",{className:"text-right",children:"المبلغ المدفوع"})]})}),e.jsx("tbody",{children:l==null?void 0:l.recentSales.map(t=>e.jsxs("tr",{className:"border-b dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[e.jsx("td",{className:"py-3 px-4 text-secondary-900 dark:text-secondary-100",children:e.jsxs(K,{to:`/receipt/${t.id}`,className:"text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 hover:underline font-medium flex items-center",children:[e.jsxs("span",{className:"bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 py-1 px-2 rounded-lg text-sm ml-2",children:["#",t.id]}),e.jsx(Z,{className:"text-sm"})]})}),e.jsx("td",{className:"py-3 px-4 text-secondary-700 dark:text-secondary-300",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(P,{className:"text-secondary-500 dark:text-secondary-400 ml-2"}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("div",{className:"flex items-center",children:e.jsx("span",{children:W(new Date(t.date))})}),e.jsxs("div",{className:"flex items-center mt-0.5",children:[e.jsx(ee,{className:"ml-1 text-xs text-secondary-500 dark:text-secondary-400"}),e.jsx("span",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:new Date(t.date).toLocaleTimeString("ar-LY",{hour:"2-digit",minute:"2-digit"})})]})]})]})}),e.jsx("td",{className:"py-3 px-4 text-secondary-700 dark:text-secondary-300",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(D,{className:"text-secondary-500 dark:text-secondary-400 ml-2"}),e.jsx("span",{className:"bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 py-1 px-2 rounded-lg text-sm",children:t.items})]})}),e.jsx("td",{className:"py-3 px-4 text-secondary-700 dark:text-secondary-300",children:e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:`py-1 px-2 rounded-lg text-sm font-medium ${_(t.payment_method,t.payment_status).colorClass}`,children:_(t.payment_method,t.payment_status).text})})}),e.jsx("td",{className:"py-3 px-4 font-medium text-secondary-900 dark:text-secondary-100",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("span",{className:"bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300 py-1 px-2 rounded-lg text-sm",children:[t.amount_paid.toFixed(2)," د.ل"]}),t.payment_status==="partial"&&t.total_amount>t.amount_paid&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["من أصل ",t.total_amount.toFixed(2)," د.ل"]})]})})]},t.id))})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx("div",{className:"grid grid-cols-1 gap-3",children:l==null?void 0:l.recentSales.map(t=>e.jsxs("div",{className:"p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsxs(K,{to:`/receipt/${t.id}`,className:"text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium flex items-center",children:[e.jsxs("span",{className:"bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 py-1 px-2 rounded-lg text-sm ml-1",children:["#",t.id]}),e.jsx(Z,{className:"text-xs"})]}),e.jsxs("div",{className:"flex flex-col text-xs text-secondary-500 dark:text-secondary-400",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(P,{className:"ml-1 text-xs"}),e.jsx("span",{children:W(new Date(t.date))})]}),e.jsxs("div",{className:"flex items-center mt-0.5",children:[e.jsx(ee,{className:"ml-1 text-xs"}),e.jsx("span",{children:new Date(t.date).toLocaleTimeString("ar-LY",{hour:"2-digit",minute:"2-digit"})})]})]})]}),e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(D,{className:"text-secondary-500 dark:text-secondary-400 ml-1 text-xs"}),e.jsxs("span",{className:"text-sm text-secondary-700 dark:text-secondary-300",children:[t.items," عناصر"]})]}),e.jsx("span",{className:`py-1 px-2 rounded-lg text-xs font-medium ${_(t.payment_method,t.payment_status).colorClass}`,children:_(t.payment_method,t.payment_status).text})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:"المبلغ المدفوع:"}),e.jsxs("div",{className:"flex flex-col items-end",children:[e.jsxs("span",{className:"font-medium text-success-700 dark:text-success-300 bg-success-50 dark:bg-success-900/30 py-1 px-2 rounded-lg text-sm",children:[t.amount_paid.toFixed(2)," د.ل"]}),t.payment_status==="partial"&&t.total_amount>t.amount_paid&&e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:["من أصل ",t.total_amount.toFixed(2)," د.ل"]})]})]})]},t.id))})}),(!(l!=null&&l.recentSales)||l.recentSales.length===0)&&e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center",children:[e.jsx("div",{className:"text-secondary-400 dark:text-secondary-500 mb-2",children:e.jsx(M,{className:"text-3xl mx-auto"})}),e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 font-medium",children:"لا توجد مبيعات حديثة"}),e.jsx("p",{className:"text-sm text-secondary-500 dark:text-secondary-400 mt-1",children:"ستظهر آخر المبيعات هنا بمجرد إتمام عمليات البيع"})]})]}),e.jsxs("div",{className:"touch-card",children:[e.jsxs("div",{className:"flex justify-between items-center mb-5",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3",children:e.jsx(ue,{className:"text-xl"})}),e.jsx("h2",{className:"font-bold text-xl text-secondary-900 dark:text-secondary-100",children:"أفضل المنتجات مبيعاً"})]}),e.jsxs("button",{onClick:()=>N("products"),className:"text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium flex items-center gap-1 transition-colors duration-200 px-2 py-1 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20",children:[e.jsx("span",{children:"عرض الكل"}),e.jsx(B,{className:"text-sm"})]})]}),e.jsx("div",{className:"hidden md:block",children:e.jsx("div",{className:"grid grid-cols-1 gap-3",children:l==null?void 0:l.topProducts.map((t,d)=>e.jsxs("div",{className:"flex items-center justify-between p-4 rounded-xl border border-gray-100 dark:border-gray-700 hover:shadow-md dark:hover:border-gray-600 transition-all duration-200",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`h-12 w-12 rounded-full flex items-center justify-center ml-4 ${d===0?"bg-warning-100 dark:bg-warning-900/50 text-warning-700 dark:text-warning-300":d===1?"bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300":d===2?"bg-success-100 dark:bg-success-900/50 text-success-700 dark:text-success-300":"bg-gray-100 dark:bg-gray-600 text-secondary-600 dark:text-secondary-300"}`,children:d===0?e.jsx(te,{className:"text-lg"}):e.jsx("span",{className:"font-bold text-lg",children:d+1})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-secondary-900 dark:text-secondary-100 text-lg",children:t.name}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(D,{className:"text-secondary-500 dark:text-secondary-400 ml-1"}),e.jsxs("p",{className:"text-sm text-secondary-500 dark:text-secondary-400",children:[t.sold," مبيعات"]})]})]})]}),e.jsxs("div",{className:"flex flex-col items-end",children:[e.jsxs("p",{className:"font-bold text-lg text-secondary-900 dark:text-secondary-100",children:[t.revenue.toLocaleString()," د.ل"]}),e.jsx("div",{className:`mt-1 px-2 py-0.5 rounded-full text-xs ${d===0?"bg-warning-100 dark:bg-warning-900/30 text-warning-700 dark:text-warning-300":d===1?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300":d===2?"bg-success-100 dark:bg-success-900/30 text-success-700 dark:text-success-300":"bg-gray-100 dark:bg-gray-700 text-secondary-600 dark:text-secondary-300"}`,children:d===0?"الأكثر مبيعاً":`المركز ${d+1}`})]})]},t.id))})}),e.jsx("div",{className:"md:hidden",children:e.jsx("div",{className:"grid grid-cols-1 gap-3",children:l==null?void 0:l.topProducts.map((t,d)=>e.jsxs("div",{className:"flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`h-10 w-10 rounded-full flex items-center justify-center ml-3 ${d===0?"bg-warning-100 dark:bg-warning-900/50 text-warning-700 dark:text-warning-300":d===1?"bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300":d===2?"bg-success-100 dark:bg-success-900/50 text-success-700 dark:text-success-300":"bg-gray-100 dark:bg-gray-600 text-secondary-600 dark:text-secondary-300"}`,children:d===0?e.jsx(te,{className:"text-sm"}):e.jsx("span",{className:"font-bold",children:d+1})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-secondary-900 dark:text-secondary-100",children:t.name}),e.jsxs("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:[t.sold," مبيعات"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-medium text-secondary-900 dark:text-secondary-100",children:[t.revenue.toLocaleString()," د.ل"]}),e.jsx("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:d===0?"الأكثر مبيعاً":`المركز ${d+1}`})]})]},t.id))})}),(!(l!=null&&l.topProducts)||l.topProducts.length===0)&&e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center",children:[e.jsx("div",{className:"text-secondary-400 dark:text-secondary-500 mb-2",children:e.jsx(z,{className:"text-3xl mx-auto"})}),e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 font-medium",children:"لا توجد بيانات مبيعات بعد"}),e.jsx("p",{className:"text-sm text-secondary-500 dark:text-secondary-400 mt-1",children:"ستظهر أفضل المنتجات مبيعاً هنا بمجرد إتمام عمليات البيع"})]})]})]}),e.jsxs("div",{className:"touch-card mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-5",children:[e.jsx("h2",{className:"font-bold text-xl text-secondary-900 dark:text-secondary-100",children:"منتجات نفذت كميتها"}),e.jsxs("button",{onClick:Q,className:"text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium flex items-center gap-1",children:[e.jsx("span",{children:"عرض الكل"}),e.jsx(B,{className:"text-sm"})]})]}),e.jsx("div",{className:"overflow-x-auto custom-scrollbar",children:e.jsxs("table",{className:"touch-table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{className:"text-right",children:"المنتج"}),e.jsx("th",{className:"text-right",children:"الكمية الحالية"}),e.jsx("th",{className:"text-right",children:"الحد الأدنى"}),e.jsx("th",{className:"text-right",children:"الحالة"}),e.jsx("th",{className:"text-right",children:"الإجراء"})]})}),e.jsx("tbody",{children:b.length>0?b.map(t=>e.jsxs("tr",{className:"border-b dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-700",children:[e.jsx("td",{className:"py-3 px-4 text-secondary-900 dark:text-secondary-100 font-medium",children:t.name}),e.jsxs("td",{className:"py-3 px-4 text-secondary-700 dark:text-secondary-300",children:[t.currentStock," ",t.unit]}),e.jsxs("td",{className:"py-3 px-4 text-secondary-700 dark:text-secondary-300",children:[t.minRequired," ",t.unit]}),e.jsx("td",{className:"py-3 px-4",children:e.jsx("span",{className:"py-1 px-2 rounded-lg text-sm bg-danger-50 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300",children:"نفذت الكمية"})}),e.jsx("td",{className:"py-3 px-4",children:e.jsx("button",{onClick:()=>{w({id:t.id,name:t.name,quantity:t.currentStock}),T(t.currentStock+t.minRequired),L(!0)},className:"text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium",children:"تحديث المخزون"})})]},t.id)):e.jsx("tr",{children:e.jsx("td",{colSpan:5,className:"py-8 text-center text-secondary-500 dark:text-secondary-400",children:"لا توجد منتجات نفذت كميتها حال"})})})]})})]}),j&&v&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-auto bg-black bg-opacity-60 flex items-center justify-center",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 w-full max-w-md mx-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-5",children:[e.jsx("h3",{className:"text-xl font-bold text-secondary-900 dark:text-secondary-100",children:"تحديث المخزون"}),e.jsx("button",{onClick:()=>L(!1),className:"text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300 text-2xl",children:"×"})]}),e.jsxs("div",{className:"mb-5",children:[e.jsx("label",{className:"block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2",children:"اسم المنتج"}),e.jsx("div",{className:"text-secondary-900 dark:text-secondary-100 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg font-medium",children:v.name})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2",children:"الكمية الجديدة"}),e.jsx("input",{type:"number",min:"0",value:F,onChange:t=>T(parseInt(t.target.value)||0),className:"input-field"})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{type:"button",onClick:()=>L(!1),className:"btn-secondary",children:"إلغاء"}),e.jsx("button",{type:"button",onClick:ce,className:"btn-primary",children:"تحديث"})]})]})}),r&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-auto bg-black bg-opacity-60 flex items-center justify-center",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 w-full max-w-md mx-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-5",children:[e.jsx("h3",{className:"text-xl font-bold text-secondary-900 dark:text-secondary-100",children:c==="sales"?"إدارة المبيعات":c==="products"?"إدارة المنتجات":"التقارير"}),e.jsx("button",{onClick:()=>a(!1),className:"text-secondary-500 dark:text-secondary-400 hover:text-secondary-700 dark:hover:text-secondary-300 text-2xl",children:"×"})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-3 mb-5",children:[c==="sales"&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>C("new-sale"),className:"btn-secondary flex items-center justify-center gap-2 py-3 text-base",children:[e.jsx(se,{}),e.jsx("span",{children:"عملية بيع جديدة"})]}),e.jsxs("button",{onClick:()=>C("view-sales"),className:"btn-secondary flex items-center justify-center gap-2 py-3 text-base",children:[e.jsx(V,{}),e.jsx("span",{children:"عرض المبيعات"})]})]}),c==="products"&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>C("add-product"),className:"btn-secondary flex items-center justify-center gap-2 py-3 text-base",children:[e.jsx(se,{}),e.jsx("span",{children:"إضافة منتج جديد"})]}),e.jsxs("button",{onClick:()=>C("update-inventory"),className:"btn-secondary flex items-center justify-center gap-2 py-3 text-base",children:[e.jsx(be,{}),e.jsx("span",{children:"تحديث المخزون"})]})]}),c==="reports"&&e.jsx(e.Fragment,{children:e.jsxs("button",{onClick:()=>C("print-report"),className:"btn-secondary flex items-center justify-center gap-2 py-3 text-base",children:[e.jsx(fe,{}),e.jsx("span",{children:"طباعة تقرير المبيعات"})]})})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{type:"button",onClick:()=>a(!1),className:"btn-secondary",children:"إغلاق"})})]})})]})};export{De as default};
