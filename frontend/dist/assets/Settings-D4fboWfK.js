import{r as l,u as H,d as P,a as Q,b as A,j as e}from"./index-B6Ptcst5.js";import{O as X,w as D,ah as Z,ai as ee,aj as re,o as ae,ak as O,m as M,Y as p,al as se,k as te,e as oe,b as U,aa as de,F as ne,a as le,am as ie}from"./index-CD3RUED3.js";const xe=()=>{var $,T,E;const[c,L]=l.useState("store"),[o,m]=l.useState({}),[u,w]=l.useState({}),[k,f]=l.useState(!1),[g,y]=l.useState(!1),[_,d]=l.useState(""),[S,n]=l.useState(""),{user:j}=H(),{setTheme:R}=P(),G=Q(),x=[{id:"store",name:"معلومات المتجر",icon:e.jsx(se,{}),settings:[{key:"store_name",name:"اسم المتجر",value:"",type:"text"},{key:"store_address",name:"عنوان المتجر",value:"",type:"textarea"},{key:"store_phone",name:"رقم الهاتف",value:"",type:"text"},{key:"store_email",name:"البريد الإلكتروني",value:"",type:"text"}]},{id:"currency",name:"العملة والضرائب",icon:e.jsx(te,{}),settings:[{key:"currency_symbol",name:"رمز العملة",value:"",type:"text",description:"مثال: د.ل, $, €"},{key:"decimal_places",name:"عدد الأرقام العشرية",value:"",type:"number"},{key:"tax_rate",name:"نسبة الضريبة (%)",value:"",type:"number"},{key:"tax_included",name:"الأسعار تشمل الضريبة",value:"",type:"boolean"}]},{id:"receipt",name:"إعدادات الفاتورة",icon:e.jsx(oe,{}),settings:[{key:"receipt_header",name:"رأس الفاتورة",value:"",type:"textarea"},{key:"receipt_footer",name:"تذييل الفاتورة",value:"",type:"textarea"},{key:"receipt_show_tax",name:"إظهار الضريبة في الفاتورة",value:"",type:"boolean"},{key:"receipt_show_cashier",name:"إظهار اسم الكاشير في الفاتورة",value:"",type:"boolean"}]},{id:"system",name:"إعدادات النظام",icon:e.jsx(D,{}),settings:[{key:"low_stock_threshold",name:"حد المخزون المنخفض",value:"",type:"number"},{key:"auto_refresh_data",name:"تحديث البيانات تلقائياً",value:"",type:"boolean"},{key:"theme_mode",name:"مظهر التطبيق",value:"",type:"theme",description:"اختر بين المظهر الفاتح والمظهر الداكن"},{key:"system_activated",name:"تفعيل النظام",value:"",type:"boolean",description:"تفعيل أو إلغاء تفعيل النظام"}]},{id:"license",name:"الترخيص والتفعيل",icon:e.jsx(U,{}),settings:[{key:"license_key",name:"مفتاح الترخيص",value:"",type:"text",description:"أدخل مفتاح الترخيص الخاص بك لتفعيل النظام"},{key:"license_status",name:"حالة الترخيص",value:"",type:"text",description:"حالة الترخيص الحالية"},{key:"license_expires",name:"تاريخ انتهاء الترخيص",value:"",type:"text",description:"تاريخ انتهاء صلاحية الترخيص"}]}];l.useEffect(()=>{F()},[]);const F=async()=>{f(!0);try{const a=(await A.get("/api/settings")).data.reduce((s,t)=>(s[t.key]=t.value,s),{});a.theme_mode||(a.theme_mode=localStorage.getItem("theme")||"light"),m(a),w(a),f(!1)}catch(r){console.error("Error fetching settings:",r),n("فشل في تحميل الإعدادات. يرجى المحاولة مرة أخرى."),f(!1),setTimeout(()=>n(""),3e3)}},v=()=>Object.keys(o).some(r=>o[r]!==u[r]),b=()=>Object.keys(o).some(r=>N(r,o[r])!==null),i=(r,a)=>{if(r==="theme_mode"&&R(a),r==="system_activated"&&a!==o[r]){const t=a===!0||a==="true"?"هل أنت متأكد من تفعيل النظام؟":"هل أنت متأكد من إلغاء تفعيل النظام؟ سيؤثر هذا على جميع العمليات.";if(!window.confirm(t))return}m({...o,[r]:a.toString()})},C=async()=>{y(!0);try{const r=[];if(Object.keys(o).forEach(s=>{const t=N(s,o[s]);t&&r.push(`${s}: ${t}`)}),r.length>0){n("يرجى تصحيح الأخطاء التالية: "+r.join(", ")),y(!1),setTimeout(()=>n(""),5e3);return}const a=Object.keys(o).filter(s=>o[s]!==u[s]).map(s=>({key:s,value:o[s]}));a.length>0?(await A.post("/api/settings/batch",{settings:a}),w(o),d("تم حفظ الإعدادات بنجاح"),setTimeout(()=>d(""),3e3)):(d("لا توجد تغييرات للحفظ"),setTimeout(()=>d(""),3e3)),y(!1)}catch(r){console.error("Error saving settings:",r),n("فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى."),y(!1),setTimeout(()=>n(""),3e3)}},J=()=>{m(u)},B=()=>{const r=JSON.stringify(o,null,2),a="data:application/json;charset=utf-8,"+encodeURIComponent(r),s=`smartpos-settings-${new Date().toISOString().split("T")[0]}.json`,t=document.createElement("a");t.setAttribute("href",a),t.setAttribute("download",s),t.click(),d("تم تصدير الإعدادات بنجاح"),setTimeout(()=>d(""),3e3)},K=r=>{var t;const a=(t=r.target.files)==null?void 0:t[0];if(!a)return;const s=new FileReader;s.onload=q=>{var I;try{const h=JSON.parse((I=q.target)==null?void 0:I.result);if(typeof h!="object"||h===null)throw new Error("Invalid settings format");const z={...o,...h};m(z),d("تم استيراد الإعدادات بنجاح"),setTimeout(()=>d(""),3e3)}catch{n("فشل في استيراد الإعدادات. تأكد من صحة الملف."),setTimeout(()=>n(""),3e3)}},s.readAsText(a),r.target.value=""},V=async r=>{try{if(r.length<10)throw new Error("مفتاح الترخيص غير صحيح");const a={...o,license_status:"نشط",license_expires:"2025-12-31",system_activated:"true"};m(a),d("تم تفعيل الترخيص بنجاح"),setTimeout(()=>d(""),3e3)}catch{n("فشل في تفعيل الترخيص. تأكد من صحة المفتاح."),setTimeout(()=>n(""),3e3)}},W=()=>{window.confirm("هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟ سيتم فقدان جميع التخصيصات الحالية.")&&(m({store_name:"متجر ذكي",store_address:"طرابلس، ليبيا",store_phone:"+218-91-1234567",store_email:"<EMAIL>",currency_symbol:"د.ل",decimal_places:"2",tax_rate:"0",tax_included:"false",receipt_header:"مرحباً بكم في متجرنا",receipt_footer:"شكراً لزيارتكم",receipt_show_tax:"true",receipt_show_cashier:"true",low_stock_threshold:"10",auto_refresh_data:"true",theme_mode:"light",system_activated:"true",license_key:"",license_status:"نشط",license_expires:"2025-12-31"}),d("تم إعادة تعيين الإعدادات إلى القيم الافتراضية"),setTimeout(()=>d(""),3e3))},N=(r,a)=>{switch(r){case"store_email":if(a&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))return"البريد الإلكتروني غير صحيح";break;case"store_phone":if(a&&!/^[\d\s\-\+\(\)]+$/.test(a))return"رقم الهاتف غير صحيح";break;case"decimal_places":if(a&&(parseInt(a)<0||parseInt(a)>4))return"عدد الأرقام العشرية يجب أن يكون بين 0 و 4";break;case"tax_rate":if(a&&(parseFloat(a)<0||parseFloat(a)>100))return"نسبة الضريبة يجب أن تكون بين 0 و 100";break;case"low_stock_threshold":if(a&&parseInt(a)<0)return"حد المخزون المنخفض يجب أن يكون أكبر من أو يساوي 0";break}return null},Y=r=>{const a=o[r.key]||"",s=N(r.key,a);switch(r.type){case"textarea":return e.jsxs("div",{children:[e.jsx("textarea",{value:a,onChange:t=>i(r.key,t.target.value),rows:3,className:`w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 transition-colors ${s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-primary-500 focus:border-primary-500"} dark:bg-gray-700 dark:text-gray-100`}),s&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s})]});case"number":return e.jsxs("div",{children:[e.jsx("input",{type:"number",value:a,onChange:t=>i(r.key,t.target.value),className:`w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 transition-colors ${s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-primary-500 focus:border-primary-500"} dark:bg-gray-700 dark:text-gray-100`}),s&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s})]});case"boolean":return e.jsxs("label",{className:"flex items-center cursor-pointer group",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"checkbox",checked:a==="true",onChange:t=>i(r.key,t.target.checked),className:"sr-only"}),e.jsx("div",{className:`block w-14 h-8 rounded-full transition-all duration-200 ${a==="true"?"bg-primary-500 shadow-lg":"bg-gray-300 dark:bg-gray-600 group-hover:bg-gray-400 dark:group-hover:bg-gray-500"}`}),e.jsx("div",{className:`absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition-all duration-200 transform shadow-md ${a==="true"?"translate-x-6":""}`})]}),e.jsx("div",{className:`mr-3 font-medium transition-colors ${a==="true"?"text-primary-600 dark:text-primary-400":"text-gray-700 dark:text-gray-300"}`,children:a==="true"?"مفعل":"غير مفعل"})]});case"theme":return e.jsx("div",{className:"flex flex-col space-y-3",children:e.jsxs("div",{className:"flex space-x-4 space-x-reverse",children:[e.jsxs("button",{type:"button",onClick:()=>i(r.key,"light"),className:`flex flex-col items-center p-3 rounded-lg border-2 transition-all ${a==="light"?"border-primary-500 bg-primary-50 dark:bg-primary-900/30":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[e.jsx(ne,{className:"text-2xl text-yellow-500 mb-2"}),e.jsx("span",{className:"text-sm font-medium dark:text-gray-200",children:"فاتح"})]}),e.jsxs("button",{type:"button",onClick:()=>i(r.key,"dark"),className:`flex flex-col items-center p-3 rounded-lg border-2 transition-all ${a==="dark"?"border-primary-500 bg-primary-50 dark:bg-primary-900/30":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[e.jsx(le,{className:"text-2xl text-gray-700 dark:text-gray-400 mb-2"}),e.jsx("span",{className:"text-sm font-medium dark:text-gray-200",children:"داكن"})]}),e.jsxs("button",{type:"button",onClick:()=>i(r.key,"system"),className:`flex flex-col items-center p-3 rounded-lg border-2 transition-all ${a==="system"?"border-primary-500 bg-primary-50 dark:bg-primary-900/30":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[e.jsx(ie,{className:"text-2xl text-blue-500 mb-2"}),e.jsx("span",{className:"text-sm font-medium dark:text-gray-200",children:"تلقائي"})]})]})});case"text":default:return e.jsxs("div",{children:[e.jsxs("div",{className:"relative",children:[r.key==="license_key"&&e.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:e.jsx(de,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{type:r.key==="license_key"?"password":"text",value:a,onChange:t=>i(r.key,t.target.value),className:`w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 transition-colors ${r.key==="license_key"?"pr-10":""} ${s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-primary-500 focus:border-primary-500"} dark:bg-gray-700 dark:text-gray-100`,disabled:r.key==="license_status"||r.key==="license_expires",placeholder:r.key==="license_key"?"أدخل مفتاح الترخيص...":""})]}),s&&e.jsx("p",{className:"text-red-500 text-sm mt-1",children:s}),r.key==="license_key"&&a&&e.jsxs("button",{onClick:()=>V(a),className:"mt-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-sm flex items-center",children:[e.jsx(U,{className:"ml-2"}),"تفعيل الترخيص"]}),r.key==="license_status"&&a==="نشط"&&e.jsxs("p",{className:"text-green-600 dark:text-green-400 text-sm mt-1 flex items-center",children:[e.jsx(M,{className:"ml-2"}),"الترخيص نشط ومفعل"]}),r.key==="license_status"&&a!=="نشط"&&a&&e.jsxs("p",{className:"text-red-600 dark:text-red-400 text-sm mt-1 flex items-center",children:[e.jsx(p,{className:"ml-2"}),"الترخيص غير نشط"]})]})}};return(j==null?void 0:j.role)!=="admin"?e.jsx("div",{className:"container mx-auto px-4 py-6",children:e.jsx("div",{className:"bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 p-4 rounded-lg",children:"يجب أن تكون مديراً للوصول إلى صفحة الإعدادات."})}):e.jsxs("div",{className:"container mx-auto px-4 py-6",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx("button",{onClick:()=>G("/"),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2.5 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm flex-shrink-0",title:"العودة للرئيسية",children:e.jsx(X,{className:"text-sm"})}),e.jsxs("div",{className:"mr-3 sm:mr-4 min-w-0 flex-1",children:[e.jsxs("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center",children:[e.jsx(D,{className:"ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:"إعدادات النظام"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block",children:"تخصيص وإدارة إعدادات التطبيق"})]})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap",children:[e.jsx("button",{onClick:B,className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"تصدير الإعدادات",children:e.jsx(Z,{className:"text-sm"})}),e.jsxs("label",{className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 cursor-pointer rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"استيراد الإعدادات",children:[e.jsx(ee,{className:"text-sm"}),e.jsx("input",{type:"file",accept:".json",onChange:K,className:"hidden"})]}),e.jsx("button",{onClick:W,className:"text-gray-600 dark:text-gray-300 hover:text-orange-600 dark:hover:text-orange-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"إعادة تعيين إلى القيم الافتراضية",children:e.jsx(re,{className:"text-sm"})}),e.jsx("button",{onClick:F,className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"تحديث",disabled:k,children:e.jsx(ae,{className:`text-sm ${k?"animate-spin":""}`})}),e.jsxs("button",{onClick:C,disabled:!v()||g||b(),className:`bg-primary-600 text-white rounded-lg py-2.5 px-3 sm:px-4 lg:px-5 flex items-center transition-all shadow-md hover:shadow-lg font-medium ${!v()||g||b()?"opacity-50 cursor-not-allowed":"hover:bg-primary-700"}`,children:[e.jsx(O,{className:"ml-2 text-sm"}),e.jsx("span",{className:"hidden sm:inline lg:inline",children:"حفظ التغييرات"}),e.jsx("span",{className:"sm:hidden lg:hidden",children:"حفظ"})]})]})]})})}),_&&e.jsxs("div",{className:"bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-200 p-4 rounded-lg mb-6 flex items-center shadow-sm border border-green-200 dark:border-green-800",children:[e.jsx(M,{className:"ml-3 text-lg"}),e.jsx("span",{className:"font-medium",children:_})]}),S&&e.jsxs("div",{className:"bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-200 p-4 rounded-lg mb-6 flex items-center shadow-sm border border-red-200 dark:border-red-800",children:[e.jsx(p,{className:"ml-3 text-lg flex-shrink-0"}),e.jsx("span",{className:"font-medium",children:S})]}),o.system_activated==="false"&&e.jsxs("div",{className:"bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-200 p-4 rounded-lg mb-6 flex items-center shadow-sm border border-yellow-200 dark:border-yellow-800",children:[e.jsx(p,{className:"ml-3 text-lg flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"تحذير:"})," النظام غير مفعل حالياً. قم بتفعيله من إعدادات النظام."]})]}),o.license_expires&&new Date(o.license_expires)<new Date(Date.now()+30*24*60*60*1e3)&&e.jsxs("div",{className:"bg-orange-100 dark:bg-orange-900/50 text-orange-700 dark:text-orange-200 p-4 rounded-lg mb-6 flex items-center shadow-sm border border-orange-200 dark:border-orange-800",children:[e.jsx(p,{className:"ml-3 text-lg flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"تنبيه:"})," سينتهي الترخيص قريباً في ",o.license_expires,". يرجى تجديد الترخيص."]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700",children:[e.jsx("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600",children:e.jsx("h3",{className:"font-semibold text-gray-800 dark:text-gray-200 text-center",children:"أقسام الإعدادات"})}),x.map((r,a)=>e.jsxs("button",{onClick:()=>L(r.id),className:`w-full flex items-center p-4 transition-all duration-200 ${c===r.id?"bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border-r-4 border-primary-500 shadow-sm":"text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"} ${a!==x.length-1?"border-b border-gray-100 dark:border-gray-700":""}`,children:[e.jsx("span",{className:`text-lg ml-3 transition-colors ${c===r.id?"text-primary-600 dark:text-primary-400":""}`,children:r.icon}),e.jsx("span",{className:"font-medium",children:r.name})]},r.id))]})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700",children:k?e.jsxs("div",{className:"flex flex-col justify-center items-center h-64",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"جارٍ تحميل الإعدادات..."})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"p-6 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600",children:e.jsxs("h2",{className:"text-xl font-semibold text-gray-800 dark:text-gray-100 flex items-center",children:[e.jsx("span",{className:"text-primary-600 dark:text-primary-400 ml-3",children:($=x.find(r=>r.id===c))==null?void 0:$.icon}),(T=x.find(r=>r.id===c))==null?void 0:T.name]})}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"space-y-6",children:(E=x.find(r=>r.id===c))==null?void 0:E.settings.map((r,a)=>{var s;return e.jsxs("div",{className:`${a!==(((s=x.find(t=>t.id===c))==null?void 0:s.settings.length)||0)-1?"border-b border-gray-100 dark:border-gray-700 pb-6":""}`,children:[e.jsxs("label",{className:"block mb-3",children:[e.jsx("span",{className:"text-gray-700 dark:text-gray-200 font-medium text-lg",children:r.name}),r.description&&e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1 leading-relaxed",children:r.description})]}),Y(r)]},r.key)})})}),v()&&e.jsx("div",{className:"p-6 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4",children:[e.jsx("div",{className:"text-sm",children:b()?e.jsxs("div",{className:"text-red-600 dark:text-red-400",children:[e.jsx("span",{className:"font-medium",children:"يوجد أخطاء في البيانات"})," - يرجى تصحيحها قبل الحفظ"]}):e.jsxs("div",{className:"text-gray-600 dark:text-gray-400",children:[e.jsx("span",{className:"font-medium",children:"تم تعديل الإعدادات"})," - لا تنس حفظ التغييرات"]})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsx("button",{onClick:J,className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-md transition-colors font-medium text-center",children:"إلغاء التغييرات"}),e.jsxs("button",{onClick:C,disabled:g||b(),className:"bg-primary-600 text-white rounded-md py-2 px-6 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-all shadow-sm hover:shadow-md font-medium",children:[e.jsx(O,{className:"ml-2"}),e.jsx("span",{children:g?"جارٍ الحفظ...":"حفظ التغييرات"})]})]})]})})]})})})]})]})};export{xe as default};
