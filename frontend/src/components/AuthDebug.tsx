import React, { useState } from 'react';
import { useAuthStore } from '../stores/authStore';
import { Fa<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ey, <PERSON>aSync, FaTrash, FaExclamation<PERSON>riangle, FaCheckCircle } from 'react-icons/fa';

interface AuthDebugProps {
  className?: string;
}

const AuthDebug: React.FC<AuthDebugProps> = ({ className = '' }) => {
  const {
    token,
    isAuthenticated,
    isInitialized,
    isLoading,
    error,
    clearAuth
  } = useAuthStore();

  const [testResult, setTestResult] = useState<{
    status: 'idle' | 'testing' | 'success' | 'error';
    message: string;
  }>({ status: 'idle', message: '' });

  const handleClearAuth = () => {
    clearAuth();
    window.location.reload();
  };

  const handleTestToken = async () => {
    if (!token) {
      setTestResult({ status: 'error', message: 'لا يوجد توكن للاختبار' });
      return;
    }

    setTestResult({ status: 'testing', message: 'جاري اختبار التوكن...' });

    try {
      // استخدام نفس منطق الحصول على عنوان الخادم المستخدم في باقي التطبيق
      const getBackendURL = () => {
        const hostname = window.location.hostname;
        if (hostname === 'localhost' || hostname === '127.0.0.1') {
          return 'http://localhost:8002';
        }
        return `http://${hostname}:8002`;
      };

      const response = await fetch(`${getBackendURL()}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const userData = await response.json();
        console.log('Token test successful:', userData);
        setTestResult({
          status: 'success',
          message: `التوكن صالح - مستخدم: ${userData.username}`
        });
      } else {
        console.error('Token test failed:', response.status, response.statusText);
        setTestResult({
          status: 'error',
          message: `فشل اختبار التوكن: ${response.status}`
        });
      }
    } catch (error) {
      console.error('Token test error:', error);
      setTestResult({
        status: 'error',
        message: 'خطأ في الاتصال بالخادم'
      });
    }
  };

  const getTokenInfo = () => {
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const exp = new Date(payload.exp * 1000);
      const now = new Date();
      const isExpired = exp < now;

      return {
        username: payload.sub,
        role: payload.role,
        expiresAt: exp.toLocaleString('ar-LY'),
        isExpired
      };
    } catch (error) {
      return null;
    }
  };

  const tokenInfo = getTokenInfo();

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <div className="flex items-center mb-4">
        <div className="bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3">
          <FaUserCheck className="text-xl" />
        </div>
        <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100">حالة التوثيق</h3>
      </div>

      {/* Authentication Status Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* Initialization Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">حالة التهيئة:</span>
          <span className={`text-sm font-bold ${isInitialized ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
            {isInitialized ? 'مُهيأ' : 'غير مُهيأ'}
          </span>
        </div>

        {/* Authentication Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">حالة المصادقة:</span>
          <span className={`text-sm font-bold ${isAuthenticated ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
            {isAuthenticated ? 'مُصادق' : 'غير مُصادق'}
          </span>
        </div>

        {/* Loading Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">حالة التحميل:</span>
          <span className={`text-sm font-bold ${isLoading ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-600 dark:text-gray-400'}`}>
            {isLoading ? 'يحمل...' : 'مكتمل'}
          </span>
        </div>

        {/* Token Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">التوكن:</span>
          <span className={`text-sm font-bold ${token ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
            {token ? 'موجود' : 'غير موجود'}
          </span>
        </div>
      </div>



      {/* Token Information */}
      {tokenInfo && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
          <h4 className="text-sm font-bold text-green-900 dark:text-green-100 mb-2">معلومات التوكن:</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-green-700 dark:text-green-300">المستخدم:</span>
              <span className="font-medium text-green-900 dark:text-green-100">{tokenInfo.username}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-700 dark:text-green-300">الدور:</span>
              <span className="font-medium text-green-900 dark:text-green-100">{tokenInfo.role}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-700 dark:text-green-300">ينتهي في:</span>
              <span className={`font-medium ${tokenInfo.isExpired ? 'text-red-600 dark:text-red-400' : 'text-green-900 dark:text-green-100'}`}>
                {tokenInfo.expiresAt}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-700 dark:text-green-300">الحالة:</span>
              <span className={`font-medium ${tokenInfo.isExpired ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                {tokenInfo.isExpired ? 'منتهي الصلاحية' : 'صالح'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
          <div className="flex items-center">
            <FaExclamationTriangle className="text-red-600 dark:text-red-400 ml-2" />
            <h4 className="text-sm font-bold text-red-900 dark:text-red-100">خطأ في التوثيق:</h4>
          </div>
          <p className="text-sm text-red-700 dark:text-red-300 mt-1">{error}</p>
        </div>
      )}

      {/* Test Result Display */}
      {testResult.status !== 'idle' && (
        <div className={`mb-6 p-4 rounded-lg border ${
          testResult.status === 'success'
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
            : testResult.status === 'error'
            ? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
            : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
        }`}>
          <div className="flex items-center">
            {testResult.status === 'success' && <FaCheckCircle className="text-green-600 dark:text-green-400 ml-2" />}
            {testResult.status === 'error' && <FaExclamationTriangle className="text-red-600 dark:text-red-400 ml-2" />}
            {testResult.status === 'testing' && <FaSync className="text-yellow-600 dark:text-yellow-400 ml-2 animate-spin" />}
            <p className={`text-sm font-medium ${
              testResult.status === 'success'
                ? 'text-green-900 dark:text-green-100'
                : testResult.status === 'error'
                ? 'text-red-900 dark:text-red-100'
                : 'text-yellow-900 dark:text-yellow-100'
            }`}>
              {testResult.message}
            </p>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3">
        <button
          onClick={handleTestToken}
          disabled={!token || testResult.status === 'testing'}
          className="flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors text-sm font-medium"
        >
          <FaKey className="ml-2" />
          <span>اختبار التوكن</span>
        </button>

        <button
          onClick={handleClearAuth}
          className="flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm font-medium"
        >
          <FaTrash className="ml-2" />
          <span>مسح البيانات</span>
        </button>
      </div>
    </div>
  );
};

export default AuthDebug;
