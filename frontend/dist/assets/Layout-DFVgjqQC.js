import{a as y,u as h,d as j,r as p,j as e,L as b}from"./index-B6Ptcst5.js";import{I as u,J as f,F as i,a as d,K as c,L as x}from"./index-CD3RUED3.js";const v=({children:m})=>{const o=y(),{logout:g,user:a}=h(),{toggleTheme:t,currentTheme:s}=j(),[r,l]=p.useState(!1),n=()=>{g(),o("/login")};return e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col",children:[e.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-soft sticky top-0 z-10",children:e.jsxs("div",{className:"container mx-auto px-4 sm:px-6 py-3",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"flex items-center",children:e.jsx(b,{to:"/",className:"flex items-center",children:e.jsxs("span",{className:"text-xl sm:text-2xl font-bold text-primary-600",children:["Smart",e.jsx("span",{className:"text-secondary-800 dark:text-secondary-200",children:"POS"})]})})}),e.jsx("div",{className:"md:hidden",children:e.jsx("button",{onClick:()=>l(!r),className:"p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors","aria-label":r?"إغلاق القائمة":"فتح القائمة",children:r?e.jsx(u,{}):e.jsx(f,{})})}),e.jsxs("div",{className:"hidden md:flex items-center gap-3",children:[e.jsx("button",{onClick:t,className:"p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors","aria-label":s==="dark"?"التبديل إلى الوضع المضيء":"التبديل إلى الوضع المظلم",children:s==="dark"?e.jsx(i,{className:"text-yellow-400"}):e.jsx(d,{className:"text-gray-700"})}),e.jsxs("div",{className:"bg-secondary-50 dark:bg-secondary-900 px-4 py-2 rounded-lg text-secondary-800 dark:text-secondary-100 font-medium flex items-center",children:[e.jsx(c,{className:"ml-2 text-primary-600 dark:text-primary-400"}),e.jsx("span",{children:a==null?void 0:a.full_name})]}),e.jsxs("button",{onClick:n,className:"btn-primary flex items-center py-2",children:[e.jsx(x,{className:"ml-2"}),e.jsx("span",{children:"تسجيل الخروج"})]})]})]}),r&&e.jsx("div",{className:"md:hidden mt-3 pt-3 border-t border-gray-200 dark:border-gray-700 animate-slideDown",children:e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("div",{className:"flex items-center justify-between animate-slideUp",style:{animationDelay:"0.05s"},children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(c,{className:"ml-2 text-primary-600 dark:text-primary-400"}),e.jsx("span",{className:"text-secondary-800 dark:text-secondary-100 font-medium truncate max-w-[200px]",children:a==null?void 0:a.full_name})]}),e.jsx("button",{onClick:t,className:"p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors","aria-label":s==="dark"?"التبديل إلى الوضع المضيء":"التبديل إلى الوضع المظلم",children:s==="dark"?e.jsx(i,{className:"text-yellow-400"}):e.jsx(d,{className:"text-gray-700"})})]}),e.jsxs("button",{onClick:()=>{n(),l(!1)},className:"btn-primary flex items-center justify-center py-2 w-full animate-slideUp",style:{animationDelay:"0.1s"},children:[e.jsx(x,{className:"ml-2"}),e.jsx("span",{children:"تسجيل الخروج"})]})]})})]})}),e.jsx("main",{className:"flex-1",children:m})]})};export{v as default};
