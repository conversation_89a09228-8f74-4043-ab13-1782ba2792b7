import{a as Pe,u as _e,r as l,b as k,j as e}from"./index-CQPlkjPn.js";import{O as Le,v as I,o as De,B as se,g as F,k as te,j as le,s as de,a0 as Oe,X as ne,at as ie,i as $e,C as oe,T as Te,a1 as Ae,a2 as Re,au as Ie,av as ze}from"./index-DLBLB0ar.js";import{M as Ue}from"./Modal-CihbLs4Y.js";import{D as Be,S as Ke}from"./SuccessModal-CJ-7h7ww.js";import{T as z,a as ce}from"./BarcodeInput-CNm480_4.js";import{S as Ve}from"./SelectBox-DVQ73Klv.js";import{T as U}from"./ToggleSwitch-rU-7sv-p.js";const Ye=()=>{const v=Pe(),{user:N,isAuthenticated:M,isInitialized:w}=_e(),[xe,B]=l.useState([]),[h,K]=l.useState(!0),[V,W]=l.useState(""),[q,E]=l.useState(!1),[Q,P]=l.useState(!1),[me,u]=l.useState(!1),[ge,_]=l.useState(!1),[he,L]=l.useState(!1),[pe,X]=l.useState(""),[x,G]=l.useState(null),[p,ye]=l.useState(null),[D,be]=l.useState(!1),[o,H]=l.useState(10),[ue,m]=l.useState(1),[a,fe]=l.useState({total:0,page:1,limit:10,pages:1}),[d,c]=l.useState({name:"",phone:"",email:"",address:"",notes:"",is_active:!0}),[y,f]=l.useState({}),[O,b]=l.useState(null),[C,J]=l.useState({totalCustomers:0,activeCustomers:0,totalDebts:0,totalPaidSales:0});l.useEffect(()=>{if(console.log("Auth state:",{user:N,isAuthenticated:M,isInitialized:w}),!w){console.log("Auth not initialized yet, waiting...");return}if(!M||!N){console.log("User not authenticated, redirecting to login"),v("/login");return}console.log("User authenticated:",N),n(),je()},[N,M,w,v]);const je=async()=>{try{console.log("Fetching customer stats...");const r=await k.get("/api/customers/stats");console.log("Customer stats response:",r.data),J(r.data),console.log("Stats state updated:",r.data)}catch(r){console.error("Error fetching customer stats:",r),J({totalCustomers:0,activeCustomers:0,totalDebts:0,totalPaidSales:0})}},n=async(r=ue,t=o,s=null,i=null,Z=null)=>{var ee;try{K(!0),b(null),console.log("Fetching customers...");const g=s!==null?s:q,re=i!==null?i:Q,Fe=Z!==null?Z:V;let T;g&&!re?T=!0:re&&!g&&(T=!1);const S=await k.get("/api/customers",{params:{active_only:T,search:Fe,skip:(r-1)*t,limit:t}});console.log("Customers fetched successfully:",S.data.length,"customers");let A=S.data,R;if(S.headers["x-total-count"]){const j=parseInt(S.headers["x-total-count"]);R={total:j,page:r,limit:t,pages:Math.ceil(j/t)},B(A)}else{const j=A.length,ae=(r-1)*t,Me=ae+t,Ee=A.slice(ae,Me);R={total:j,page:r,limit:t,pages:Math.ceil(j/t)},B(Ee)}fe(R),m(r),b(null)}catch(g){console.error("Error fetching customers:",g),((ee=g.response)==null?void 0:ee.status)===401?(console.log("401 error during fetch - auth will be handled by axios interceptor"),b("انتهت صلاحية الجلسة. يتم إعادة التوجيه لتسجيل الدخول...")):g.code==="NETWORK_ERROR"||g.message.includes("Network Error")?b("لا يمكن الاتصال بالخادم. تأكد من تشغيل الخادم."):b("حدث خطأ غير متوقع")}finally{K(!1)}},Y=()=>{n()},ke=()=>{G(null),c({name:"",phone:"",email:"",address:"",notes:"",is_active:!0}),f({}),u(!0)},ve=r=>{G(r),c({name:r.name,phone:r.phone||"",email:r.email||"",address:r.address||"",notes:r.notes||"",is_active:r.is_active}),f({}),u(!0)},Ne=r=>{ye(r),_(!0)},we=()=>{const r={};return d.name.trim()||(r.name="اسم العميل مطلوب"),d.email&&!d.email.includes("@")&&(r.email="البريد الإلكتروني غير صحيح"),f(r),Object.keys(r).length===0},Ce=async r=>{var t,s;if(r.preventDefault(),!!we())try{if(console.log("Starting customer save process..."),console.log("Customer data:",d),x){console.log("Updating existing customer:",x.id);const i=await k.put(`/api/customers/${x.id}`,d);console.log("Customer updated successfully:",i.data)}else{console.log("Creating new customer");const i=await k.post("/api/customers",d);console.log("Customer created successfully:",i.data)}u(!1),X(x?"تم تحديث العميل بنجاح":"تم إضافة العميل بنجاح"),L(!0),n()}catch(i){console.error("Error saving customer:",i),((t=i.response)==null?void 0:t.status)===401?console.log("401 error during save - auth will be handled by axios interceptor"):((s=i.response)==null?void 0:s.status)===400?console.error("Validation error:",i.response.data):console.error("Unexpected error:",i.message)}},Se=async()=>{var r,t;if(p)try{console.log("Starting customer delete process..."),console.log("Deleting customer:",p.id);const s=await k.delete(`/api/customers/${p.id}`);console.log("Customer deleted successfully:",s.data),_(!1),X("تم حذف العميل بنجاح"),L(!0),n()}catch(s){console.error("Error deleting customer:",s),((r=s.response)==null?void 0:r.status)===401?console.log("401 error during delete - auth will be handled by axios interceptor"):((t=s.response)==null?void 0:t.status)===400?console.error("Cannot delete customer:",s.response.data):console.error("Unexpected error:",s.message)}},$=xe;return w?h?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"جاري تحميل العملاء..."})]})}):e.jsxs("div",{className:"container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx("button",{onClick:()=>v("/"),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2.5 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm flex-shrink-0",title:"العودة للرئيسية",children:e.jsx(Le,{className:"text-sm"})}),e.jsxs("div",{className:"mr-3 sm:mr-4 min-w-0 flex-1",children:[e.jsxs("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center",children:[e.jsx(I,{className:"ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:"إدارة العملاء"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block",children:"إدارة وتتبع بيانات العملاء"})]})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap",children:[e.jsx("button",{onClick:()=>n(),className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"تحديث البيانات",children:e.jsx(De,{className:`text-sm ${h?"animate-spin":""}`})}),e.jsxs("button",{onClick:ke,className:"bg-primary-600 text-white rounded-lg py-2.5 px-3 sm:px-4 lg:px-5 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg font-medium",children:[e.jsx(se,{className:"ml-2 text-sm"}),e.jsx("span",{className:"hidden sm:inline lg:inline",children:"إضافة عميل جديد"}),e.jsx("span",{className:"sm:hidden lg:hidden",children:"إضافة"})]})]})]})})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(I,{className:"text-primary-600 dark:text-primary-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-primary-700 dark:text-primary-300 mb-1",children:"إجمالي العملاء"}),e.jsx("div",{className:"text-xl font-bold text-primary-600 dark:text-primary-400",children:C.totalCustomers})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30",children:[e.jsx("div",{className:"bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(F,{className:"text-success-600 dark:text-success-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-success-700 dark:text-success-300 mb-1",children:"العملاء النشطين"}),e.jsx("div",{className:"text-xl font-bold text-success-600 dark:text-success-400",children:C.activeCustomers})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg border border-danger-100 dark:border-danger-800/30",children:[e.jsx("div",{className:"bg-danger-100 dark:bg-danger-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(te,{className:"text-danger-600 dark:text-danger-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-danger-700 dark:text-danger-300 mb-1",children:"إجمالي الديون"}),e.jsxs("div",{className:"text-lg font-bold text-danger-600 dark:text-danger-400 truncate",children:[C.totalDebts.toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-danger-500 dark:text-danger-400",children:"المبالغ المستحقة"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30",children:[e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(le,{className:"text-blue-600 dark:text-blue-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-blue-700 dark:text-blue-300 mb-1",children:"المبيعات المدفوعة"}),e.jsxs("div",{className:"text-lg font-bold text-blue-600 dark:text-blue-400 truncate",children:[C.totalPaidSales.toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-blue-500 dark:text-blue-400",children:"المبالغ المدفوعة فعلياً"})]})]})]})}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(de,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"}),e.jsx("input",{type:"text",placeholder:"البحث بالاسم أو الهاتف أو البريد الإلكتروني...",value:V,onChange:r=>W(r.target.value),onKeyDown:r=>r.key==="Enter"&&Y(),className:"w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"})]})}),e.jsxs("div",{className:"flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>be(!D),className:`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${D?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(Oe,{className:"ml-2"}),"فلاتر"]}),e.jsxs("button",{onClick:Y,className:"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 hover:bg-primary-200 dark:hover:bg-primary-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center",children:[e.jsx(de,{className:"ml-2"}),"بحث"]})]})]}),D&&e.jsx("div",{className:"mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(Ve,{label:"عدد العناصر",name:"itemsPerPage",value:o.toString(),onChange:r=>{const t=parseInt(r,10);H(t)},options:[{value:"10",label:"10"},{value:"20",label:"20"},{value:"30",label:"30"},{value:"50",label:"50"}]})}),e.jsxs("div",{className:"sm:col-span-1 lg:col-span-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"العملاء النشطين"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center",children:e.jsx(U,{id:"activeOnly",checked:q,onChange:r=>{E(r),r&&P(!1)},label:"النشطين فقط"})})]}),e.jsxs("div",{className:"sm:col-span-1 lg:col-span-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"العملاء غير النشطين"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center",children:e.jsx(U,{id:"inactiveOnly",checked:Q,onChange:r=>{P(r),r&&E(!1)},label:"غير النشطين فقط"})})]}),e.jsxs("div",{className:"sm:col-span-2 lg:col-span-1 flex flex-col gap-2",children:[e.jsx("button",{type:"button",onClick:()=>{W(""),E(!1),P(!1),H(10),m(1),n(1,10,!1,!1,"")},className:"bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm",children:"إعادة تعيين"}),e.jsx("button",{type:"button",onClick:()=>{n(1,o)},className:"bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm",children:"تطبيق الفلاتر"})]})]})})]}),O&&e.jsx("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"mr-3 flex-1",children:[e.jsx("p",{className:"text-sm text-red-800 dark:text-red-200",children:O}),O.includes("لا يمكن الاتصال بالخادم")&&e.jsx("button",{onClick:()=>n(),className:"mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline",children:"إعادة المحاولة"})]}),e.jsx("div",{className:"mr-auto",children:e.jsx("button",{onClick:()=>b(null),className:"text-red-400 hover:text-red-600 dark:hover:text-red-300",children:e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden border border-gray-200 dark:border-gray-700",children:[$.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(I,{className:"mx-auto text-4xl text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"لا توجد عملاء"})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-700",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"العميل"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"معلومات الاتصال"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المديونية"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المبيعات المدفوعة"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الحالة"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الإجراءات"})]})}),e.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:$.map(r=>e.jsxs("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.jsx("div",{className:"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center",children:e.jsx(F,{className:"text-primary-600 dark:text-primary-400"})})}),e.jsxs("div",{className:"mr-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:r.name}),e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["#",r.id]})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:e.jsxs("div",{className:"space-y-1",children:[r.phone&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(ne,{className:"text-gray-400 ml-2"}),r.phone]}),r.email&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(ie,{className:"text-gray-400 ml-2"}),r.email]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(te,{className:`ml-2 ${r.total_debt>0?"text-red-500":"text-green-500"}`}),e.jsxs("span",{className:`text-sm font-medium ${r.total_debt>0?"text-red-600 dark:text-red-400":"text-green-600 dark:text-green-400"}`,children:[r.total_debt.toFixed(2)," د.ل"]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(le,{className:"text-blue-500 ml-2"}),e.jsxs("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:[r.total_sales.toFixed(2)," د.ل"]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${r.is_active?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"}`,children:r.is_active?"نشط":"غير نشط"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>v(`/debts?customer_id=${r.id}`),className:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300",title:"عرض المديونية",children:e.jsx($e,{})}),e.jsx("button",{onClick:()=>ve(r),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300",title:"تعديل",children:e.jsx(oe,{})}),e.jsx("button",{onClick:()=>Ne(r),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300",title:"حذف",children:e.jsx(Te,{})})]})})]},r.id))})]})}),!h&&$.length>0&&e.jsxs("div",{className:"px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex-1 flex justify-between items-center sm:hidden",children:[e.jsx("button",{onClick:()=>{const r=Math.max(1,a.page-1);m(r),n(r,o)},disabled:a.page<=1,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${a.page<=1?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:"السابق"}),e.jsx("button",{onClick:()=>{const r=Math.min(a.pages,a.page+1);m(r),n(r,o)},disabled:a.page>=a.pages,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${a.page>=a.pages?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:"التالي"})]}),e.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:[e.jsx("span",{className:"font-medium",children:a.page})," / ",e.jsx("span",{className:"font-medium",children:a.pages})," ",e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["(الإجمالي: ",a.total,")"]})]})}),e.jsx("div",{children:e.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse","aria-label":"Pagination",children:[e.jsxs("button",{onClick:()=>{const r=Math.max(1,a.page-1);m(r),n(r,o)},disabled:a.page<=1,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${a.page<=1?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"sr-only",children:"السابق"}),e.jsx(Ae,{className:"h-5 w-5"})]}),Array.from({length:Math.min(5,a.pages)},(r,t)=>{let s;return a.pages<=5||a.page<=3?s=t+1:a.page>=a.pages-2?s=a.pages-4+t:s=a.page-2+t,e.jsx("button",{onClick:()=>{m(s),n(s,o)},className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${a.page===s?"z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:s},s)}),e.jsxs("button",{onClick:()=>{const r=Math.min(a.pages,a.page+1);m(r),n(r,o)},disabled:a.page>=a.pages,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${a.page>=a.pages?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"sr-only",children:"التالي"}),e.jsx(Re,{className:"h-5 w-5"})]})]})})]})]})]}),e.jsx(Ue,{isOpen:me,onClose:()=>u(!1),title:x?"تعديل العميل":"إضافة عميل جديد",size:"lg",children:e.jsxs("form",{onSubmit:Ce,className:"space-y-5",children:[e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[e.jsx(F,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"المعلومات الأساسية"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsx(z,{label:"اسم العميل",name:"name",value:d.name,onChange:r=>{c({...d,name:r}),y.name&&f({...y,name:""})},placeholder:"أدخل اسم العميل",required:!0,error:y.name,icon:e.jsx(F,{}),maxLength:100}),e.jsx(z,{label:"رقم الهاتف",name:"phone",type:"tel",value:d.phone,onChange:r=>c({...d,phone:r}),placeholder:"أدخل رقم الهاتف",icon:e.jsx(ne,{}),maxLength:20})]}),e.jsx("div",{className:"mt-4",children:e.jsx(z,{label:"البريد الإلكتروني",name:"email",type:"email",value:d.email,onChange:r=>{c({...d,email:r}),y.email&&f({...y,email:""})},placeholder:"أدخل البريد الإلكتروني (اختياري)",error:y.email,icon:e.jsx(ie,{}),maxLength:100})})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[e.jsx(Ie,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"معلومات إضافية"]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(ce,{label:"العنوان",name:"address",value:d.address,onChange:r=>c({...d,address:r}),placeholder:"أدخل العنوان (اختياري)",rows:2,maxLength:200}),e.jsx(ce,{label:"ملاحظات",name:"notes",value:d.notes,onChange:r=>c({...d,notes:r}),placeholder:"أدخل ملاحظات إضافية (اختياري)",rows:2,maxLength:500})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center",children:[e.jsx(ze,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"حالة العميل"]}),e.jsx(U,{id:"is_active",checked:d.is_active,onChange:r=>c({...d,is_active:r}),label:"عميل نشط"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"تحديد ما إذا كان العميل نشط أم لا"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsx("button",{type:"button",onClick:()=>u(!1),disabled:h,className:"btn-secondary flex items-center justify-center min-w-[120px]",children:e.jsx("span",{children:"إلغاء"})}),e.jsx("button",{type:"submit",disabled:h,className:"btn-primary flex items-center justify-center min-w-[140px]",children:h?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),e.jsx("span",{children:"جاري الحفظ..."})]}):e.jsxs(e.Fragment,{children:[x?e.jsx(oe,{className:"ml-2"}):e.jsx(se,{className:"ml-2"}),e.jsx("span",{children:x?"تحديث العميل":"إضافة العميل"})]})})]})]})}),e.jsx(Be,{isOpen:ge,onClose:()=>_(!1),onConfirm:Se,title:"تأكيد الحذف",message:"هل أنت متأكد من حذف العميل؟",itemName:(p==null?void 0:p.name)||""}),e.jsx(Ke,{isOpen:he,onClose:()=>L(!1),title:"نجح العملية",message:pe})]}):e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"جاري التحقق من الجلسة..."})]})})};export{Ye as default};
