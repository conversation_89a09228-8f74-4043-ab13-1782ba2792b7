import{f as Ft,a as Dt,r as R,j as i,b as z}from"./index-CQPlkjPn.js";import{O as pt,e as tt,D as kt,an as Ut}from"./index-DLBLB0ar.js";var K={},$t=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},Nt={},S={};let dt;const zt=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];S.getSymbolSize=function(e){if(!e)throw new Error('"version" cannot be null or undefined');if(e<1||e>40)throw new Error('"version" should be in range from 1 to 40');return e*4+17};S.getSymbolTotalCodewords=function(e){return zt[e]};S.getBCHDigit=function(r){let e=0;for(;r!==0;)e++,r>>>=1;return e};S.setToSJISFunction=function(e){if(typeof e!="function")throw new Error('"toSJISFunc" is not a valid function.');dt=e};S.isKanjiModeEnabled=function(){return typeof dt<"u"};S.toSJIS=function(e){return dt(e)};var G={};(function(r){r.L={bit:1},r.M={bit:0},r.Q={bit:3},r.H={bit:2};function e(t){if(typeof t!="string")throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return r.L;case"m":case"medium":return r.M;case"q":case"quartile":return r.Q;case"h":case"high":return r.H;default:throw new Error("Unknown EC Level: "+t)}}r.isValid=function(s){return s&&typeof s.bit<"u"&&s.bit>=0&&s.bit<4},r.from=function(s,n){if(r.isValid(s))return s;try{return e(s)}catch{return n}}})(G);function bt(){this.buffer=[],this.length=0}bt.prototype={get:function(r){const e=Math.floor(r/8);return(this.buffer[e]>>>7-r%8&1)===1},put:function(r,e){for(let t=0;t<e;t++)this.putBit((r>>>e-t-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(r){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),r&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var Vt=bt;function O(r){if(!r||r<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=r,this.data=new Uint8Array(r*r),this.reservedBit=new Uint8Array(r*r)}O.prototype.set=function(r,e,t,s){const n=r*this.size+e;this.data[n]=t,s&&(this.reservedBit[n]=!0)};O.prototype.get=function(r,e){return this.data[r*this.size+e]};O.prototype.xor=function(r,e,t){this.data[r*this.size+e]^=t};O.prototype.isReserved=function(r,e){return this.reservedBit[r*this.size+e]};var Ht=O,Et={};(function(r){const e=S.getSymbolSize;r.getRowColCoords=function(s){if(s===1)return[];const n=Math.floor(s/7)+2,o=e(s),a=o===145?26:Math.ceil((o-13)/(2*n-2))*2,l=[o-7];for(let c=1;c<n-1;c++)l[c]=l[c-1]-a;return l.push(6),l.reverse()},r.getPositions=function(s){const n=[],o=r.getRowColCoords(s),a=o.length;for(let l=0;l<a;l++)for(let c=0;c<a;c++)l===0&&c===0||l===0&&c===a-1||l===a-1&&c===0||n.push([o[l],o[c]]);return n}})(Et);var Ct={};const Kt=S.getSymbolSize,yt=7;Ct.getPositions=function(e){const t=Kt(e);return[[0,0],[t-yt,0],[0,t-yt]]};var jt={};(function(r){r.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e={N1:3,N2:3,N3:40,N4:10};r.isValid=function(n){return n!=null&&n!==""&&!isNaN(n)&&n>=0&&n<=7},r.from=function(n){return r.isValid(n)?parseInt(n,10):void 0},r.getPenaltyN1=function(n){const o=n.size;let a=0,l=0,c=0,f=null,u=null;for(let C=0;C<o;C++){l=c=0,f=u=null;for(let y=0;y<o;y++){let p=n.get(C,y);p===f?l++:(l>=5&&(a+=e.N1+(l-5)),f=p,l=1),p=n.get(y,C),p===u?c++:(c>=5&&(a+=e.N1+(c-5)),u=p,c=1)}l>=5&&(a+=e.N1+(l-5)),c>=5&&(a+=e.N1+(c-5))}return a},r.getPenaltyN2=function(n){const o=n.size;let a=0;for(let l=0;l<o-1;l++)for(let c=0;c<o-1;c++){const f=n.get(l,c)+n.get(l,c+1)+n.get(l+1,c)+n.get(l+1,c+1);(f===4||f===0)&&a++}return a*e.N2},r.getPenaltyN3=function(n){const o=n.size;let a=0,l=0,c=0;for(let f=0;f<o;f++){l=c=0;for(let u=0;u<o;u++)l=l<<1&2047|n.get(f,u),u>=10&&(l===1488||l===93)&&a++,c=c<<1&2047|n.get(u,f),u>=10&&(c===1488||c===93)&&a++}return a*e.N3},r.getPenaltyN4=function(n){let o=0;const a=n.data.length;for(let c=0;c<a;c++)o+=n.data[c];return Math.abs(Math.ceil(o*100/a/5)-10)*e.N4};function t(s,n,o){switch(s){case r.Patterns.PATTERN000:return(n+o)%2===0;case r.Patterns.PATTERN001:return n%2===0;case r.Patterns.PATTERN010:return o%3===0;case r.Patterns.PATTERN011:return(n+o)%3===0;case r.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(o/3))%2===0;case r.Patterns.PATTERN101:return n*o%2+n*o%3===0;case r.Patterns.PATTERN110:return(n*o%2+n*o%3)%2===0;case r.Patterns.PATTERN111:return(n*o%3+(n+o)%2)%2===0;default:throw new Error("bad maskPattern:"+s)}}r.applyMask=function(n,o){const a=o.size;for(let l=0;l<a;l++)for(let c=0;c<a;c++)o.isReserved(c,l)||o.xor(c,l,t(n,c,l))},r.getBestMask=function(n,o){const a=Object.keys(r.Patterns).length;let l=0,c=1/0;for(let f=0;f<a;f++){o(f),r.applyMask(f,n);const u=r.getPenaltyN1(n)+r.getPenaltyN2(n)+r.getPenaltyN3(n)+r.getPenaltyN4(n);r.applyMask(f,n),u<c&&(c=u,l=f)}return l}})(jt);var W={};const v=G,J=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],Y=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];W.getBlocksCount=function(e,t){switch(t){case v.L:return J[(e-1)*4+0];case v.M:return J[(e-1)*4+1];case v.Q:return J[(e-1)*4+2];case v.H:return J[(e-1)*4+3];default:return}};W.getTotalCodewordsCount=function(e,t){switch(t){case v.L:return Y[(e-1)*4+0];case v.M:return Y[(e-1)*4+1];case v.Q:return Y[(e-1)*4+2];case v.H:return Y[(e-1)*4+3];default:return}};var _t={},Z={};const V=new Uint8Array(512),q=new Uint8Array(256);(function(){let e=1;for(let t=0;t<255;t++)V[t]=e,q[e]=t,e<<=1,e&256&&(e^=285);for(let t=255;t<512;t++)V[t]=V[t-255]})();Z.log=function(e){if(e<1)throw new Error("log("+e+")");return q[e]};Z.exp=function(e){return V[e]};Z.mul=function(e,t){return e===0||t===0?0:V[q[e]+q[t]]};(function(r){const e=Z;r.mul=function(s,n){const o=new Uint8Array(s.length+n.length-1);for(let a=0;a<s.length;a++)for(let l=0;l<n.length;l++)o[a+l]^=e.mul(s[a],n[l]);return o},r.mod=function(s,n){let o=new Uint8Array(s);for(;o.length-n.length>=0;){const a=o[0];for(let c=0;c<n.length;c++)o[c]^=e.mul(n[c],a);let l=0;for(;l<o.length&&o[l]===0;)l++;o=o.slice(l)}return o},r.generateECPolynomial=function(s){let n=new Uint8Array([1]);for(let o=0;o<s;o++)n=r.mul(n,new Uint8Array([1,e.exp(o)]));return n}})(_t);const At=_t;function ut(r){this.genPoly=void 0,this.degree=r,this.degree&&this.initialize(this.degree)}ut.prototype.initialize=function(e){this.degree=e,this.genPoly=At.generateECPolynomial(this.degree)};ut.prototype.encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");const t=new Uint8Array(e.length+this.degree);t.set(e);const s=At.mod(t,this.genPoly),n=this.degree-s.length;if(n>0){const o=new Uint8Array(this.degree);return o.set(s,n),o}return s};var Ot=ut,Bt={},L={},ht={};ht.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40};var P={};const St="[0-9]+",Jt="[A-Z $%*+\\-./:]+";let H="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";H=H.replace(/u/g,"\\u");const Yt="(?:(?![A-Z0-9 $%*+\\-./:]|"+H+`)(?:.|[\r
]))+`;P.KANJI=new RegExp(H,"g");P.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");P.BYTE=new RegExp(Yt,"g");P.NUMERIC=new RegExp(St,"g");P.ALPHANUMERIC=new RegExp(Jt,"g");const qt=new RegExp("^"+H+"$"),Qt=new RegExp("^"+St+"$"),Gt=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");P.testKanji=function(e){return qt.test(e)};P.testNumeric=function(e){return Qt.test(e)};P.testAlphanumeric=function(e){return Gt.test(e)};(function(r){const e=ht,t=P;r.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},r.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},r.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},r.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},r.MIXED={bit:-1},r.getCharCountIndicator=function(o,a){if(!o.ccBits)throw new Error("Invalid mode: "+o);if(!e.isValid(a))throw new Error("Invalid version: "+a);return a>=1&&a<10?o.ccBits[0]:a<27?o.ccBits[1]:o.ccBits[2]},r.getBestModeForData=function(o){return t.testNumeric(o)?r.NUMERIC:t.testAlphanumeric(o)?r.ALPHANUMERIC:t.testKanji(o)?r.KANJI:r.BYTE},r.toString=function(o){if(o&&o.id)return o.id;throw new Error("Invalid mode")},r.isValid=function(o){return o&&o.bit&&o.ccBits};function s(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"numeric":return r.NUMERIC;case"alphanumeric":return r.ALPHANUMERIC;case"kanji":return r.KANJI;case"byte":return r.BYTE;default:throw new Error("Unknown mode: "+n)}}r.from=function(o,a){if(r.isValid(o))return o;try{return s(o)}catch{return a}}})(L);(function(r){const e=S,t=W,s=G,n=L,o=ht,a=7973,l=e.getBCHDigit(a);function c(y,p,x){for(let N=1;N<=40;N++)if(p<=r.getCapacity(N,x,y))return N}function f(y,p){return n.getCharCountIndicator(y,p)+4}function u(y,p){let x=0;return y.forEach(function(N){const _=f(N.mode,p);x+=_+N.getBitsLength()}),x}function C(y,p){for(let x=1;x<=40;x++)if(u(y,x)<=r.getCapacity(x,p,n.MIXED))return x}r.from=function(p,x){return o.isValid(p)?parseInt(p,10):x},r.getCapacity=function(p,x,N){if(!o.isValid(p))throw new Error("Invalid QR Code version");typeof N>"u"&&(N=n.BYTE);const _=e.getSymbolTotalCodewords(p),h=t.getTotalCodewordsCount(p,x),g=(_-h)*8;if(N===n.MIXED)return g;const m=g-f(N,p);switch(N){case n.NUMERIC:return Math.floor(m/10*3);case n.ALPHANUMERIC:return Math.floor(m/11*2);case n.KANJI:return Math.floor(m/13);case n.BYTE:default:return Math.floor(m/8)}},r.getBestVersionForData=function(p,x){let N;const _=s.from(x,s.M);if(Array.isArray(p)){if(p.length>1)return C(p,_);if(p.length===0)return 1;N=p[0]}else N=p;return c(N.mode,N.getLength(),_)},r.getEncodedBits=function(p){if(!o.isValid(p)||p<7)throw new Error("Invalid QR Code version");let x=p<<12;for(;e.getBCHDigit(x)-l>=0;)x^=a<<e.getBCHDigit(x)-l;return p<<12|x}})(Bt);var Tt={};const it=S,It=1335,Wt=21522,wt=it.getBCHDigit(It);Tt.getEncodedBits=function(e,t){const s=e.bit<<3|t;let n=s<<10;for(;it.getBCHDigit(n)-wt>=0;)n^=It<<it.getBCHDigit(n)-wt;return(s<<10|n)^Wt};var Pt={};const Zt=L;function D(r){this.mode=Zt.NUMERIC,this.data=r.toString()}D.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)};D.prototype.getLength=function(){return this.data.length};D.prototype.getBitsLength=function(){return D.getBitsLength(this.data.length)};D.prototype.write=function(e){let t,s,n;for(t=0;t+3<=this.data.length;t+=3)s=this.data.substr(t,3),n=parseInt(s,10),e.put(n,10);const o=this.data.length-t;o>0&&(s=this.data.substr(t),n=parseInt(s,10),e.put(n,o*3+1))};var Xt=D;const te=L,et=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function k(r){this.mode=te.ALPHANUMERIC,this.data=r}k.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)};k.prototype.getLength=function(){return this.data.length};k.prototype.getBitsLength=function(){return k.getBitsLength(this.data.length)};k.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let s=et.indexOf(this.data[t])*45;s+=et.indexOf(this.data[t+1]),e.put(s,11)}this.data.length%2&&e.put(et.indexOf(this.data[t]),6)};var ee=k;const ne=L;function U(r){this.mode=ne.BYTE,typeof r=="string"?this.data=new TextEncoder().encode(r):this.data=new Uint8Array(r)}U.getBitsLength=function(e){return e*8};U.prototype.getLength=function(){return this.data.length};U.prototype.getBitsLength=function(){return U.getBitsLength(this.data.length)};U.prototype.write=function(r){for(let e=0,t=this.data.length;e<t;e++)r.put(this.data[e],8)};var re=U;const se=L,oe=S;function $(r){this.mode=se.KANJI,this.data=r}$.getBitsLength=function(e){return e*13};$.prototype.getLength=function(){return this.data.length};$.prototype.getBitsLength=function(){return $.getBitsLength(this.data.length)};$.prototype.write=function(r){let e;for(e=0;e<this.data.length;e++){let t=oe.toSJIS(this.data[e]);if(t>=33088&&t<=40956)t-=33088;else if(t>=57408&&t<=60351)t-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);t=(t>>>8&255)*192+(t&255),r.put(t,13)}};var ie=$,Mt={exports:{}};(function(r){var e={single_source_shortest_paths:function(t,s,n){var o={},a={};a[s]=0;var l=e.PriorityQueue.make();l.push(s,0);for(var c,f,u,C,y,p,x,N,_;!l.empty();){c=l.pop(),f=c.value,C=c.cost,y=t[f]||{};for(u in y)y.hasOwnProperty(u)&&(p=y[u],x=C+p,N=a[u],_=typeof a[u]>"u",(_||N>x)&&(a[u]=x,l.push(u,x),o[u]=f))}if(typeof n<"u"&&typeof a[n]>"u"){var h=["Could not find a path from ",s," to ",n,"."].join("");throw new Error(h)}return o},extract_shortest_path_from_predecessor_list:function(t,s){for(var n=[],o=s;o;)n.push(o),t[o],o=t[o];return n.reverse(),n},find_path:function(t,s,n){var o=e.single_source_shortest_paths(t,s,n);return e.extract_shortest_path_from_predecessor_list(o,n)},PriorityQueue:{make:function(t){var s=e.PriorityQueue,n={},o;t=t||{};for(o in s)s.hasOwnProperty(o)&&(n[o]=s[o]);return n.queue=[],n.sorter=t.sorter||s.default_sorter,n},default_sorter:function(t,s){return t.cost-s.cost},push:function(t,s){var n={value:t,cost:s};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};r.exports=e})(Mt);var ae=Mt.exports;(function(r){const e=L,t=Xt,s=ee,n=re,o=ie,a=P,l=S,c=ae;function f(h){return unescape(encodeURIComponent(h)).length}function u(h,g,m){const d=[];let w;for(;(w=h.exec(m))!==null;)d.push({data:w[0],index:w.index,mode:g,length:w[0].length});return d}function C(h){const g=u(a.NUMERIC,e.NUMERIC,h),m=u(a.ALPHANUMERIC,e.ALPHANUMERIC,h);let d,w;return l.isKanjiModeEnabled()?(d=u(a.BYTE,e.BYTE,h),w=u(a.KANJI,e.KANJI,h)):(d=u(a.BYTE_KANJI,e.BYTE,h),w=[]),g.concat(m,d,w).sort(function(E,A){return E.index-A.index}).map(function(E){return{data:E.data,mode:E.mode,length:E.length}})}function y(h,g){switch(g){case e.NUMERIC:return t.getBitsLength(h);case e.ALPHANUMERIC:return s.getBitsLength(h);case e.KANJI:return o.getBitsLength(h);case e.BYTE:return n.getBitsLength(h)}}function p(h){return h.reduce(function(g,m){const d=g.length-1>=0?g[g.length-1]:null;return d&&d.mode===m.mode?(g[g.length-1].data+=m.data,g):(g.push(m),g)},[])}function x(h){const g=[];for(let m=0;m<h.length;m++){const d=h[m];switch(d.mode){case e.NUMERIC:g.push([d,{data:d.data,mode:e.ALPHANUMERIC,length:d.length},{data:d.data,mode:e.BYTE,length:d.length}]);break;case e.ALPHANUMERIC:g.push([d,{data:d.data,mode:e.BYTE,length:d.length}]);break;case e.KANJI:g.push([d,{data:d.data,mode:e.BYTE,length:f(d.data)}]);break;case e.BYTE:g.push([{data:d.data,mode:e.BYTE,length:f(d.data)}])}}return g}function N(h,g){const m={},d={start:{}};let w=["start"];for(let b=0;b<h.length;b++){const E=h[b],A=[];for(let M=0;M<E.length;M++){const T=E[M],F=""+b+M;A.push(F),m[F]={node:T,lastCount:0},d[F]={};for(let j=0;j<w.length;j++){const B=w[j];m[B]&&m[B].node.mode===T.mode?(d[B][F]=y(m[B].lastCount+T.length,T.mode)-y(m[B].lastCount,T.mode),m[B].lastCount+=T.length):(m[B]&&(m[B].lastCount=T.length),d[B][F]=y(T.length,T.mode)+4+e.getCharCountIndicator(T.mode,g))}}w=A}for(let b=0;b<w.length;b++)d[w[b]].end=0;return{map:d,table:m}}function _(h,g){let m;const d=e.getBestModeForData(h);if(m=e.from(g,d),m!==e.BYTE&&m.bit<d.bit)throw new Error('"'+h+'" cannot be encoded with mode '+e.toString(m)+`.
 Suggested mode is: `+e.toString(d));switch(m===e.KANJI&&!l.isKanjiModeEnabled()&&(m=e.BYTE),m){case e.NUMERIC:return new t(h);case e.ALPHANUMERIC:return new s(h);case e.KANJI:return new o(h);case e.BYTE:return new n(h)}}r.fromArray=function(g){return g.reduce(function(m,d){return typeof d=="string"?m.push(_(d,null)):d.data&&m.push(_(d.data,d.mode)),m},[])},r.fromString=function(g,m){const d=C(g,l.isKanjiModeEnabled()),w=x(d),b=N(w,m),E=c.find_path(b.map,"start","end"),A=[];for(let M=1;M<E.length-1;M++)A.push(b.table[E[M]].node);return r.fromArray(p(A))},r.rawSplit=function(g){return r.fromArray(C(g,l.isKanjiModeEnabled()))}})(Pt);const X=S,nt=G,ce=Vt,le=Ht,de=Et,ue=Ct,at=jt,ct=W,he=Ot,Q=Bt,fe=Tt,ge=L,rt=Pt;function me(r,e){const t=r.size,s=ue.getPositions(e);for(let n=0;n<s.length;n++){const o=s[n][0],a=s[n][1];for(let l=-1;l<=7;l++)if(!(o+l<=-1||t<=o+l))for(let c=-1;c<=7;c++)a+c<=-1||t<=a+c||(l>=0&&l<=6&&(c===0||c===6)||c>=0&&c<=6&&(l===0||l===6)||l>=2&&l<=4&&c>=2&&c<=4?r.set(o+l,a+c,!0,!0):r.set(o+l,a+c,!1,!0))}}function pe(r){const e=r.size;for(let t=8;t<e-8;t++){const s=t%2===0;r.set(t,6,s,!0),r.set(6,t,s,!0)}}function ye(r,e){const t=de.getPositions(e);for(let s=0;s<t.length;s++){const n=t[s][0],o=t[s][1];for(let a=-2;a<=2;a++)for(let l=-2;l<=2;l++)a===-2||a===2||l===-2||l===2||a===0&&l===0?r.set(n+a,o+l,!0,!0):r.set(n+a,o+l,!1,!0)}}function we(r,e){const t=r.size,s=Q.getEncodedBits(e);let n,o,a;for(let l=0;l<18;l++)n=Math.floor(l/3),o=l%3+t-8-3,a=(s>>l&1)===1,r.set(n,o,a,!0),r.set(o,n,a,!0)}function st(r,e,t){const s=r.size,n=fe.getEncodedBits(e,t);let o,a;for(o=0;o<15;o++)a=(n>>o&1)===1,o<6?r.set(o,8,a,!0):o<8?r.set(o+1,8,a,!0):r.set(s-15+o,8,a,!0),o<8?r.set(8,s-o-1,a,!0):o<9?r.set(8,15-o-1+1,a,!0):r.set(8,15-o-1,a,!0);r.set(s-8,8,1,!0)}function xe(r,e){const t=r.size;let s=-1,n=t-1,o=7,a=0;for(let l=t-1;l>0;l-=2)for(l===6&&l--;;){for(let c=0;c<2;c++)if(!r.isReserved(n,l-c)){let f=!1;a<e.length&&(f=(e[a]>>>o&1)===1),r.set(n,l-c,f),o--,o===-1&&(a++,o=7)}if(n+=s,n<0||t<=n){n-=s,s=-s;break}}}function Ne(r,e,t){const s=new ce;t.forEach(function(c){s.put(c.mode.bit,4),s.put(c.getLength(),ge.getCharCountIndicator(c.mode,r)),c.write(s)});const n=X.getSymbolTotalCodewords(r),o=ct.getTotalCodewordsCount(r,e),a=(n-o)*8;for(s.getLengthInBits()+4<=a&&s.put(0,4);s.getLengthInBits()%8!==0;)s.putBit(0);const l=(a-s.getLengthInBits())/8;for(let c=0;c<l;c++)s.put(c%2?17:236,8);return be(s,r,e)}function be(r,e,t){const s=X.getSymbolTotalCodewords(e),n=ct.getTotalCodewordsCount(e,t),o=s-n,a=ct.getBlocksCount(e,t),l=s%a,c=a-l,f=Math.floor(s/a),u=Math.floor(o/a),C=u+1,y=f-u,p=new he(y);let x=0;const N=new Array(a),_=new Array(a);let h=0;const g=new Uint8Array(r.buffer);for(let E=0;E<a;E++){const A=E<c?u:C;N[E]=g.slice(x,x+A),_[E]=p.encode(N[E]),x+=A,h=Math.max(h,A)}const m=new Uint8Array(s);let d=0,w,b;for(w=0;w<h;w++)for(b=0;b<a;b++)w<N[b].length&&(m[d++]=N[b][w]);for(w=0;w<y;w++)for(b=0;b<a;b++)m[d++]=_[b][w];return m}function Ee(r,e,t,s){let n;if(Array.isArray(r))n=rt.fromArray(r);else if(typeof r=="string"){let f=e;if(!f){const u=rt.rawSplit(r);f=Q.getBestVersionForData(u,t)}n=rt.fromString(r,f||40)}else throw new Error("Invalid data");const o=Q.getBestVersionForData(n,t);if(!o)throw new Error("The amount of data is too big to be stored in a QR Code");if(!e)e=o;else if(e<o)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+o+`.
`);const a=Ne(e,t,n),l=X.getSymbolSize(e),c=new le(l);return me(c,e),pe(c),ye(c,e),st(c,t,0),e>=7&&we(c,e),xe(c,a),isNaN(s)&&(s=at.getBestMask(c,st.bind(null,c,t))),at.applyMask(s,c),st(c,t,s),{modules:c,version:e,errorCorrectionLevel:t,maskPattern:s,segments:n}}Nt.create=function(e,t){if(typeof e>"u"||e==="")throw new Error("No input text");let s=nt.M,n,o;return typeof t<"u"&&(s=nt.from(t.errorCorrectionLevel,nt.M),n=Q.from(t.version),o=at.from(t.maskPattern),t.toSJISFunc&&X.setToSJISFunction(t.toSJISFunc)),Ee(e,n,s,o)};var Rt={},ft={};(function(r){function e(t){if(typeof t=="number"&&(t=t.toString()),typeof t!="string")throw new Error("Color should be defined as hex string");let s=t.slice().replace("#","").split("");if(s.length<3||s.length===5||s.length>8)throw new Error("Invalid hex color: "+t);(s.length===3||s.length===4)&&(s=Array.prototype.concat.apply([],s.map(function(o){return[o,o]}))),s.length===6&&s.push("F","F");const n=parseInt(s.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:n&255,hex:"#"+s.slice(0,6).join("")}}r.getOptions=function(s){s||(s={}),s.color||(s.color={});const n=typeof s.margin>"u"||s.margin===null||s.margin<0?4:s.margin,o=s.width&&s.width>=21?s.width:void 0,a=s.scale||4;return{width:o,scale:o?4:a,margin:n,color:{dark:e(s.color.dark||"#000000ff"),light:e(s.color.light||"#ffffffff")},type:s.type,rendererOpts:s.rendererOpts||{}}},r.getScale=function(s,n){return n.width&&n.width>=s+n.margin*2?n.width/(s+n.margin*2):n.scale},r.getImageWidth=function(s,n){const o=r.getScale(s,n);return Math.floor((s+n.margin*2)*o)},r.qrToImageData=function(s,n,o){const a=n.modules.size,l=n.modules.data,c=r.getScale(a,o),f=Math.floor((a+o.margin*2)*c),u=o.margin*c,C=[o.color.light,o.color.dark];for(let y=0;y<f;y++)for(let p=0;p<f;p++){let x=(y*f+p)*4,N=o.color.light;if(y>=u&&p>=u&&y<f-u&&p<f-u){const _=Math.floor((y-u)/c),h=Math.floor((p-u)/c);N=C[l[_*a+h]?1:0]}s[x++]=N.r,s[x++]=N.g,s[x++]=N.b,s[x]=N.a}}})(ft);(function(r){const e=ft;function t(n,o,a){n.clearRect(0,0,o.width,o.height),o.style||(o.style={}),o.height=a,o.width=a,o.style.height=a+"px",o.style.width=a+"px"}function s(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}r.render=function(o,a,l){let c=l,f=a;typeof c>"u"&&(!a||!a.getContext)&&(c=a,a=void 0),a||(f=s()),c=e.getOptions(c);const u=e.getImageWidth(o.modules.size,c),C=f.getContext("2d"),y=C.createImageData(u,u);return e.qrToImageData(y.data,o,c),t(C,f,u),C.putImageData(y,0,0),f},r.renderToDataURL=function(o,a,l){let c=l;typeof c>"u"&&(!a||!a.getContext)&&(c=a,a=void 0),c||(c={});const f=r.render(o,a,c),u=c.type||"image/png",C=c.rendererOpts||{};return f.toDataURL(u,C.quality)}})(Rt);var vt={};const Ce=ft;function xt(r,e){const t=r.a/255,s=e+'="'+r.hex+'"';return t<1?s+" "+e+'-opacity="'+t.toFixed(2).slice(1)+'"':s}function ot(r,e,t){let s=r+e;return typeof t<"u"&&(s+=" "+t),s}function je(r,e,t){let s="",n=0,o=!1,a=0;for(let l=0;l<r.length;l++){const c=Math.floor(l%e),f=Math.floor(l/e);!c&&!o&&(o=!0),r[l]?(a++,l>0&&c>0&&r[l-1]||(s+=o?ot("M",c+t,.5+f+t):ot("m",n,0),n=0,o=!1),c+1<e&&r[l+1]||(s+=ot("h",a),a=0)):n++}return s}vt.render=function(e,t,s){const n=Ce.getOptions(t),o=e.modules.size,a=e.modules.data,l=o+n.margin*2,c=n.color.light.a?"<path "+xt(n.color.light,"fill")+' d="M0 0h'+l+"v"+l+'H0z"/>':"",f="<path "+xt(n.color.dark,"stroke")+' d="'+je(a,o,n.margin)+'"/>',u='viewBox="0 0 '+l+" "+l+'"',y='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+u+' shape-rendering="crispEdges">'+c+f+`</svg>
`;return typeof s=="function"&&s(null,y),y};const _e=$t,lt=Nt,Lt=Rt,Ae=vt;function gt(r,e,t,s,n){const o=[].slice.call(arguments,1),a=o.length,l=typeof o[a-1]=="function";if(!l&&!_e())throw new Error("Callback required as last argument");if(l){if(a<2)throw new Error("Too few arguments provided");a===2?(n=t,t=e,e=s=void 0):a===3&&(e.getContext&&typeof n>"u"?(n=s,s=void 0):(n=s,s=t,t=e,e=void 0))}else{if(a<1)throw new Error("Too few arguments provided");return a===1?(t=e,e=s=void 0):a===2&&!e.getContext&&(s=t,t=e,e=void 0),new Promise(function(c,f){try{const u=lt.create(t,s);c(r(u,e,s))}catch(u){f(u)}})}try{const c=lt.create(t,s);n(null,r(c,e,s))}catch(c){n(c)}}K.create=lt.create;K.toCanvas=gt.bind(null,Lt.render);K.toDataURL=gt.bind(null,Lt.renderToDataURL);K.toString=gt.bind(null,function(r,e,t){return Ae.render(r,t)});const Te=()=>{const{id:r}=Ft(),e=Dt(),[t,s]=R.useState(null),[n,o]=R.useState({name:"",address:"",phone:"",email:"",tax_rate:"0",receipt_header:"",receipt_footer:"",currency_symbol:"د.ل"}),[a,l]=R.useState(!0),[c,f]=R.useState(null),[u,C]=R.useState("medium"),[y,p]=R.useState("");R.useEffect(()=>{(async()=>{l(!0),f(null);try{const g=parseInt(r||"0");if(isNaN(g)||g<=0)throw new Error("رقم الفاتورة غير صالح");const d=(await z.get(`/api/sales/${g}`)).data;if(!d||!d.items||!Array.isArray(d.items))throw new Error("بيانات الفاتورة غير صالحة");const w=await Promise.all(d.items.map(async j=>{try{const B=await z.get(`/api/products/${j.product_id}`);return{...j,product_name:B.data.name||`منتج #${j.product_id}`,price:j.unit_price}}catch{return{...j,product_name:`منتج #${j.product_id}`,price:j.unit_price}}}));let b="الكاشير";if(d.user_id)try{const j=await z.get(`/api/users/${d.user_id}`);b=j.data.full_name||j.data.username||"الكاشير"}catch{console.log("Could not fetch user data, using default cashier name"),d.user&&d.user.full_name?b=d.user.full_name:d.user&&d.user.username&&(b=d.user.username)}else d.user&&(b=d.user.full_name||d.user.username||"الكاشير");let E=null;if(d.customer_id&&d.customer_id!==null)try{E=(await z.get(`/api/customers/${d.customer_id}`)).data}catch{console.log("Could not fetch customer data"),d.customer&&(E=d.customer)}else d.customer&&(E=d.customer);const A=d.discount_amount||0,T=d.total_amount-A+(d.tax_amount||0),F={...d,items:w,payment_amount:d.amount_paid||T,change_amount:0,cashier_name:b,customer:E};s(F),o({name:"Smart POS",address:"ليبيا، طرابلس",phone:"+218 91-234-5678",email:"<EMAIL>",tax_rate:"0",receipt_header:"شكراً لاختيارك Smart POS",receipt_footer:"نتمنى لك يوماً سعيداً",currency_symbol:"د.ل"});try{const j=await z.get("/api/settings");if(j.data&&Array.isArray(j.data)){const B=j.data,I={};B.forEach(mt=>{I[mt.key]=mt.value}),Object.keys(I).length>0&&o({name:I.store_name||"Smart POS",address:I.store_address||"ليبيا، طرابلس",phone:I.store_phone||"+218 91-234-5678",email:I.store_email||"<EMAIL>",tax_rate:I.tax_rate||"0",receipt_header:I.receipt_header||"شكراً لاختيارك Smart POS",receipt_footer:I.receipt_footer||"نتمنى لك يوماً سعيداً",currency_symbol:I.currency_symbol||"د.ل"})}}catch{console.log("Settings API not available, using default store info")}l(!1)}catch(g){console.error("Error fetching receipt data:",g),f("فشل في تحميل بيانات الفاتورة"),l(!1)}})()},[r]),R.useEffect(()=>{(async()=>{if(t&&n.phone)try{const g=t.discount_amount||0,d=t.total_amount-g+(t.tax_amount||0);let w=`هاتف المتجر: ${n.phone}
فاتورة رقم: ${t.id}
المبلغ النهائي: ${d.toFixed(2)} ${n.currency_symbol}`;if(t.customer&&t.customer.name!=="عميل مباشر"&&(w+=`
العميل: ${t.customer.name}`,t.customer.phone&&(w+=`
هاتف العميل: ${t.customer.phone}`)),t.amount_paid!==d){w+=`
المبلغ المدفوع: ${t.amount_paid.toFixed(2)} ${n.currency_symbol}`;const E=d-t.amount_paid;w+=`
المديونية: ${E.toFixed(2)} ${n.currency_symbol}`}g>0&&(w+=`
الخصم: ${g.toFixed(2)} ${n.currency_symbol}`),w+=`
التاريخ: ${N(t.created_at)}
الكاشير: ${t.cashier_name||"غير محدد"}`;const b=await K.toDataURL(w,{width:120,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});p(b)}catch(g){console.error("Error generating QR code:",g)}})()},[t,n]);const x=()=>{window.print()},N=h=>{const g=new Date(h),m=String(g.getDate()).padStart(2,"0"),d=String(g.getMonth()+1).padStart(2,"0"),w=g.getFullYear();return`${m}/${d}/${w}`},_=h=>new Date(h).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!1});return a?i.jsx("div",{className:"flex justify-center items-center h-screen",children:i.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"})}):c||!t?i.jsxs("div",{className:"container mx-auto px-4 py-6 flex flex-col items-center justify-center min-h-[70vh]",children:[i.jsxs("div",{className:"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4 max-w-md w-full text-center",children:[i.jsx("h2",{className:"text-xl font-bold mb-2",children:"عذراً!"}),i.jsx("p",{children:c||"حدث خطأ في تحميل الفاتورة"}),i.jsx("p",{className:"mt-2 text-sm",children:"يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى."})]}),i.jsxs("button",{onClick:()=>e(-1),className:"mt-4 flex items-center bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors",children:[i.jsx(pt,{className:"ml-2"})," العودة للصفحة السابقة"]})]}):i.jsxs("div",{className:"receipt-container",children:[i.jsxs("div",{className:"print-controls print:hidden container mx-auto px-4 py-6 mb-6",children:[i.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:i.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:i.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center p-6 gap-4",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx("button",{onClick:()=>e(-1),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md p-2 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm",children:i.jsx(pt,{})}),i.jsxs("h1",{className:"text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100 mr-4 flex items-center",children:[i.jsx(tt,{className:"ml-3 text-primary-600 dark:text-primary-400"}),"فاتورة رقم #",t.id]})]}),i.jsx("div",{className:"flex items-center gap-2 flex-wrap",children:i.jsxs("button",{onClick:x,className:"bg-primary-600 text-white rounded-md py-2 px-3 sm:px-4 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg",children:[i.jsx(kt,{className:"ml-2"}),i.jsx("span",{className:"hidden sm:inline",children:"طباعة الفاتورة"}),i.jsx("span",{className:"sm:hidden",children:"طباعة"})]})})]})})}),i.jsxs("div",{className:"receipt-size-selector",children:[i.jsx("h2",{className:"text-lg font-medium ml-4",children:"حجم الفاتورة:"}),i.jsxs("button",{onClick:()=>C("small"),className:`receipt-size-button ${u==="small"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[i.jsx(tt,{}),i.jsx("span",{children:"صغير (58mm)"})]}),i.jsxs("button",{onClick:()=>C("medium"),className:`receipt-size-button ${u==="medium"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[i.jsx(tt,{}),i.jsx("span",{children:"متوسط (80mm)"})]}),i.jsxs("button",{onClick:()=>C("a4"),className:`receipt-size-button ${u==="a4"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[i.jsx(Ut,{}),i.jsx("span",{children:"A4"})]})]})]}),i.jsx("div",{className:`receipt-wrapper receipt-size-${u}`,children:i.jsxs("div",{className:"receipt-content",children:[i.jsxs("div",{className:"receipt-header text-center receipt-section",children:[i.jsx("h1",{children:n.name}),n.address&&i.jsx("p",{className:"mb-1",children:n.address}),n.phone&&i.jsxs("p",{className:"mb-1",children:["هاتف: ",n.phone]}),n.email&&i.jsx("p",{className:"mb-1",children:n.email}),n.receipt_header&&i.jsx("p",{className:"mt-2 italic",children:n.receipt_header}),i.jsx("div",{className:"border-t border-gray-300 dark:border-gray-700 my-3"}),i.jsx("h2",{children:"فاتورة مبيعات"})]}),i.jsxs("div",{className:"receipt-info grid grid-cols-2 gap-2 receipt-section",children:[i.jsxs("div",{children:[i.jsxs("p",{children:[i.jsx("strong",{children:"رقم الفاتورة:"})," #",t.id]}),i.jsxs("p",{children:[i.jsx("strong",{children:"التاريخ:"})," ",N(t.created_at)]}),i.jsxs("p",{children:[i.jsx("strong",{children:"الوقت:"})," ",_(t.created_at)]})]}),i.jsxs("div",{className:"text-left",children:[i.jsxs("p",{children:[i.jsx("strong",{children:"الكاشير:"})," ",t.cashier_name||"-"]}),i.jsxs("p",{children:[i.jsx("strong",{children:"رقم الفاتورة الضريبي:"})," INV-",t.id,"-",new Date(t.created_at).getFullYear()]}),i.jsxs("p",{children:[i.jsx("strong",{children:"حالة الدفع:"})," ",t.payment_status==="paid"?"مدفوع":t.payment_status==="partial"?"جزئي":t.payment_status==="credit"?"آجل":"غير محدد"]})]})]}),t.customer&&t.customer.name!=="عميل مباشر"&&i.jsxs("div",{className:"receipt-customer receipt-section",children:[i.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"معلومات العميل"}),i.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[i.jsxs("div",{children:[i.jsxs("p",{children:[i.jsx("strong",{children:"الاسم:"})," ",t.customer.name]}),t.customer.phone&&i.jsxs("p",{children:[i.jsx("strong",{children:"الهاتف:"})," ",t.customer.phone]})]}),i.jsxs("div",{className:"text-left",children:[t.customer.email&&i.jsxs("p",{children:[i.jsx("strong",{children:"البريد الإلكتروني:"})," ",t.customer.email]}),t.customer.total_debt!==void 0&&t.customer.total_debt>0&&i.jsxs("p",{children:[i.jsx("strong",{children:"إجمالي المديونية:"})," ",t.customer.total_debt.toFixed(2)," ",n.currency_symbol]})]})]}),t.customer.address&&i.jsxs("p",{className:"mt-2",children:[i.jsx("strong",{children:"العنوان:"})," ",t.customer.address]})]}),i.jsxs("div",{className:"receipt-items py-2 receipt-section",children:[i.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"تفاصيل المنتجات"}),i.jsxs("table",{className:"receipt-items-table",children:[i.jsx("thead",{children:i.jsxs("tr",{children:[i.jsx("th",{className:"text-right",children:"المنتج"}),i.jsx("th",{className:"text-center",children:"الكمية"}),i.jsx("th",{className:"text-center",children:"السعر"}),i.jsx("th",{className:"text-left",children:"المجموع"})]})}),i.jsx("tbody",{children:t.items.map(h=>i.jsxs("tr",{children:[i.jsx("td",{className:"text-right",children:h.product_name||`منتج #${h.product_id}`}),i.jsx("td",{className:"text-center",children:h.quantity}),i.jsx("td",{className:"text-center",children:(h.price||h.unit_price).toFixed(2)}),i.jsx("td",{className:"text-left",children:h.subtotal.toFixed(2)})]},h.id))})]})]}),i.jsxs("div",{className:"receipt-totals receipt-section",children:[i.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"ملخص الفاتورة"}),(()=>{const h=t.discount_amount||0,g=t.total_amount,m=g-h,d=t.tax_amount||0,w=m+d;return i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"المجموع قبل الخصم:"}),i.jsxs("span",{children:[g.toFixed(2)," ",n.currency_symbol]})]}),h>0&&i.jsxs("div",{className:"total-row discount-row",children:[i.jsxs("span",{children:["الخصم (",t.discount_type==="percentage"?"نسبة مئوية":"مبلغ ثابت","):"]}),i.jsxs("span",{children:["-",h.toFixed(2)," ",n.currency_symbol,t.discount_type==="percentage"&&i.jsxs("span",{className:"text-xs mr-1",children:["(",(h/g*100).toFixed(1),"%)"]})]})]}),i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"المجموع بعد الخصم:"}),i.jsxs("span",{children:[m.toFixed(2)," ",n.currency_symbol]})]}),d>0&&i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"الضريبة:"}),i.jsxs("span",{children:[d.toFixed(2)," ",n.currency_symbol]})]}),i.jsxs("div",{className:"total-row grand-total",children:[i.jsx("span",{children:"المجموع النهائي:"}),i.jsxs("span",{children:[w.toFixed(2)," ",n.currency_symbol]})]})]})})()]}),i.jsxs("div",{className:"receipt-payment receipt-section",children:[i.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"معلومات الدفع"}),(()=>{const h=t.discount_amount||0,g=t.total_amount-h,m=t.tax_amount||0,d=g+m,w=t.amount_paid||0,b=d-w,E=t.change_amount||0,A=()=>{if(t.payment_status==="credit")return"آجل";if(t.payment_status==="partial")return"جزئي";switch(t.payment_method){case"cash":return"نقدي";case"card":return"بطاقة";case"credit":return"آجل";case"partial":return"جزئي";default:return t.payment_method}};return i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"طريقة الدفع:"}),i.jsx("span",{children:A()})]}),i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"المبلغ المدفوع:"}),i.jsxs("span",{children:[w.toFixed(2)," ",n.currency_symbol]})]}),b>0&&i.jsxs("div",{className:"total-row debt-row",style:{color:"#dc2626"},children:[i.jsx("span",{children:"المبلغ المتبقي (مديونية):"}),i.jsxs("span",{children:[b.toFixed(2)," ",n.currency_symbol]})]}),t.payment_method==="cash"&&E>0&&i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"المتبقي (الباقي):"}),i.jsxs("span",{children:[E.toFixed(2)," ",n.currency_symbol]})]}),i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"حالة الفاتورة:"}),i.jsx("span",{style:{color:t.payment_status==="paid"?"#16a34a":t.payment_status==="partial"?"#ea580c":t.payment_status==="credit"?"#dc2626":"#6b7280"},children:t.payment_status==="paid"?"✓ مدفوعة بالكامل":t.payment_status==="partial"?"◐ مدفوعة جزئياً":t.payment_status==="credit"?"○ آجلة":"غير محدد"})]})]})})()]}),t.notes&&i.jsxs("div",{className:"receipt-notes mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md receipt-section",children:[i.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"ملاحظات"}),i.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:t.notes})]}),i.jsxs("div",{className:"receipt-footer receipt-section",children:[n.receipt_footer&&i.jsx("p",{className:"mb-3",children:n.receipt_footer}),i.jsx("p",{className:"mt-1",children:"** نسخة العميل **"})]}),(u==="a4"||u==="medium")&&y&&i.jsxs("div",{className:"receipt-qr",children:[i.jsx("div",{className:"qr-container",children:i.jsx("img",{src:y,alt:"QR Code",className:"qr-image"})}),i.jsxs("div",{className:"qr-info",children:[i.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 mb-1",children:"امسح الرمز للحصول على معلومات الفاتورة"}),i.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:["هاتف المتجر: ",n.phone]})]})]}),u==="a4"&&i.jsx("div",{className:"receipt-stamp",children:"مدفوع"})]})})]})};export{Te as default};
