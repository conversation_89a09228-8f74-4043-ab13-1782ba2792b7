import{r as t,b as E,j as e,u as Hr,d as Ft,a as It,L as _t}from"./index-BtFmrjEm.js";import{g as Ce,I as te,s as we,B as Be,k as Gr,M as Pt,z as pr,N as Qe,F as $t,a as Tt,K as qt,e as gr,O as Et,P as Mt,Q as je,R as ur,l as ke,r as Or,q as Wr,m as ve,j as hr,S as Ur,T as Vr,U as At,C as yr,V as br,W as Rt,D as Kr,X as zt}from"./index-BEpVSoxh.js";import{T as Yr}from"./ToggleSwitch-D7j2F6ek.js";import{M as Ne}from"./Modal-BgtJQkhC.js";import{N as Xr,T as Zr}from"./BarcodeInput-CFOVzXV3.js";const Jr=t.forwardRef(({selectedCustomer:b,onCustomerSelect:P,onAddCustomer:F},$)=>{const[y,x]=t.useState([]),[S,ae]=t.useState(""),[T,w]=t.useState(!1),[z,ce]=t.useState(!1),[L,X]=t.useState(!1),[q,W]=t.useState(1),[M,A]=t.useState(!0),[se,le]=t.useState(0);t.useEffect(()=>{C()},[]),t.useImperativeHandle($,()=>({refreshCustomers:C}));const C=async(u=1,B=!1,p="")=>{try{B?X(!0):(ce(!0),W(1),A(!0));const I=10,j=(u-1)*I,ge=await E.get("/api/customers",{params:{active_only:!0,skip:j,limit:I,search:p||S}}),_=ge.data||[];console.log(`CustomerSelector: Fetched ${_.length} customers for page ${u}`);let G=_.length===I,R=0;ge.headers["x-total-count"]?(R=parseInt(ge.headers["x-total-count"]),G=(B?u*I:I)<R,le(R),console.log(`CustomerSelector: Total count from server: ${R}, hasMore: ${G}`)):(le(U=>B?U+_.length:_.length),console.log(`CustomerSelector: Using fallback logic, hasMore: ${G}`)),A(G),B?(x(U=>[...U,..._]),W(u)):(x(_),W(1))}catch(I){console.error("Error fetching customers:",I)}finally{ce(!1),X(!1)}},xe=async()=>{try{return(await E.get("/api/customers/default/direct")).data}catch(u){return console.error("Error fetching default customer:",u),null}},Se=()=>{console.log(`CustomerSelector: loadMoreCustomers called - hasMore: ${M}, loadingMore: ${L}, currentPage: ${q}`),M&&!L&&(console.log(`CustomerSelector: Loading page ${q+1}`),C(q+1,!0,S))};t.useEffect(()=>{const u=setTimeout(()=>{C(1,!1,S)},300);return()=>clearTimeout(u)},[S]);const D=y.filter(u=>!(u.name==="عميل مباشر"||u.name.toLowerCase()==="direct customer")),me=u=>{const{scrollTop:B,scrollHeight:p,clientHeight:I}=u.currentTarget;p-B-I<50&&Se()},Z=u=>{P(u),w(!1),ae("")},Fe=()=>{P(null),ae("")},i=async()=>{const u=await xe();u&&(P(u),w(!1),ae(""))};return e.jsxs("div",{className:"relative",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"العميل"}),b?e.jsxs("div",{className:"flex items-center justify-between p-3 bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-700 rounded-lg",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-8 w-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center",children:e.jsx(Ce,{className:"text-primary-600 dark:text-primary-400 text-sm"})}),e.jsxs("div",{className:"mr-3",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:b.name}),b.phone&&e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:b.phone}),b.total_debt>0&&e.jsxs("div",{className:"text-xs text-red-600 dark:text-red-400",children:["مديونية: ",b.total_debt.toFixed(2)," د.ل"]})]})]}),e.jsx("button",{onClick:Fe,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"إلغاء الاختيار",children:e.jsx(te,{})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"البحث عن عميل أو اختيار عميل جديد...",value:S,onChange:u=>{ae(u.target.value),w(!0)},onFocus:()=>w(!0),className:"input-field pr-10"}),e.jsx(we,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]}),T&&e.jsx("div",{className:"absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg overflow-hidden",children:z?e.jsx("div",{className:"p-4 text-center",children:e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600 mx-auto"})}):e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:i,className:"w-full px-4 py-3 text-right hover:bg-green-50 dark:hover:bg-green-900/20 border-b border-gray-200 dark:border-gray-700 text-green-600 dark:text-green-400",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-8 w-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center",children:e.jsx(Ce,{className:"text-green-600 dark:text-green-400 text-sm"})}),e.jsxs("div",{className:"mr-3",children:[e.jsx("div",{className:"font-medium",children:"عميل مباشر"}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"للمبيعات النقدية المباشرة"})]})]})}),F&&e.jsxs("button",{onClick:()=>{F(),w(!1)},className:"w-full px-4 py-3 text-right hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 flex items-center",children:[e.jsx(Be,{className:"text-primary-600 dark:text-primary-400 ml-3"}),e.jsx("span",{className:"text-primary-600 dark:text-primary-400 font-medium",children:"إضافة عميل جديد"})]}),e.jsx("div",{className:"max-h-60 overflow-y-auto custom-scrollbar-thin",onScroll:me,children:z&&D.length===0?e.jsxs("div",{className:"p-4 text-center text-gray-500 dark:text-gray-400",children:[e.jsx("div",{className:"animate-spin h-5 w-5 border-2 border-gray-300 border-t-primary-500 rounded-full mx-auto mb-2"}),"جاري التحميل..."]}):D.length===0?e.jsx("div",{className:"p-4 text-center text-gray-500 dark:text-gray-400",children:S?"لا توجد نتائج":"لا توجد عملاء"}):e.jsxs(e.Fragment,{children:[D.map(u=>e.jsx("button",{onClick:()=>Z(u),className:"w-full px-4 py-3 text-right hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700 last:border-b-0",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-8 w-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center",children:e.jsx(Ce,{className:"text-gray-600 dark:text-gray-400 text-sm"})}),e.jsxs("div",{className:"mr-3 flex-1",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 text-right",children:u.name}),e.jsxs("div",{className:"flex justify-between items-center",children:[u.phone&&e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:u.phone}),u.total_debt>0&&e.jsxs("span",{className:"text-xs text-red-600 dark:text-red-400",children:["مديونية: ",u.total_debt.toFixed(2)," د.ل"]})]})]})]})},u.id)),L&&e.jsxs("div",{className:"p-3 text-center text-gray-500 dark:text-gray-400",children:[e.jsx("div",{className:"animate-spin h-4 w-4 border-2 border-gray-300 border-t-primary-500 rounded-full mx-auto mb-1"}),e.jsx("span",{className:"text-xs",children:"جاري تحميل المزيد..."})]}),!M&&D.length>0&&e.jsxs("div",{className:"p-2 text-center text-xs text-gray-400 dark:text-gray-500",children:["تم عرض جميع العملاء (",se,")"]})]})})]})})]}),T&&e.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>w(!1)})]})});Jr.displayName="CustomerSelector";const Lt=({paymentType:b,onPaymentTypeChange:P,totalAmount:F,hasCustomer:$})=>{const y=[{id:"full",label:"دفع كامل",description:`دفع المبلغ كاملاً (${F.toFixed(2)} د.ل)`,icon:e.jsx(Gr,{}),color:"green",available:!0},{id:"partial",label:"دفع جزئي",description:"دفع جزء من المبلغ والباقي دين",icon:e.jsx(Pt,{}),color:"orange",available:$},{id:"credit",label:"بيع آجل",description:"تسجيل المبلغ كاملاً كدين",icon:e.jsx(pr,{}),color:"red",available:$}];return e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"نوع الدفع"}),e.jsx("div",{className:"grid grid-cols-1 gap-3",children:y.map(x=>e.jsx("button",{type:"button",onClick:()=>x.available&&P(x.id),disabled:!x.available,className:`
              relative p-4 rounded-xl border-2 text-right transition-all duration-200
              ${b===x.id?`border-${x.color}-500 bg-${x.color}-50 dark:bg-${x.color}-900/20`:x.available?"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800":"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 opacity-50 cursor-not-allowed"}
            `,children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`
                flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center
                ${b===x.id?`bg-${x.color}-100 dark:bg-${x.color}-900/40 text-${x.color}-600 dark:text-${x.color}-400`:x.available?"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400":"bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500"}
              `,children:x.icon}),e.jsxs("div",{className:"mr-4 flex-1",children:[e.jsx("div",{className:`
                  font-medium text-base
                  ${b===x.id?`text-${x.color}-700 dark:text-${x.color}-300`:x.available?"text-gray-900 dark:text-gray-100":"text-gray-500 dark:text-gray-400"}
                `,children:x.label}),e.jsx("div",{className:`
                  text-sm mt-1
                  ${b===x.id?`text-${x.color}-600 dark:text-${x.color}-400`:x.available?"text-gray-600 dark:text-gray-400":"text-gray-400 dark:text-gray-500"}
                `,children:x.description}),!x.available&&e.jsx("div",{className:"text-xs text-red-500 dark:text-red-400 mt-1",children:"يتطلب اختيار عميل"})]}),b===x.id&&e.jsx("div",{className:`
                  flex-shrink-0 w-5 h-5 rounded-full border-2 border-${x.color}-500
                  bg-${x.color}-500 flex items-center justify-center
                `,children:e.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})})]})},x.id))}),!$&&e.jsx("div",{className:"bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl p-3",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-amber-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),e.jsx("div",{className:"mr-3",children:e.jsx("p",{className:"text-sm text-amber-800 dark:text-amber-200",children:"لاستخدام الدفع الجزئي أو البيع الآجل، يجب اختيار عميل أولاً"})})]})})]})},Wt=()=>{const[b,P]=t.useState([]),[F,$]=t.useState([]),[y,x]=t.useState([]),[S,ae]=t.useState(""),[T,w]=t.useState(!0),[z,ce]=t.useState(!1),[L,X]=t.useState(!1),[q,W]=t.useState("cash"),[M,A]=t.useState(""),[se,le]=t.useState("0"),[C,xe]=t.useState("fixed"),[Se,D]=t.useState(!1),[me,Z]=t.useState(null),[Fe,i]=t.useState(null),[u,B]=t.useState(null),[p,I]=t.useState(null),[j,ge]=t.useState([]),[_,G]=t.useState(!1),[R,U]=t.useState(!1),[ne,fr]=t.useState(j.length),[jr,et]=t.useState({top:0,left:0}),[He,rt]=t.useState(!1),[J,Oe]=t.useState(!1),[Ie,We]=t.useState(!1),[k,Ue]=t.useState(null),[_e,Ve]=t.useState([]),[Ke,kr]=t.useState(!1),[Ye,vr]=t.useState(!1),[Nr,wr]=t.useState(1),[Xe,Cr]=t.useState(!0),[de,Ze]=t.useState(""),[Sr,Fr]=t.useState(1),[Pe,Ir]=t.useState(!0),[tt,Ge]=t.useState(!1),[H,Je]=t.useState(null),[_r,$e]=t.useState(""),[f,Te]=t.useState(null),[m,ue]=t.useState("full"),[at,er]=t.useState(!1),[he,rr]=t.useState(""),[Pr,tr]=t.useState(""),[ar,$r]=t.useState(!1),{user:O}=Hr(),{currentTheme:Tr,toggleTheme:st}=Ft(),sr=It(),lt=t.useRef(null),Q=t.useRef(null),ye=t.useRef(null),qe=t.useRef(null),Ee=t.useRef(null),qr=t.useRef(null),lr=t.useRef(null),[V,Me]=t.useState(""),[Er,nt]=t.useState(0),dt=50,[it,nr]=t.useState(!1),K=y.reduce((r,a)=>r+a.subtotal,0),dr=y.reduce((r,a)=>r+a.cartQuantity,0),ee=C==="percentage"?K*parseFloat(se||"0")/100:parseFloat(se||"0"),Ae=Math.max(0,K-ee),Mr=(u==null?void 0:u.tax_rate)||0,ie=Ae*parseFloat(Mr.toString())/100,v=Ae+ie,ir=q==="cash"&&M?parseFloat(M)-v:0,ot=r=>ee<=0?0:r.subtotal/K*ee,re=t.useCallback(r=>{if(r.quantity===0){i(`لا يمكن إضافة ${r.name}. المنتج غير متوفر في المخزون.`),setTimeout(()=>i(null),3e3);return}const a=y.find(s=>s.id===r.id);if(a&&a.cartQuantity>=r.quantity){i(`لا يمكن إضافة المزيد من ${r.name}. الكمية المتوفرة: ${r.quantity}`),setTimeout(()=>i(null),3e3);return}x(s=>s.find(l=>l.id===r.id)?s.map(l=>l.id===r.id?{...l,cartQuantity:l.cartQuantity+1,subtotal:(l.cartQuantity+1)*l.price}:l):[...s,{...r,cartQuantity:1,subtotal:r.price}])},[y,i]),or=t.useCallback(r=>{const a=document.activeElement;if(a&&(a.tagName==="INPUT"||a.tagName==="TEXTAREA"||a.getAttribute("contenteditable")==="true")||(L||Ie||J||He))return;const l=new Date().getTime();if(l-Er<dt){if(nr(!0),r.key!=="Shift"&&r.key!=="Control"&&r.key!=="Alt"&&r.key!=="Tab")if(r.key==="Enter"){if(V.length>5){const o=b.find(d=>d.barcode===V&&d.is_active===!0);if(o)re(o),Q.current&&(Q.current.value=""),ye.current&&(ye.current.value=""),new Audio("/beep-success.mp3").play().catch(c=>console.log("Audio play failed:",c));else{const d=b.find(h=>h.barcode===V);d&&!d.is_active?i(`المنتج ${d.name} غير نشط ولا يمكن بيعه`):i(`لم يتم العثور على منتج بالباركود: ${V}`),setTimeout(()=>i(null),3e3),new Audio("/beep-error.mp3").play().catch(h=>console.log("Audio play failed:",h))}}Me(""),setTimeout(()=>nr(!1),500)}else(r.key.length===1||r.key==="Backspace")&&(r.key==="Backspace"?Me(o=>o.slice(0,-1)):Me(o=>o+r.key))}else r.key.length===1&&(Me(r.key),nr(!0));nt(l)},[V,Er,b,re,i,L,Ie,J,He]);t.useEffect(()=>(window.addEventListener("keydown",or),()=>{window.removeEventListener("keydown",or)}),[or]),t.useEffect(()=>{const r=a=>{var s;if(R){const n=a.target;!((s=qr.current)!=null&&s.contains(n))&&!n.closest('[data-dropdown="categories"]')&&U(!1)}};return document.addEventListener("mousedown",r),()=>{document.removeEventListener("mousedown",r)}},[R]);const Re=t.useCallback(()=>{if(!Ee.current||j.length===0){fr(j.length);return}setTimeout(()=>{if(!Ee.current)return;const r=Ee.current,n=r.offsetWidth-17,l=document.createElement("div");l.style.position="absolute",l.style.visibility="hidden",l.style.whiteSpace="nowrap",l.style.display="flex",l.style.gap="8px",l.style.fontSize="14px",l.style.fontFamily=getComputedStyle(r).fontFamily,document.body.appendChild(l);const g=document.createElement("button");g.className="px-3 py-1.5 rounded-lg text-sm font-medium flex-shrink-0",g.style.padding="6px 12px",g.innerHTML='<span style="display: flex; align-items: center; gap: 6px;"><svg style="width: 12px; height: 12px;"></svg>الكل</span>',l.appendChild(g);let o=g.offsetWidth+8,d=0;for(let c=0;c<j.length;c++){const h=document.createElement("button");h.className="px-3 py-1.5 rounded-lg text-sm font-medium whitespace-nowrap",h.style.padding="6px 12px",h.innerHTML=`<span style="display: flex; align-items: center; gap: 6px;"><svg style="width: 12px; height: 12px;"></svg>${j[c]}</span>`,l.appendChild(h);const N=h.offsetWidth,fe=j.length-c-1>0?70:0;if(o+N+fe<=n)o+=N+8,d++;else break}document.body.removeChild(l),fr(Math.max(0,d))},100)},[j]);t.useEffect(()=>{Re()},[j,Re]),t.useEffect(()=>{const r=()=>{Re()};return window.addEventListener("resize",r),()=>window.removeEventListener("resize",r)},[Re]);const ze=t.useRef(null),be=t.useCallback(async(r=1,a=15,s=!1)=>{try{s?ce(!0):w(!0),i(null);const n=new URLSearchParams;n.append("page",r.toString()),n.append("limit",a.toString()),S&&n.append("search",S),p&&n.append("category",p);const l=await E.get(`/api/products/?${n.toString()}`);console.log("Products fetched from API:",l.data);const g=parseInt(l.headers["x-pages"]||"1"),o=parseInt(l.headers["x-page"]||"1");if(Fr(o),Ir(o<g),l.data&&Array.isArray(l.data)){const d=l.data;if(s)P(c=>[...c,...d]),$(c=>{let h=d;return h=h.filter(N=>N.is_active===!0),_&&(h=h.filter(N=>N.quantity>0)),[...c,...h]});else{P(d);let c=d;c=c.filter(h=>h.is_active===!0),_&&(c=c.filter(h=>h.quantity>0)),$(c)}}else console.error("API returned empty data"),s||(i("لم يتم العثور على منتجات. يرجى التحقق من قاعدة البيانات."),P([]),$([]))}catch(n){console.error("Error fetching products from API:",n),i("فشل في تحميل المنتجات. يرجى التحقق من الاتصال والمحاولة مرة أخرى.")}finally{w(!1),ce(!1)}},[S,p,_]),Le=t.useCallback(()=>{Pe&&!z&&!T&&be(Sr+1,15,!0)},[be,Sr,Pe,z,T]),Ar=(r,a)=>{let s=!1;return function(...n){s||(r.apply(this,n),s=!0,setTimeout(()=>s=!1,a))}},De=t.useCallback(Ar(()=>{if(ze.current){const{scrollTop:r,scrollHeight:a,clientHeight:s}=ze.current;a-r-s<200&&Le()}},300),[Le]),ct=t.useCallback(Ar(()=>{if(lr.current){const{scrollTop:r,scrollHeight:a,clientHeight:s}=lr.current;a-r-s<200&&Le()}},300),[Le]),xt=t.useCallback(async()=>{try{const r=await E.get("/api/products/categories/list");r.data&&Array.isArray(r.data)&&r.data.length>0&&ge(r.data)}catch(r){console.error("Error fetching categories:",r)}},[]),mt=t.useCallback(async()=>{try{const r=await E.get("/api/settings/public");r.data&&B(r.data)}catch(r){console.error("Error fetching store settings:",r),B({tax_rate:0})}},[]);t.useEffect(()=>{(async()=>{await mt(),await xt(),await be(1,20,!1),Q.current&&Q.current.focus()})()},[]),t.useEffect(()=>{const r=ze.current;if(r)return r.addEventListener("scroll",De),()=>{r.removeEventListener("scroll",De)}},[De]);const Rr=t.useRef(S),zr=t.useRef(p);t.useEffect(()=>{if(b.length>0){let r=b;r=r.filter(a=>a.is_active===!0),_&&(r=r.filter(a=>a.quantity>0)),$(r)}},[_,b]),t.useEffect(()=>{(Rr.current!==S||zr.current!==p)&&(Fr(1),Ir(!0),be(1,15,!1),Rr.current=S,zr.current=p)},[S,p,be]),t.useEffect(()=>{L&&v>0&&A(v.toFixed(2))},[v,L]),t.useEffect(()=>{v>0&&A(v.toFixed(2))},[se,C,v]);const gt=r=>{ae(r.target.value)},Lr=r=>{r.preventDefault();const a=document.activeElement;let s=null;if(a===Q.current?s=Q.current:a===ye.current?s=ye.current:s=Q.current,s&&s.value){const n=s.value;if(n!==V){const l=b.find(g=>g.barcode===n&&g.is_active===!0);if(l)re(l),s.value="";else{const g=b.find(o=>o.barcode===n);g&&!g.is_active?i(`المنتج ${g.name} غير نشط ولا يمكن بيعه`):i(`لم يتم العثور على منتج بالباركود: ${n}`),setTimeout(()=>i(null),3e3)}}else s.value=""}s&&s.focus()},cr=r=>{I(r===p?null:r)},xr=(r,a)=>{if(a<1||!y.find(l=>l.id===r))return;const n=F.find(l=>l.id===r);if(n){if(a>n.quantity){i(`لا يمكن إضافة كمية أكبر من المتوفرة. الكمية المتوفرة: ${n.quantity}`),setTimeout(()=>i(null),3e3);return}x(l=>l.map(g=>g.id===r?{...g,cartQuantity:a,subtotal:a*g.price}:g))}},ut=r=>{x(a=>a.filter(s=>s.id!==r))},ht=r=>{Je(r),$e(r.cartQuantity.toString()),Ge(!0)},Dr=()=>{if(!H)return;const r=parseInt(_r);if(isNaN(r)||r<1){i("يرجى إدخال كمية صحيحة");return}xr(H.id,r),Ge(!1),Je(null),$e("")},mr=()=>{Ge(!1),Je(null),$e("")},pe=()=>{x([]),X(!1),W("cash"),A(""),le("0"),xe("fixed"),D(!1),Z(null),Te(null),ue("full")},yt=()=>{if(y.length===0){i("لا يمكن إتمام عملية الدفع، السلة فارغة."),setTimeout(()=>i(null),3e3);return}A(m==="full"?v.toFixed(2):m==="credit"?"0":""),X(!0)},bt=async()=>{try{const r=parseFloat(M||"0");if(m==="full"&&r<v){i("المبلغ المدفوع أقل من إجمالي الفاتورة.");return}if(m==="partial"&&r>=v){i("للدفع الجزئي، يجب أن يكون المبلغ المدفوع أقل من الإجمالي.");return}if((m==="partial"||m==="credit")&&!f){i("يجب اختيار عميل للدفع الجزئي أو البيع الآجل.");return}w(!0);const a={payment_method:m==="credit"?"آجل":q,total_amount:K,tax_amount:ie,discount_amount:ee,discount_type:C,customer_id:(f==null?void 0:f.id)||null,customer_name:(f==null?void 0:f.name)||null,notes:null,amount_paid:m==="credit"?0:r,payment_status:m==="full"?"paid":m==="partial"?"partial":"unpaid",items:y.map(s=>({product_id:s.id,quantity:s.cartQuantity,unit_price:s.price,discount:0}))};console.log("Submitting sale:",a);try{const s=Hr.getState();if(!s.token)try{await s.refreshAccessToken(),console.log("Token refreshed successfully")}catch(o){console.error("Failed to refresh token:",o)}if(!s.token)throw new Error("No authentication token available");console.log("Submitting sale to API with correct path");let g=null;try{const o=await E.get("/api/sales/?limit=1");if(o.data&&o.data.length>0){const d=o.data[0];Math.abs(d.total_amount-v)<.01&&(g=d,console.log("Found existing sale with matching total:",g))}}catch(o){console.error("Error checking for existing sales:",o)}if(g){console.log("Using existing sale instead of creating a new one:",g.id),Z(g.id),D(!0),P(o=>o.map(d=>{const c=y.find(h=>h.id===d.id);return c?{...d,quantity:d.quantity-c.cartQuantity}:d})),$(o=>o.map(d=>{const c=y.find(h=>h.id===d.id);return c?{...d,quantity:d.quantity-c.cartQuantity}:d})),w(!1);return}try{const o=await E.post("/api/sales/",a);console.log("Sale response:",o.data),Z(o.data.id),D(!0),i(null),P(d=>d.map(c=>{const h=y.find(N=>N.id===c.id);return h?{...c,quantity:c.quantity-h.cartQuantity}:c})),$(d=>d.map(c=>{const h=y.find(N=>N.id===c.id);return h?{...c,quantity:c.quantity-h.cartQuantity}:c})),w(!1)}catch(o){console.error("API error when completing sale:",o);try{await new Promise(c=>setTimeout(c,500));const d=await E.get("/api/sales/?limit=1");if(console.log("Latest sales response after error:",d.data),d.data&&d.data.length>0){const c=d.data[0];if((!g||c.id!==g.id)&&(console.log("Found new sale after error:",c),Math.abs(c.total_amount-v)<.01)){console.log("Sale was created successfully despite error"),Z(c.id),D(!0),P(h=>h.map(N=>{const oe=y.find(fe=>fe.id===N.id);return oe?{...N,quantity:N.quantity-oe.cartQuantity}:N})),$(h=>h.map(N=>{const oe=y.find(fe=>fe.id===N.id);return oe?{...N,quantity:N.quantity-oe.cartQuantity}:N})),w(!1);return}}}catch(d){console.error("Error fetching latest sale after error:",d)}i("فشل في إتمام عملية البيع. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى."),w(!1)}}catch{i("فشل في إتمام عملية البيع. يرجى المحاولة مرة أخرى."),w(!1)}}catch{i("فشل في إتمام عملية البيع. يرجى المحاولة مرة أخرى."),w(!1)}},pt=()=>{me?sr(`/sales/${me}/print`):(i("لا يمكن طباعة الفاتورة. لم يتم إتمام عملية البيع بنجاح."),setTimeout(()=>i(null),3e3))},ft=()=>{pe(),Ue(null),Q.current&&Q.current.focus()},jt=()=>{er(!0)},kt=async()=>{var r,a;if(!he.trim()){i("يرجى إدخال اسم العميل");return}try{$r(!0);const s={name:he.trim(),phone:Pr.trim()||null,email:null,address:null,is_active:!0},l={...(await E.post("/api/customers/",s)).data,total_debt:0};Te(l),qe.current&&qe.current.refreshCustomers&&qe.current.refreshCustomers(),er(!1),rr(""),tr(""),i(null)}catch(s){console.error("Error adding customer:",s),(a=(r=s.response)==null?void 0:r.data)!=null&&a.detail?i(s.response.data.detail):i("فشل في إضافة العميل. يرجى المحاولة مرة أخرى.")}finally{$r(!1)}},Qr=()=>{er(!1),rr(""),tr(""),i(null)},Y=t.useCallback(async(r=1,a=!1,s="")=>{try{a?vr(!0):(kr(!0),Ve([]),wr(1),Cr(!0));const n=new URLSearchParams;n.append("limit","10"),n.append("page",r.toString()),s.trim()&&n.append("search",s.trim());const l=await E.get(`/api/sales/?${n.toString()}`);if(l.data&&Array.isArray(l.data)){const g=parseInt(l.headers["x-pages"]||"1"),o=parseInt(l.headers["x-page"]||"1");wr(o),Cr(o<g),Ve(a?d=>[...d,...l.data]:l.data)}}catch(n){console.error("Error fetching previous invoices:",n),i("فشل في تحميل الفواتير السابقة")}finally{kr(!1),vr(!1)}},[]),vt=t.useCallback(()=>{Xe&&!Ye&&!Ke&&Y(Nr+1,!0,de)},[Y,Nr,Xe,Ye,Ke,de]),Nt=t.useCallback(async r=>{var a;try{if(pe(),Ue(r),r.items&&r.items.length>0)for(const s of r.items){const n=b.find(l=>l.id===s.product_id);if(n)for(let l=0;l<s.quantity;l++)re(n)}if(r.customer_id)try{const s=await E.get(`/api/customers/${r.customer_id}`);Te(s.data)}catch(s){console.error("Error fetching customer:",s)}r.payment_status==="unpaid"?ue("credit"):r.payment_status==="partial"?ue("partial"):ue("full"),W(r.payment_method||"cash"),A(((a=r.amount_paid)==null?void 0:a.toString())||"0"),We(!1)}catch(s){console.error("Error loading invoice for editing:",s),i("فشل في تحميل الفاتورة للتعديل")}},[b,re,pe]),Br=t.useCallback(()=>{Y(1,!1,de)},[Y,de]),wt=t.useCallback(()=>{Ze(""),Y(1,!1,"")},[Y]),Ct=t.useCallback(()=>{We(!0),Ze(""),Y(1,!1,"")},[Y]),St=t.useCallback(async()=>{if(k){w(!0);try{const r={payment_method:m==="credit"?"آجل":q,total_amount:K,tax_amount:ie,discount_amount:ee,discount_type:C,customer_id:(f==null?void 0:f.id)||k.customer_id,customer_name:(f==null?void 0:f.name)||k.customer_name,notes:k.notes,amount_paid:m==="credit"?0:parseFloat(M||"0"),payment_status:m==="full"?"paid":m==="partial"?"partial":"unpaid",items:y.map(s=>({product_id:s.id,quantity:s.cartQuantity,unit_price:s.price,discount:0}))},a=await E.put(`/api/sales/${k.id}`,r);console.log("Invoice updated:",a.data),Z(a.data.id),D(!0),i(null),P(s=>s.map(n=>{const l=y.find(o=>o.id===n.id),g=k.items.find(o=>o.product_id===n.id);if(l&&g){const o=l.cartQuantity-g.quantity;return{...n,quantity:n.quantity-o}}else{if(l&&!g)return{...n,quantity:n.quantity-l.cartQuantity};if(!l&&g)return{...n,quantity:n.quantity+g.quantity}}return n})),w(!1)}catch(r){console.error("Error updating invoice:",r),i("فشل في تحديث الفاتورة. يرجى المحاولة مرة أخرى."),w(!1)}}},[k,q,Ae,ie,y,ot]);return e.jsxs("div",{className:"flex flex-col h-screen w-full bg-gray-50 dark:bg-gray-900 overflow-hidden",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 py-2 px-4 flex justify-between items-center",children:[e.jsx("div",{className:"flex items-center",children:e.jsx(_t,{to:"/",className:"flex items-center",children:e.jsxs("span",{className:"text-xl font-bold text-primary-600",children:["Smart",e.jsx("span",{className:"text-secondary-800 dark:text-secondary-200",children:"POS"})]})})}),e.jsxs("div",{className:"flex items-center gap-2",children:[it&&e.jsxs("div",{className:"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-3 py-1 rounded-lg font-medium flex items-center text-sm animate-pulse",children:[e.jsx(Qe,{className:"ml-1"}),e.jsx("span",{children:"جاري المسح..."})]}),e.jsx("button",{onClick:st,className:"p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors","aria-label":Tr==="dark"?"التبديل إلى الوضع المضيء":"التبديل إلى الوضع المظلم",children:Tr==="dark"?e.jsx($t,{className:"text-yellow-400"}):e.jsx(Tt,{className:"text-gray-700"})}),e.jsxs("span",{className:"bg-blue-50 dark:bg-blue-900/40 px-3 py-1 rounded-lg text-blue-700 dark:text-blue-200 text-sm font-medium flex items-center",children:[e.jsx(qt,{className:"ml-1 text-blue-500 dark:text-blue-300"}),e.jsx("span",{children:(O==null?void 0:O.full_name)||"مستخدم"})]}),e.jsxs("span",{className:"bg-green-50 dark:bg-green-900/40 text-green-700 dark:text-green-200 px-3 py-1 rounded-lg font-medium flex items-center text-sm",children:[e.jsx(gr,{className:"ml-1"}),e.jsxs("span",{children:[v.toFixed(2)," د.ل"]})]})]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 shadow-sm py-2 px-4 border-b border-gray-200 dark:border-gray-700 z-40 flex items-center",children:[e.jsx("button",{onClick:()=>sr("/"),className:"text-secondary-600 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-700 h-8 w-8 flex items-center justify-center rounded-full mr-2",children:e.jsx(Et,{className:"text-lg"})}),e.jsx("h1",{className:"text-lg font-bold text-secondary-900 dark:text-secondary-100",children:"نقطة البيع"})]}),e.jsxs("div",{className:"flex flex-col md:flex-row flex-1 overflow-hidden h-full w-full",children:[e.jsx("div",{className:"md:hidden p-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 z-20",children:e.jsxs("form",{onSubmit:Lr,className:"flex gap-2",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:e.jsx(Qe,{className:"text-gray-400 dark:text-gray-500"})}),e.jsx("input",{ref:Q,type:"text",placeholder:"باركود المنتج...",className:"input-field pr-10 text-base w-full py-2 focus:ring-2 focus:ring-primary-500",autoFocus:!0})]}),e.jsx("button",{type:"submit",className:"btn-secondary py-2 px-3 whitespace-nowrap text-sm",children:"بحث"}),e.jsx("button",{type:"button",onClick:()=>rt(!0),className:"btn-secondary py-2 px-3 whitespace-nowrap text-sm flex items-center justify-center","aria-label":"فتح الكاميرا لمسح الباركود",children:e.jsx(Mt,{className:"text-lg"})})]})}),J&&e.jsx(Ne,{isOpen:J,onClose:()=>Oe(!1),title:"المنتجات",size:"full",children:e.jsxs("div",{className:"h-full flex flex-col",children:[e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:e.jsx(we,{className:"text-gray-400 dark:text-gray-500"})}),e.jsx("input",{type:"text",placeholder:"البحث عن منتج...",value:"",onChange:()=>{},className:"w-full px-4 py-3 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200",autoFocus:!0})]})})}),e.jsx("div",{className:"mb-4 sticky top-0 z-30 bg-gray-50 dark:bg-gray-900 pb-2 -mx-4 px-4",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow-lg backdrop-blur-sm",children:[j.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2",children:[e.jsx(je,{className:"text-primary-500 text-xs"}),"الفئات"]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute left-0 top-0 bottom-0 w-4 bg-gradient-to-r from-white dark:from-gray-800 to-transparent z-10 pointer-events-none opacity-50"}),e.jsx("div",{className:"absolute right-0 top-0 bottom-0 w-4 bg-gradient-to-l from-white dark:from-gray-800 to-transparent z-10 pointer-events-none opacity-50"}),e.jsxs("div",{className:"flex gap-2 overflow-x-auto pb-2 modal-scrollbar scroll-smooth px-2",style:{scrollbarWidth:"thin",scrollBehavior:"smooth",scrollbarColor:"#cbd5e1 transparent"},children:[e.jsx("button",{onClick:()=>I(null),className:`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex-shrink-0 ${p===null?"bg-primary-500 text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:e.jsxs("span",{className:"flex items-center gap-1.5",children:[e.jsx(je,{className:"text-xs"}),"الكل"]})}),j.map(r=>e.jsx("button",{onClick:()=>cr(r),className:`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex-shrink-0 whitespace-nowrap ${p===r?"bg-primary-500 text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:e.jsxs("span",{className:"flex items-center gap-1.5",children:[e.jsx(ur,{className:"text-xs"}),r]})},r))]})]})]}),e.jsx("div",{className:`${j.length>0?"pt-3 border-t border-gray-200 dark:border-gray-700":""}`,children:e.jsx(Yr,{id:"mobile-hide-zero-stock",checked:_,onChange:r=>G(r),label:"إخفاء المنتجات غير المتوفرة",className:"text-sm"})})]})}),e.jsx("div",{ref:lr,className:"flex-1 overflow-y-auto modal-scrollbar",onScroll:ct,children:T?e.jsxs("div",{className:"flex flex-col justify-center items-center h-full py-20",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"جاري تحميل المنتجات..."})]}):F.length>0?e.jsxs("div",{className:"space-y-3 p-1",children:[F.map(r=>e.jsx("div",{onClick:()=>{r.quantity>0&&(re(r),Oe(!1))},className:`bg-white dark:bg-gray-800 rounded-xl shadow-sm border transition-all duration-300 ${r.quantity>0?"border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-500 hover:shadow-md cursor-pointer active:scale-[0.98]":"border-gray-200 dark:border-gray-700 opacity-70 cursor-not-allowed"}`,children:e.jsxs("div",{className:"flex items-center p-4 gap-3",children:[e.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 rounded-lg flex items-center justify-center border border-primary-200 dark:border-primary-700",children:e.jsx(ke,{className:"text-primary-500 dark:text-primary-400 text-sm"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsx("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 text-base line-clamp-2 flex-1 pr-3",children:r.name}),e.jsx("span",{className:`text-xs font-medium px-2.5 py-1 rounded-full flex-shrink-0 ${r.quantity===0?"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400":r.quantity>r.min_quantity?"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400":"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400"}`,children:r.quantity===0?"نفد":r.quantity>r.min_quantity?"متوفر":"قليل"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-primary-600 dark:text-primary-400 font-bold text-lg",children:[r.price.toFixed(2)," د.ل"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center bg-gray-100 dark:bg-gray-700 px-2.5 py-1.5 rounded-lg",children:[e.jsx(Or,{className:"ml-1.5 text-gray-500 dark:text-gray-400 text-xs"}),e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-300 font-medium",children:r.quantity})]}),e.jsx("span",{className:"text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2.5 py-1.5 rounded-lg font-medium",children:r.unit}),r.quantity>0&&e.jsx("div",{className:"bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 p-2.5 rounded-lg border border-primary-200 dark:border-primary-700",children:e.jsx(Wr,{className:"text-sm"})})]})]})]})]})},r.id)),z&&e.jsxs("div",{className:"flex justify-center items-center py-6",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mr-3"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"جاري تحميل المزيد..."})]}),!Pe&&F.length>0&&!z&&e.jsx("div",{className:"text-center py-6",children:e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-xl p-4 mx-4 border border-gray-200 dark:border-gray-700",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx(ve,{className:"text-green-500 dark:text-green-400 text-lg"})}),e.jsx("h3",{className:"text-base font-semibold text-gray-800 dark:text-gray-200 mb-2",children:"تم تحميل جميع المنتجات"}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["إجمالي: ",F.length," منتج",p&&e.jsxs("span",{className:"block text-xs mt-1",children:["في فئة: ",p]})]})]})})]}):e.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center py-12",children:[e.jsx("div",{className:"bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 p-12 rounded-2xl mb-8 shadow-inner",children:e.jsx(ke,{className:"text-6xl text-gray-400 dark:text-gray-500"})}),e.jsx("h3",{className:"text-xl mb-3 font-bold text-gray-900 dark:text-gray-100",children:"لم يتم العثور على منتجات مطابقة"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 max-w-md mb-6",children:"حاول تغيير معايير البحث أو تصفية الفئات للعثور على المنتجات المطلوبة"}),e.jsx("button",{onClick:()=>{I(null)},className:"px-6 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg font-medium transition-all duration-200 hover:shadow-md",children:"إعادة ضبط البحث"})]})})]})}),e.jsxs("div",{className:"hidden md:flex w-full md:w-2/3 p-4 flex-col overflow-hidden order-2 md:order-1 h-full",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row mb-4 gap-3",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:e.jsx(we,{className:"text-gray-400 dark:text-gray-500"})}),e.jsx("input",{ref:lt,type:"text",placeholder:"البحث عن منتج...",value:S,onChange:gt,className:"input-field pr-10 text-base w-full py-2 focus:ring-1"})]}),e.jsxs("form",{onSubmit:Lr,className:"flex-1 flex gap-2",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:e.jsx(Qe,{className:"text-gray-400 dark:text-gray-500"})}),e.jsx("input",{ref:ye,type:"text",placeholder:"باركود المنتج...",className:"input-field pr-10 text-base w-full py-2 focus:ring-2 focus:ring-primary-500"})]}),e.jsx("button",{type:"submit",className:"btn-secondary py-2 px-3 whitespace-nowrap text-sm",children:"بحث"})]})]}),e.jsx("div",{className:"mb-4",style:{overflow:"visible"},children:e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700 shadow-sm",style:{overflow:"visible"},children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex-1 min-w-0",ref:Ee,children:j.length>0&&e.jsxs("div",{className:"flex items-center gap-2",style:{overflow:"visible"},children:[e.jsx("button",{onClick:()=>I(null),className:`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex-shrink-0 ${p===null?"bg-primary-500 text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:e.jsxs("span",{className:"flex items-center gap-1.5",children:[e.jsx(je,{className:"text-xs"}),"الكل"]})}),e.jsxs("div",{className:"flex items-center gap-2 overflow-hidden",children:[e.jsx("div",{className:"flex items-center gap-2 flex-shrink-0",children:j.slice(0,ne).map(r=>e.jsx("button",{onClick:()=>cr(r),className:`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap ${p===r?"bg-primary-500 text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:e.jsxs("span",{className:"flex items-center gap-1.5",children:[e.jsx(ur,{className:"text-xs"}),r]})},r))}),ne<j.length&&e.jsx("div",{className:"relative flex-shrink-0",children:e.jsx("button",{ref:qr,onClick:r=>{const a=r.currentTarget.getBoundingClientRect();et({top:a.bottom+window.scrollY+8,left:a.left+window.scrollX}),U(!R)},className:`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 border ${R?"bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 border-primary-200 dark:border-primary-700":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:e.jsxs("span",{className:"flex items-center gap-1.5",children:[e.jsx(Be,{className:`text-xs transition-transform duration-200 ${R?"rotate-45":""}`}),"+",j.length-ne]})})})]}),R&&ne<j.length&&e.jsx("div",{"data-dropdown":"categories",className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl min-w-[200px] max-h-80 overflow-hidden",style:{position:"fixed",top:`${jr.top}px`,left:`${jr.left}px`,zIndex:1e4,boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"},children:e.jsxs("div",{className:"p-3",children:[e.jsxs("div",{className:"text-xs font-semibold text-primary-600 dark:text-primary-400 px-3 py-2 bg-primary-50 dark:bg-primary-900/30 rounded-lg mb-2 text-center border border-primary-200 dark:border-primary-700",children:[e.jsx(je,{className:"inline-block ml-2 text-xs"}),"الفئات الإضافية (",j.length-ne,")"]}),e.jsx("div",{className:"space-y-1 max-h-60 overflow-y-auto modal-scrollbar",children:j.slice(ne).map(r=>e.jsxs("button",{onClick:()=>{cr(r),U(!1)},className:`w-full text-right px-3 py-2.5 text-sm rounded-lg transition-all duration-200 flex items-center gap-2 ${p===r?"bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 border border-primary-200 dark:border-primary-700":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50"}`,children:[e.jsx(ur,{className:"text-xs flex-shrink-0"}),e.jsx("span",{className:"flex-1 text-right",children:r}),p===r&&e.jsx(ve,{className:"text-xs text-primary-500"})]},r))})]})})]})}),j.length>0&&e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"h-8 w-px bg-gradient-to-b from-transparent via-gray-300 dark:via-gray-600 to-transparent"})}),e.jsx("div",{className:"flex-shrink-0",children:e.jsx(Yr,{id:"hide-zero-stock",checked:_,onChange:r=>G(r),label:"إخفاء المنتجات غير المتوفرة",className:"text-sm"})})]})})}),V.length>0&&!L&&!Ie&&!J&&!He&&e.jsxs("div",{className:"bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 p-2 rounded-xl mb-2 shadow-sm flex items-center animate-fadeIn",children:[e.jsx(Qe,{className:"ml-2 flex-shrink-0 text-primary-600 dark:text-primary-400"}),e.jsx("span",{className:"text-sm font-mono",children:V})]}),Fe&&e.jsxs("div",{className:"bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 p-3 rounded-xl mb-3 shadow-sm flex items-center animate-fadeIn",children:[e.jsx(te,{className:"ml-2 flex-shrink-0 text-danger-600 dark:text-danger-400"}),e.jsx("span",{className:"text-sm",children:Fe})]}),e.jsx("div",{ref:ze,className:"flex-1 overflow-y-auto overflow-x-hidden px-4 py-4 pr-6 custom-scrollbar-auto",onScroll:De,children:T&&!z?e.jsx("div",{className:"flex justify-center items-center h-full py-20",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"})}):F.length>0?e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 auto-rows-fr gap-4",children:[F.map(r=>e.jsx("div",{onClick:()=>r.quantity>0&&re(r),className:`product-card group ${r.quantity===0?"opacity-70 cursor-not-allowed":""}`,children:e.jsxs("div",{className:"product-card-inner",children:[e.jsx("div",{className:"absolute top-3 left-3 z-10",children:e.jsx("span",{className:`text-xs font-medium px-2.5 py-1 rounded-full shadow-sm ${r.quantity===0?"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400":r.quantity>r.min_quantity?"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400":"bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400"}`,children:r.quantity===0?"نفد":r.quantity>r.min_quantity?"متوفر":"قليل"})}),e.jsx("div",{className:"absolute top-3 right-3 z-10",children:e.jsx("span",{className:"text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2.5 py-1 rounded-full font-medium shadow-sm",children:r.unit})}),e.jsxs("div",{className:"flex flex-col h-full justify-between pt-8",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-bold text-base text-secondary-900 dark:text-secondary-100 line-clamp-2 mb-2",children:r.name}),r.description&&e.jsx("p",{className:"text-secondary-500 dark:text-secondary-400 text-xs line-clamp-2 mb-3",children:r.description})]}),e.jsx("div",{className:"mt-auto pt-3 border-t border-gray-100 dark:border-gray-700",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("span",{className:"text-primary-600 dark:text-primary-400 font-bold text-base",children:[r.price.toFixed(2)," د.ل"]}),e.jsxs("div",{className:"flex items-center gap-1.5 bg-gray-100 dark:bg-gray-700 px-2.5 py-1.5 rounded-lg",children:[e.jsx(Or,{className:"text-gray-400 dark:text-gray-500 text-xs"}),e.jsx("span",{className:"text-gray-700 dark:text-gray-300 text-xs font-medium",children:r.quantity})]})]})})]}),e.jsx("div",{className:"product-card-overlay",children:e.jsx("button",{className:`add-to-cart-btn ${r.quantity===0?"bg-gray-500 hover:bg-gray-600":""}`,children:r.quantity===0?e.jsxs(e.Fragment,{children:[e.jsx(te,{className:"text-xl"}),e.jsx("span",{children:"غير متوفر"})]}):e.jsxs(e.Fragment,{children:[e.jsx(Wr,{className:"text-xl"}),e.jsx("span",{children:"إضافة للسلة"})]})})})]})},r.id)),z&&e.jsx("div",{className:"col-span-full flex justify-center items-center py-8",children:e.jsxs("div",{className:"flex items-center gap-3 bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg border border-gray-200 dark:border-gray-700",children:[e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400 font-medium",children:"جاري تحميل المزيد من المنتجات..."})]})}),!Pe&&F.length>0&&!z&&e.jsx("div",{className:"col-span-full text-center py-8",children:e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 max-w-sm mx-auto",children:[e.jsx("div",{className:"flex items-center justify-center mb-3",children:e.jsx(ve,{className:"text-green-500 dark:text-green-400 text-xl"})}),e.jsx("h3",{className:"text-base font-semibold text-gray-800 dark:text-gray-200 mb-2",children:"تم تحميل جميع المنتجات"}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:["إجمالي: ",F.length," منتج",p&&e.jsxs("span",{className:"block text-xs mt-1",children:["في فئة: ",p]})]})]})})]}):e.jsxs("div",{className:"flex flex-col justify-center items-center h-full text-secondary-500 dark:text-secondary-400 py-20",children:[e.jsx("div",{className:"bg-gray-100 dark:bg-gray-700 p-6 rounded-full mb-4",children:e.jsx(ke,{className:"text-4xl text-gray-400 dark:text-gray-500"})}),e.jsx("p",{className:"text-lg mb-2",children:"لم يتم العثور على منتجات مطابقة"}),e.jsx("p",{className:"text-sm text-gray-400 dark:text-gray-500 text-center",children:"حاول تغيير معايير البحث أو تصفية الفئات"})]})})]}),e.jsxs("div",{className:"w-full md:w-1/3 bg-white dark:bg-gray-800 shadow-soft flex flex-col overflow-hidden order-1 md:order-2 md:border-r md:border-gray-200 dark:md:border-gray-700 h-full flex-1",children:[e.jsx("div",{className:"bg-primary-600 dark:bg-primary-700 text-white py-3 px-4 sticky top-0 z-10 shadow-lg border-b border-primary-500 dark:border-primary-600",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-white/15 p-2 rounded-lg ml-3 border border-white/15",children:e.jsx(hr,{className:"text-white text-base"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-bold text-white leading-tight",children:k?`تعديل فاتورة #${k.id}`:"سلة المبيعات"}),e.jsxs("div",{className:"flex items-center text-white/80 text-xs font-medium mt-0.5",children:[e.jsxs("span",{children:[dr," ",dr===1?"عنصر":"عناصر"]}),k&&e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"mx-1.5",children:"•"}),e.jsx("span",{className:"bg-orange-400/70 text-white px-1.5 py-0.5 rounded-full text-xs",children:"وضع التعديل"})]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-1.5",children:[k&&e.jsx("button",{onClick:()=>{Ue(null),pe()},className:"bg-red-500/80 hover:bg-red-500 text-white p-2 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95 border border-red-400/50",title:"إلغاء التعديل",children:e.jsx(te,{className:"text-sm"})}),!k&&e.jsx("button",{onClick:Ct,className:"bg-white/15 hover:bg-white/25 text-white p-2 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95 border border-white/15",title:"الفواتير السابقة",children:e.jsx(Ur,{className:"text-sm"})}),e.jsx("button",{onClick:()=>Oe(!J),className:"md:hidden bg-white/15 hover:bg-white/25 text-white p-2 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95 border border-white/15",title:"عرض المنتجات",children:J?e.jsx(te,{className:"text-sm"}):e.jsx(je,{className:"text-sm"})}),y.length>0&&!k&&e.jsx("button",{onClick:pe,className:"bg-red-400/60 hover:bg-red-500/70 text-white p-2 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95 border border-white/15",title:"إفراغ السلة",children:e.jsx(Vr,{className:"text-sm"})})]})]})}),e.jsx("div",{className:"flex-1 overflow-y-auto p-3 md:p-4 custom-scrollbar pr-2",children:y.length>0?e.jsx("div",{className:"space-y-4",children:y.map(r=>e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg border border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-500 transition-all duration-300",children:e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center flex-1 min-w-0",children:[e.jsx("div",{className:"bg-primary-50 dark:bg-primary-900/30 p-2.5 rounded-xl ml-3 flex-shrink-0 border border-primary-100 dark:border-primary-800",children:e.jsx(ke,{className:"text-primary-600 dark:text-primary-400 text-lg"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-bold text-gray-900 dark:text-gray-100 text-lg mb-1 truncate",children:r.name}),e.jsxs("div",{className:"flex items-center text-sm text-gray-600 dark:text-gray-300",children:[e.jsxs("span",{className:"bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-lg font-medium border border-green-200 dark:border-green-800",children:[r.price.toFixed(2)," د.ل"]}),e.jsx("span",{className:"mx-2 text-gray-400 dark:text-gray-500",children:"×"}),e.jsx("span",{className:"bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-2 py-1 rounded-lg font-medium border border-primary-200 dark:border-primary-800",children:r.cartQuantity})]})]})]}),e.jsx("div",{className:"text-left ml-4 flex-shrink-0",children:e.jsxs("div",{className:"bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 px-4 py-2 rounded-lg",children:[e.jsx("div",{className:"text-xs text-green-600 dark:text-green-300 font-medium mb-1",children:"المجموع"}),e.jsxs("div",{className:"text-green-700 dark:text-green-200 font-bold text-xl",children:[r.subtotal.toFixed(2)," د.ل"]})]})})]}),e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 ml-3",children:"الكمية:"}),e.jsxs("div",{className:"flex items-center bg-gray-50 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm",children:[e.jsx("button",{onClick:a=>{a.stopPropagation(),xr(r.id,r.cartQuantity-1)},className:"text-gray-600 dark:text-gray-400 hover:bg-red-50 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400 h-10 w-10 flex items-center justify-center transition-all duration-200",children:e.jsx(At,{className:"text-sm"})}),e.jsx("button",{onClick:a=>{a.stopPropagation(),ht(r)},className:"px-4 py-2 bg-white dark:bg-gray-700 border-x border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors cursor-pointer",title:"اضغط لتعديل الكمية يدوياً",children:e.jsx("span",{className:"font-bold text-gray-900 dark:text-gray-100 text-lg min-w-[2rem] text-center block",children:r.cartQuantity})}),e.jsx("button",{onClick:a=>{a.stopPropagation(),xr(r.id,r.cartQuantity+1)},className:"text-gray-600 dark:text-gray-400 hover:bg-green-50 dark:hover:bg-green-900/30 hover:text-green-600 dark:hover:text-green-400 h-10 w-10 flex items-center justify-center transition-all duration-200",children:e.jsx(Be,{className:"text-sm"})})]})]}),e.jsxs("button",{onClick:a=>{a.stopPropagation(),ut(r.id)},className:"bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/40 hover:text-red-700 dark:hover:text-red-300 px-4 py-2.5 rounded-xl transition-all duration-200 flex items-center gap-2 hover:shadow-md hover:scale-105 active:scale-95",children:[e.jsx(Vr,{className:"text-sm"}),e.jsx("span",{className:"text-sm font-medium",children:"حذف"})]})]})]})},r.id))}):e.jsxs("div",{className:"h-full flex flex-col justify-center items-center py-12",children:[e.jsx("div",{className:"bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 p-12 rounded-2xl mb-8 shadow-inner",children:e.jsx(hr,{className:"text-6xl text-gray-400 dark:text-gray-500"})}),e.jsx("h3",{className:"text-xl mb-3 font-bold text-gray-900 dark:text-gray-100",children:"السلة فارغة"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-center max-w-sm",children:"قم بإضافة منتجات من القائمة لبدء عملية البيع. يمكنك البحث عن المنتجات أو تصفح الفئات المختلفة."})]})}),e.jsxs("div",{className:"p-4 md:p-5 border-t border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 sticky bottom-0 left-0 right-0 z-10",children:[f&&e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-4 mb-4 border border-gray-200 dark:border-gray-700 shadow-sm",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/30 p-2 rounded-xl ml-3 border border-blue-100 dark:border-blue-800",children:e.jsx(Ce,{className:"text-blue-600 dark:text-blue-400 text-lg"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400 font-medium",children:"العميل المحدد"}),e.jsx("div",{className:"font-bold text-gray-900 dark:text-gray-100",children:f.name})]})]}),e.jsx("div",{className:"text-left",children:f.total_debt>0&&e.jsx("div",{className:"bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 px-3 py-1 rounded-lg",children:e.jsxs("div",{className:"text-red-700 dark:text-red-300 font-bold text-sm",children:["دين: ",f.total_debt.toFixed(2)," د.ل"]})})})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl p-4 mb-4 border border-gray-200 dark:border-gray-700 shadow-sm",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-primary-50 dark:bg-primary-900/30 p-2 rounded-xl ml-3 border border-primary-100 dark:border-primary-800",children:e.jsx(hr,{className:"text-primary-600 dark:text-primary-400 text-lg"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400 font-medium",children:"إجمالي السلة"}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-500",children:[y.length," عنصر"]})]})]}),e.jsx("div",{className:"text-left",children:e.jsx("div",{className:"bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 px-4 py-2 rounded-lg",children:e.jsxs("div",{className:"text-green-700 dark:text-green-200 font-bold text-2xl",children:[v.toFixed(2)," د.ل"]})})})]})}),e.jsx("button",{onClick:k?()=>X(!0):yt,disabled:y.length===0||T,className:`w-full py-4 text-lg rounded-xl font-bold transition-all duration-300 flex items-center justify-center gap-3 ${y.length===0||T?"bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed":k?"bg-gradient-to-r from-orange-400 to-orange-500 hover:from-orange-500 hover:to-orange-600 dark:from-orange-500 dark:to-orange-600 dark:hover:from-orange-600 dark:hover:to-orange-700 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]":"bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 dark:from-primary-600 dark:to-primary-700 dark:hover:from-primary-700 dark:hover:to-primary-800 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"}`,children:T?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin h-6 w-6 border-2 border-white border-t-transparent rounded-full"}),e.jsx("span",{children:"جاري المعالجة..."})]}):k?e.jsxs(e.Fragment,{children:[e.jsx(yr,{className:"text-xl"}),e.jsx("span",{children:"تحديث الفاتورة"})]}):e.jsxs(e.Fragment,{children:[e.jsx(br,{className:"text-xl"}),e.jsx("span",{children:"إتمام الدفع"})]})})]})]})]}),e.jsx(Ne,{isOpen:L&&!Se,onClose:()=>X(!1),title:"إتمام الدفع",size:"lg",zIndex:"highest",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 border border-gray-100 dark:border-gray-600",children:e.jsxs("div",{className:"space-y-1.5 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"إجمالي العناصر:"}),e.jsx("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:dr})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"المجموع الفرعي:"}),e.jsxs("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:[K.toFixed(2)," د.ل"]})]}),ee>0&&e.jsxs("div",{className:"flex justify-between text-success-600 dark:text-success-400",children:[e.jsx("span",{children:"الخصم:"}),e.jsxs("span",{children:["- ",ee.toFixed(2)," د.ل"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"المجموع بعد الخصم:"}),e.jsxs("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:[Ae.toFixed(2)," د.ل"]})]}),e.jsxs("div",{className:"flex justify-between text-warning-600 dark:text-warning-400",children:[e.jsxs("span",{children:["الضريبة (",Mr.toString(),"%):"]}),e.jsxs("span",{children:[ie>0?"+":""," ",ie.toFixed(2)," د.ل"]})]}),e.jsxs("div",{className:"flex justify-between font-bold text-base pt-1.5 border-t border-gray-200 dark:border-gray-600 mt-1.5",children:[e.jsx("span",{className:"text-gray-900 dark:text-gray-100",children:"المبلغ الإجمالي:"}),e.jsxs("span",{className:"text-primary-600 dark:text-primary-400",children:[v.toFixed(2)," د.ل"]})]})]})}),e.jsx("div",{children:e.jsx(Jr,{ref:qe,selectedCustomer:f,onCustomerSelect:Te,onAddCustomer:jt})}),e.jsx("div",{children:e.jsx(Lt,{paymentType:m,onPaymentTypeChange:ue,totalAmount:v,hasCustomer:!!f})}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100",children:"الخصم"}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-3",children:[e.jsx("button",{onClick:()=>{xe("fixed")},className:`flex items-center justify-center gap-2 p-2.5 rounded-lg border-2 transition-all duration-300 text-sm ${C==="fixed"?"border-success-500 bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300 shadow-sm":"border-gray-200 dark:border-gray-600 hover:border-success-300 dark:hover:border-success-600 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:e.jsx("span",{className:"font-medium",children:"مبلغ ثابت"})}),e.jsx("button",{onClick:()=>{xe("percentage")},className:`flex items-center justify-center gap-2 p-2.5 rounded-lg border-2 transition-all duration-300 text-sm ${C==="percentage"?"border-success-500 bg-success-50 dark:bg-success-900/30 text-success-700 dark:text-success-300 shadow-sm":"border-gray-200 dark:border-gray-600 hover:border-success-300 dark:hover:border-success-600 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:e.jsx("span",{className:"font-medium",children:"نسبة مئوية %"})})]}),e.jsx(Xr,{name:"discountAmount",value:se,onChange:r=>{const a=typeof r=="string"?parseFloat(r):r;C==="percentage"?a<=100&&le(a.toString()):a<=K&&le(a.toString())},min:0,max:C==="percentage"?100:K,step:.01,precision:2,placeholder:C==="percentage"?"نسبة الخصم":"مبلغ الخصم",currency:C==="percentage"?void 0:"د.ل",showControls:!0,icon:C==="percentage"?e.jsx("span",{className:"text-lg",children:"%"}):e.jsx(Gr,{})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block mb-2 text-sm font-medium text-gray-900 dark:text-gray-100",children:"طريقة الدفع"}),m==="credit"?e.jsxs("div",{className:"p-3 rounded-lg border-2 border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-700 text-center",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400",children:[e.jsx(pr,{className:"text-lg"}),e.jsx("span",{className:"font-medium",children:"آجل"})]}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"طريقة الدفع محددة تلقائياً للبيع الآجل"})]}):e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("button",{onClick:()=>W("cash"),className:`flex items-center justify-center gap-2 p-3 rounded-lg border-2 transition-all duration-300 text-sm ${q==="cash"?"border-primary-500 dark:border-primary-400 bg-primary-50 dark:bg-primary-900/40 text-primary-600 dark:text-primary-300 shadow-sm":"border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-500 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[e.jsx(br,{className:"text-lg"}),e.jsx("span",{className:"font-medium",children:"نقداً"})]}),e.jsxs("button",{onClick:()=>W("card"),className:`flex items-center justify-center gap-2 p-3 rounded-lg border-2 transition-all duration-300 text-sm ${q==="card"?"border-primary-500 dark:border-primary-400 bg-primary-50 dark:bg-primary-900/40 text-primary-600 dark:text-primary-300 shadow-sm":"border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-500 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[e.jsx(Rt,{className:"text-lg"}),e.jsx("span",{className:"font-medium",children:"بطاقة"})]})]})]}),e.jsxs("div",{children:[e.jsx(Xr,{name:"amountPaid",label:m==="full"?"المبلغ المستلم":m==="partial"?"المبلغ المدفوع (جزئي)":"المبلغ المدفوع (اختياري)",value:m==="credit"?"0":M,onChange:r=>{m!=="credit"&&A(r.toString())},min:m==="credit"?0:m==="partial"?.01:v,max:m==="partial"?v-.01:void 0,step:.01,precision:2,placeholder:m==="full"?"أدخل المبلغ المستلم":m==="partial"?"أدخل المبلغ المدفوع":m==="credit"?"لا يوجد دفع مقدم للبيع الآجل":"أدخل المبلغ (اختياري)",currency:"د.ل",showControls:m!=="credit",disabled:m==="credit",icon:m==="credit"?e.jsx(pr,{}):e.jsx(br,{})}),m==="full"&&q==="cash"&&e.jsxs("div",{className:"flex justify-between bg-success-50 dark:bg-success-900/30 p-2.5 rounded-lg text-success-900 dark:text-success-300 border border-success-100 dark:border-success-800 mt-2 text-sm",children:[e.jsx("span",{className:"font-medium",children:"المتبقي:"}),e.jsxs("span",{className:"font-bold",children:[ir.toFixed(2)," د.ل"]})]}),m==="partial"&&e.jsxs("div",{className:"flex justify-between bg-orange-50 dark:bg-orange-900/30 p-2.5 rounded-lg text-orange-900 dark:text-orange-300 border border-orange-100 dark:border-orange-800 mt-2 text-sm",children:[e.jsx("span",{className:"font-medium",children:"المبلغ المتبقي (دين):"}),e.jsxs("span",{className:"font-bold",children:[(v-parseFloat(M||"0")).toFixed(2)," د.ل"]})]}),m==="credit"&&e.jsxs("div",{className:"flex justify-between bg-red-50 dark:bg-red-900/30 p-2.5 rounded-lg text-red-900 dark:text-red-300 border border-red-100 dark:border-red-800 mt-2 text-sm",children:[e.jsx("span",{className:"font-medium",children:"إجمالي الدين:"}),e.jsxs("span",{className:"font-bold",children:[v.toFixed(2)," د.ل"]})]})]}),e.jsx("div",{className:"pt-3 border-t border-gray-200 dark:border-gray-700",children:e.jsx("button",{onClick:k?St:bt,disabled:T,className:`w-full py-3 text-base rounded-lg transition-all duration-300 font-medium ${T?"bg-primary-400 dark:bg-primary-500 cursor-not-allowed":k?"bg-orange-500 hover:bg-orange-600 active:bg-orange-700 dark:bg-orange-600 dark:hover:bg-orange-700 dark:active:bg-orange-800 shadow-sm hover:shadow-md":"bg-primary-500 hover:bg-primary-600 active:bg-primary-700 dark:bg-primary-600 dark:hover:bg-primary-700 dark:active:bg-primary-800 shadow-sm hover:shadow-md"} text-white`,children:e.jsx("div",{className:"flex items-center justify-center",children:T?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full ml-2"}),e.jsx("span",{children:k?"جاري تحديث الفاتورة...":"جاري تسجيل البيع..."})]}):e.jsxs(e.Fragment,{children:[k?e.jsx(yr,{className:"ml-2 text-sm"}):e.jsx(ve,{className:"ml-2 text-sm"}),e.jsx("span",{children:k?"تحديث الفاتورة":"تأكيد الدفع"})]})})})})]})}),e.jsx(Ne,{isOpen:Se,onClose:()=>{},title:"",size:"md",zIndex:"highest",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto mb-3 w-12 h-12 bg-success-100 dark:bg-success-900/30 rounded-full flex items-center justify-center",children:e.jsx(ve,{className:"text-xl text-success-600 dark:text-success-400"})}),e.jsx("h3",{className:"text-lg font-bold text-gray-900 dark:text-white mb-3",children:"تمت العملية بنجاح"}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-3",children:[e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 mb-1",children:"رقم الفاتورة"}),e.jsxs("div",{className:"text-base font-bold text-primary-600 dark:text-primary-400",children:["#",me]}),f&&m!=="full"&&e.jsxs("div",{className:"mt-1 text-xs text-gray-600 dark:text-gray-400",children:[m==="partial"?"دفع جزئي":"بيع آجل"," - ",f.name]})]}),m==="full"&&q==="cash"&&ir>0&&e.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-2.5 mb-3",children:e.jsxs("div",{className:"flex justify-between items-center text-xs",children:[e.jsx("span",{className:"text-blue-700 dark:text-blue-300",children:"الباقي للعميل:"}),e.jsxs("span",{className:"font-bold text-blue-800 dark:text-blue-200",children:[ir.toFixed(2)," د.ل"]})]})}),m!=="full"&&f&&e.jsx("div",{className:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-2.5 mb-3",children:e.jsxs("div",{className:"flex justify-between items-center text-xs",children:[e.jsx("span",{className:"text-orange-700 dark:text-orange-300",children:"المبلغ المتبقي:"}),e.jsxs("span",{className:"font-bold text-orange-800 dark:text-orange-200",children:[(v-parseFloat(M||"0")).toFixed(2)," د.ل"]})]})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("button",{onClick:pt,className:"w-full py-2.5 flex items-center justify-center gap-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 text-sm",children:[e.jsx(Kr,{className:"text-xs"}),e.jsx("span",{children:"طباعة الفاتورة"})]}),e.jsxs("button",{onClick:ft,className:"w-full py-2.5 flex items-center justify-center gap-2 bg-primary-500 hover:bg-primary-600 text-white font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md text-sm",children:[e.jsx(Be,{className:"text-xs"}),e.jsx("span",{children:"عملية جديدة"})]})]})]})}),e.jsx(Ne,{isOpen:tt,onClose:mr,title:"تعديل الكمية",size:"sm",zIndex:"highest",children:e.jsx("div",{className:"space-y-4",children:H&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/30 p-2 rounded-lg",children:e.jsx(ke,{className:"text-primary-600 dark:text-primary-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100",children:H.name}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["السعر: ",H.price.toFixed(2)," د.ل"]})]})]})}),e.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3 border border-blue-100 dark:border-blue-800",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-blue-700 dark:text-blue-300 font-medium",children:"الكمية المتاحة:"}),e.jsxs("span",{className:"text-blue-800 dark:text-blue-200 font-bold",children:[H.quantity," قطعة"]})]})}),e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 border border-gray-100 dark:border-gray-600",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-700 dark:text-gray-300 font-medium",children:"الكمية الحالية:"}),e.jsxs("span",{className:"text-gray-800 dark:text-gray-200 font-bold",children:[H.cartQuantity," قطعة"]})]})}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"الكمية الجديدة"}),e.jsx("input",{type:"number",min:"1",max:H.quantity,value:_r,onChange:r=>$e(r.target.value),onKeyDown:r=>{r.key==="Enter"?Dr():r.key==="Escape"&&mr()},className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-center text-lg font-bold focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"أدخل الكمية",autoFocus:!0}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1 text-center",children:["الحد الأقصى: ",H.quantity," قطعة"]})]}),e.jsxs("div",{className:"flex gap-3 pt-2",children:[e.jsx("button",{onClick:mr,className:"flex-1 py-3 px-4 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:"إلغاء"}),e.jsx("button",{onClick:Dr,className:"flex-1 py-3 px-4 bg-primary-500 hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-lg font-medium transition-colors",children:"تأكيد"})]})]})})}),Ie&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden",children:[e.jsxs("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center",children:[e.jsx(Ur,{className:"ml-3 text-primary-600 dark:text-primary-400"}),"الفواتير السابقة"]}),(O==null?void 0:O.role)==="cashier"&&e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"عرض فواتيرك الخاصة فقط"}),(O==null?void 0:O.role)==="admin"&&e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"عرض جميع الفواتير"})]}),e.jsx("button",{onClick:()=>We(!1),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700",children:e.jsx(te,{className:"text-xl"})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:de,onChange:r=>Ze(r.target.value),onKeyDown:r=>r.key==="Enter"&&Br(),placeholder:"ابحث برقم الفاتورة أو اسم العميل...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent"}),e.jsx(we,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"})]})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:Br,className:"px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg font-medium flex items-center gap-2 transition-colors",children:[e.jsx(we,{className:"text-sm"}),"بحث"]}),de&&e.jsxs("button",{onClick:wt,className:"px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg font-medium flex items-center gap-2 transition-colors",children:[e.jsx(te,{className:"text-sm"}),"مسح"]})]})]})]}),e.jsx("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)] modal-scrollbar",onScroll:r=>{const{scrollTop:a,scrollHeight:s,clientHeight:n}=r.currentTarget;s-a-n<100&&vt()},children:Ke&&_e.length===0?e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin h-8 w-8 border-2 border-primary-500 border-t-transparent rounded-full ml-3"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"جاري تحميل الفواتير..."})]}):_e.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(gr,{className:"text-4xl text-gray-400 dark:text-gray-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-lg",children:"لا توجد فواتير سابقة"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-500 text-sm mt-2",children:"ستظهر الفواتير هنا بعد إتمام عمليات البيع"})]}):e.jsxs("div",{className:"space-y-4",children:[_e.map(r=>{var a;return e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-all duration-200",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3",children:e.jsx(gr,{className:"text-lg"})}),e.jsxs("div",{children:[e.jsxs("h4",{className:"font-semibold text-gray-900 dark:text-white",children:["فاتورة رقم #",r.id]}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[new Date(r.created_at).toLocaleDateString("ar-LY")," - ",new Date(r.created_at).toLocaleTimeString("ar-LY",{hour:"2-digit",minute:"2-digit"})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-bold text-lg text-gray-900 dark:text-white",children:[((r.total_amount||0)-(r.discount_amount||0)+(r.tax_amount||0)).toFixed(2)," د.ل"]}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-medium rounded-full ${r.payment_status==="paid"?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":r.payment_status==="partial"?"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"}`,children:r.payment_status==="paid"?"مدفوع":r.payment_status==="partial"?"مدفوع جزئياً":"غير مدفوع"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"العميل:"}),e.jsx("p",{className:"font-medium text-gray-900 dark:text-white",children:r.customer_name||"عميل مباشر"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"طريقة الدفع:"}),e.jsx("p",{className:"font-medium text-gray-900 dark:text-white",children:r.payment_method||"نقدي"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"العناصر:"}),e.jsxs("p",{className:"font-medium text-gray-900 dark:text-white",children:[((a=r.items)==null?void 0:a.length)||0," عنصر"]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"المبلغ المدفوع:"}),e.jsxs("p",{className:"font-medium text-gray-900 dark:text-white",children:[(r.amount_paid||0).toFixed(2)," د.ل"]})]})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsxs("button",{onClick:()=>Nt(r),className:"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2 transition-colors",children:[e.jsx(yr,{className:"text-xs"}),"تعديل"]}),e.jsxs("button",{onClick:()=>sr(`/sales/${r.id}/print`),className:"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2 transition-colors",children:[e.jsx(Kr,{className:"text-xs"}),"طباعة"]})]})]},r.id)}),Ye&&e.jsxs("div",{className:"flex items-center justify-center py-6",children:[e.jsx("div",{className:"animate-spin h-6 w-6 border-2 border-primary-500 border-t-transparent rounded-full ml-2"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"جاري تحميل المزيد..."})]}),!Xe&&_e.length>0&&e.jsx("div",{className:"text-center py-4",children:e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"تم عرض جميع الفواتير"})})]})})]})}),e.jsx(Ne,{isOpen:at,onClose:Qr,title:"إضافة عميل جديد",size:"md",zIndex:"highest",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(Zr,{name:"customerName",label:"اسم العميل",value:he,onChange:rr,placeholder:"أدخل اسم العميل",required:!0,autoFocus:!0,icon:e.jsx(Ce,{})}),e.jsx(Zr,{name:"customerPhone",label:"رقم الهاتف",type:"tel",value:Pr,onChange:tr,placeholder:"أدخل رقم الهاتف (اختياري)",icon:e.jsx(zt,{})}),e.jsxs("div",{className:"flex gap-3 pt-4",children:[e.jsx("button",{onClick:Qr,className:"flex-1 py-2.5 px-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 font-medium text-sm",children:"إلغاء"}),e.jsx("button",{onClick:kt,disabled:ar||!he.trim(),className:`flex-1 py-2.5 px-4 rounded-lg font-medium text-sm transition-all duration-200 ${ar||!he.trim()?"bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed":"bg-primary-500 hover:bg-primary-600 text-white shadow-sm hover:shadow-md"}`,children:ar?e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full ml-2"}),e.jsx("span",{children:"جاري الإضافة..."})]}):"إضافة العميل"})]})]})})]})};export{Wt as default};
