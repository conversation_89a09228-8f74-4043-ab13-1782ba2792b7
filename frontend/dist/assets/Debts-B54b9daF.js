import{a as Ie,u as b,i as Ue,r as n,j as e}from"./index-4-jyR6ws.js";import{O as ze,k as i,o as Be,B as re,I as te,s as se,a0 as qe,g as U,y as ne,m as le,W as C,C as M,T as Re,a1 as Je,a2 as We}from"./index-yc4-8FIA.js";import{M as de}from"./Modal-Df3FNVJO.js";import{D as Ye,S as Ke}from"./SuccessModal-DrxHshjl.js";import{T as Ge}from"./ToggleSwitch-CZWahUnf.js";import{S as ie}from"./SelectBox-CMEL6VH_.js";import{S as oe,N as ce,a as me}from"./BarcodeInput-C6WLMcyO.js";const ta=()=>{var ee,ae;const xe=Ie(),{user:z}=b(),[ge]=Ue(),B=ge.get("customer_id"),[pe,q]=n.useState([]),[R,he]=n.useState([]),[o,J]=n.useState(!0),[f,W]=n.useState(""),[Y,K]=n.useState(!0),[j,G]=n.useState(B?parseInt(B):null),[ye,k]=n.useState(!1),[ue,D]=n.useState(!1),[be,A]=n.useState(!1),[je,F]=n.useState(!1),[fe,T]=n.useState(""),[y,H]=n.useState(null),[l,ke]=n.useState(null),[N,Ne]=n.useState(null),[c,v]=n.useState({customer_id:0,amount:0,description:""}),[m,$]=n.useState({amount:0,payment_method:"cash",notes:""}),[p,u]=n.useState({}),[O,ve]=n.useState(!1),[x,Q]=n.useState(10),[V,h]=n.useState(1),[r,we]=n.useState({total:0,page:1,limit:10,pages:1}),[_,X]=n.useState({totalDebts:0,unpaidDebts:0,totalAmount:0,unpaidAmount:0});n.useEffect(()=>{if(!z){window.location.href="/login";return}Ce(),d(),Se()},[z]);const Se=async()=>{try{const{token:a}=b.getState(),t=window.location.hostname==="localhost"?"http://localhost:8002":`http://${window.location.hostname}:8002`,s=await fetch(`${t}/api/debts/stats`,{headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}});if(s.ok){const g=await s.json();X(g)}}catch{X({totalDebts:0,unpaidDebts:0,totalAmount:0,unpaidAmount:0})}},Ce=async()=>{try{const{token:a}=b.getState(),t=window.location.hostname==="localhost"?"http://localhost:8002":`http://${window.location.hostname}:8002`,s=await fetch(`${t}/api/customers?active_only=true`,{headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}});if(s.ok){const g=await s.json();he(g)}}catch{}},d=async(a=V,t=x)=>{try{J(!0);const{token:s}=b.getState();let P=`${window.location.hostname==="localhost"?"http://localhost:8002":`http://${window.location.hostname}:8002`}/api/debts?unpaid_only=${Y}&skip=${(a-1)*t}&limit=${t}`;j&&(P+=`&customer_id=${j}`),f&&(P+=`&search=${encodeURIComponent(f)}`);const w=await fetch(P,{headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(w.ok){let E=await w.json(),I;if(w.headers.get("x-total-count")){const S=parseInt(w.headers.get("x-total-count")||"0");I={total:S,page:a,limit:t,pages:Math.ceil(S/t)},q(E)}else{const S=E.length;I={total:S,page:a,limit:t,pages:Math.ceil(S/t)},q(E)}we(I),h(a)}}catch{}finally{J(!1)}},Z=()=>{h(1),d(1,x)},De=()=>{h(1),d(1,x)},Fe=()=>{W(""),G(null),K(!0),Q(10),h(1),d(1,10)},$e=()=>{H(null),v({customer_id:j||0,amount:0,description:""}),u({}),k(!0)},_e=a=>{H(a),v({customer_id:a.customer_id,amount:a.amount,description:a.description||""}),u({}),k(!0)},Pe=a=>{ke(a),$({amount:a.remaining_amount,payment_method:"cash",notes:""}),u({}),D(!0)},Me=a=>{Ne(a),A(!0)},Ae=()=>{const a={};return c.customer_id||(a.customer_id="يجب اختيار العميل"),(!c.amount||c.amount<=0)&&(a.amount="يجب إدخال مبلغ صحيح"),u(a),Object.keys(a).length===0},Te=()=>{const a={};return(!m.amount||m.amount<=0)&&(a.amount="يجب إدخال مبلغ صحيح"),l&&m.amount>l.remaining_amount&&(a.amount="المبلغ أكبر من المبلغ المتبقي"),u(a),Object.keys(a).length===0},Oe=async a=>{if(a.preventDefault(),!!Ae())try{const{token:t}=b.getState(),s=window.location.hostname==="localhost"?"http://localhost:8002":`http://${window.location.hostname}:8002`,g=y?`${s}/api/debts/${y.id}`:`${s}/api/debts`;(await fetch(g,{method:y?"PUT":"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify(c)})).ok&&(k(!1),T(y?"تم تحديث المديونية بنجاح":"تم إضافة المديونية بنجاح"),F(!0),d())}catch{}},Le=async a=>{if(a.preventDefault(),!(!Te()||!l))try{const{token:t}=b.getState(),s=window.location.hostname==="localhost"?"http://localhost:8002":`http://${window.location.hostname}:8002`;(await fetch(`${s}/api/debts/${l.id}/payments`,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({debt_id:l.id,...m})})).ok&&(D(!1),T("تم تسجيل الدفعة بنجاح"),F(!0),d())}catch{}},Ee=async()=>{if(N)try{const{token:a}=b.getState(),t=window.location.hostname==="localhost"?"http://localhost:8002":`http://${window.location.hostname}:8002`;(await fetch(`${t}/api/debts/${N.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${a}`}})).ok&&(A(!1),T("تم حذف المديونية بنجاح"),F(!0),d())}catch{}},L=pe.filter(a=>{var s,g;return(((s=a.customer)==null?void 0:s.name)||"").toLowerCase().includes(f.toLowerCase())||((g=a.description)==null?void 0:g.toLowerCase().includes(f.toLowerCase()))});return o?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"})}):e.jsxs("div",{className:"container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx("button",{onClick:()=>xe("/"),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2.5 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm flex-shrink-0",title:"العودة للرئيسية",children:e.jsx(ze,{className:"text-sm"})}),e.jsxs("div",{className:"mr-3 sm:mr-4 min-w-0 flex-1",children:[e.jsxs("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center",children:[e.jsx(i,{className:"ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:"إدارة المديونية"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block",children:"إدارة وتتبع مديونيات العملاء"})]})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap",children:[e.jsx("button",{onClick:()=>d(V,x),className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"تحديث البيانات",children:e.jsx(Be,{className:`text-sm ${o?"animate-spin":""}`})}),e.jsxs("button",{onClick:$e,className:"bg-primary-600 text-white rounded-lg py-2.5 px-3 sm:px-4 lg:px-5 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg font-medium",children:[e.jsx(re,{className:"ml-2 text-sm"}),e.jsx("span",{className:"hidden sm:inline lg:inline",children:"إضافة مديونية جديدة"}),e.jsx("span",{className:"sm:hidden lg:hidden",children:"إضافة"})]})]})]})})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(i,{className:"text-primary-600 dark:text-primary-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-primary-700 dark:text-primary-300 mb-1",children:"إجمالي المديونيات"}),e.jsx("div",{className:"text-xl font-bold text-primary-600 dark:text-primary-400",children:_.totalDebts})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30",children:[e.jsx("div",{className:"bg-red-100 dark:bg-red-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(te,{className:"text-red-600 dark:text-red-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-red-700 dark:text-red-300 mb-1",children:"غير مدفوعة"}),e.jsx("div",{className:"text-xl font-bold text-red-600 dark:text-red-400",children:_.unpaidDebts})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30",children:[e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(i,{className:"text-blue-600 dark:text-blue-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-blue-700 dark:text-blue-300 mb-1",children:"إجمالي المبلغ"}),e.jsxs("div",{className:"text-lg font-bold text-blue-600 dark:text-blue-400 truncate",children:[_.totalAmount.toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-blue-500 dark:text-blue-400",children:"المبلغ الكلي"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-100 dark:border-orange-800/30",children:[e.jsx("div",{className:"bg-orange-100 dark:bg-orange-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(i,{className:"text-orange-600 dark:text-orange-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-orange-700 dark:text-orange-300 mb-1",children:"المبلغ غير المدفوع"}),e.jsxs("div",{className:"text-lg font-bold text-orange-600 dark:text-orange-400 truncate",children:[_.unpaidAmount.toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-orange-500 dark:text-orange-400",children:"المبلغ المستحق"})]})]})]})}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(se,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"}),e.jsx("input",{type:"text",placeholder:"البحث بالعميل أو الوصف...",value:f,onChange:a=>W(a.target.value),onKeyDown:a=>a.key==="Enter"&&Z(),className:"w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"})]})}),e.jsxs("div",{className:"flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>ve(!O),className:`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${O?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(qe,{className:"ml-2"}),"فلاتر"]}),e.jsxs("button",{onClick:Z,className:"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 hover:bg-primary-200 dark:hover:bg-primary-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center",children:[e.jsx(se,{className:"ml-2"}),"بحث"]})]})]}),O&&e.jsx("div",{className:"mt-4 p-6 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 xl:grid-cols-6 gap-6",children:[e.jsx("div",{className:"sm:col-span-1 lg:col-span-2 xl:col-span-2",children:e.jsx(ie,{label:"العميل",name:"customer",value:(j==null?void 0:j.toString())||"",onChange:a=>G(a?parseInt(a):null),options:[{value:"",label:"جميع العملاء"},...R.map(a=>({value:a.id.toString(),label:a.name}))]})}),e.jsxs("div",{className:"sm:col-span-1 lg:col-span-1 xl:col-span-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"حالة الدفع"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center justify-start",children:e.jsx(Ge,{id:"unpaidOnly",checked:Y,onChange:a=>K(a),label:"غير المدفوعة فقط"})})]}),e.jsx("div",{className:"sm:col-span-1 lg:col-span-1 xl:col-span-1",children:e.jsx(ie,{label:"عدد العناصر",name:"itemsPerPage",value:x.toString(),onChange:a=>Q(parseInt(a,10)),options:[{value:"10",label:"10"},{value:"20",label:"20"},{value:"30",label:"30"},{value:"50",label:"50"}]})}),e.jsxs("div",{className:"sm:col-span-2 lg:col-span-1 xl:col-span-1 flex flex-col gap-3",children:[e.jsx("button",{type:"button",onClick:Fe,className:"bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-4 py-2.5 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm font-medium",children:"إعادة تعيين"}),e.jsx("button",{type:"button",onClick:De,className:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2.5 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm font-medium",children:"تطبيق الفلاتر"})]})]})})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden border border-gray-200 dark:border-gray-700",children:[L.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(i,{className:"mx-auto text-4xl text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"لا توجد مديونيات"})]}):e.jsx("div",{className:"overflow-x-auto custom-scrollbar-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-700",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"العميل"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المبلغ الإجمالي"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المبلغ المتبقي"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الوصف"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"التاريخ"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الحالة"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الإجراءات"})]})}),e.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:L.map(a=>{var t;return e.jsxs("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.jsx("div",{className:"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center",children:e.jsx(U,{className:"text-primary-600 dark:text-primary-400"})})}),e.jsxs("div",{className:"mr-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:((t=a.customer)==null?void 0:t.name)||"عميل غير معروف"}),e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["#",a.id]})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(i,{className:"text-blue-500 ml-2"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:[a.amount.toFixed(2)," د.ل"]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(i,{className:`ml-2 ${a.remaining_amount>0?"text-red-500":"text-green-500"}`}),e.jsxs("span",{className:`text-sm font-medium ${a.remaining_amount>0?"text-red-600 dark:text-red-400":"text-green-600 dark:text-green-400"}`,children:[a.remaining_amount.toFixed(2)," د.ل"]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:a.description||"-"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(ne,{className:"text-gray-400 ml-2"}),e.jsx("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:new Date(a.created_at).toLocaleDateString("ar-LY")})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${a.is_paid?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"}`,children:a.is_paid?e.jsxs(e.Fragment,{children:[e.jsx(le,{className:"ml-1"}),"مدفوعة"]}):e.jsxs(e.Fragment,{children:[e.jsx(te,{className:"ml-1"}),"غير مدفوعة"]})})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex items-center gap-2",children:[!a.is_paid&&e.jsx("button",{onClick:()=>Pe(a),className:"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300",title:"إضافة دفعة",children:e.jsx(C,{})}),e.jsx("button",{onClick:()=>_e(a),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300",title:"تعديل",children:e.jsx(M,{})}),e.jsx("button",{onClick:()=>Me(a),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300",title:"حذف",children:e.jsx(Re,{})})]})})]},a.id)})})]})}),!o&&L.length>0&&e.jsxs("div",{className:"px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex-1 flex justify-between items-center sm:hidden",children:[e.jsx("button",{onClick:()=>{const a=Math.max(1,r.page-1);h(a),d(a,x)},disabled:r.page<=1,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${r.page<=1?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:"السابق"}),e.jsx("button",{onClick:()=>{const a=Math.min(r.pages,r.page+1);h(a),d(a,x)},disabled:r.page>=r.pages,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${r.page>=r.pages?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:"التالي"})]}),e.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:[e.jsx("span",{className:"font-medium",children:r.page})," / ",e.jsx("span",{className:"font-medium",children:r.pages})," ",e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["(الإجمالي: ",r.total,")"]})]})}),e.jsx("div",{children:e.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse","aria-label":"Pagination",children:[e.jsxs("button",{onClick:()=>{const a=Math.max(1,r.page-1);h(a),d(a,x)},disabled:r.page<=1,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${r.page<=1?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"sr-only",children:"السابق"}),e.jsx(Je,{className:"h-5 w-5"})]}),Array.from({length:Math.min(5,r.pages)},(a,t)=>{let s;return r.pages<=5||r.page<=3?s=t+1:r.page>=r.pages-2?s=r.pages-4+t:s=r.page-2+t,e.jsx("button",{onClick:()=>{h(s),d(s,x)},className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${r.page===s?"z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:s},s)}),e.jsxs("button",{onClick:()=>{const a=Math.min(r.pages,r.page+1);h(a),d(a,x)},disabled:r.page>=r.pages,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${r.page>=r.pages?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"sr-only",children:"التالي"}),e.jsx(We,{className:"h-5 w-5"})]})]})})]})]})]}),e.jsx(de,{isOpen:ye,onClose:()=>k(!1),title:y?"تعديل المديونية":"إضافة مديونية جديدة",size:"lg",children:e.jsxs("form",{onSubmit:Oe,className:"space-y-5",children:[e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[e.jsx(i,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"معلومات المديونية"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsx(oe,{label:"العميل",name:"customer_id",value:c.customer_id.toString(),onChange:a=>{v({...c,customer_id:parseInt(a)}),p.customer_id&&u({...p,customer_id:void 0})},options:[{value:"0",label:"اختر العميل"},...R.map(a=>({value:a.id.toString(),label:a.name}))],placeholder:"اختر العميل",required:!0,disabled:!!y,error:p.customer_id,icon:e.jsx(U,{})}),e.jsx(ce,{label:"المبلغ",name:"amount",value:c.amount.toString(),onChange:a=>{v({...c,amount:parseFloat(a)||0}),p.amount&&u({...p,amount:void 0})},placeholder:"أدخل مبلغ المديونية",required:!0,min:0,step:"0.01",currency:"د.ل",error:p.amount,icon:e.jsx(i,{}),dir:"ltr"})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[e.jsx(M,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"تفاصيل إضافية"]}),e.jsx(me,{label:"الوصف",name:"description",value:c.description,onChange:a=>v({...c,description:a}),placeholder:"أدخل وصف المديونية (اختياري)",rows:3})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsx("button",{type:"button",onClick:()=>k(!1),disabled:o,className:"btn-secondary flex items-center justify-center min-w-[120px]",children:e.jsx("span",{children:"إلغاء"})}),e.jsx("button",{type:"submit",disabled:o,className:"btn-primary flex items-center justify-center min-w-[140px]",children:o?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),e.jsx("span",{children:"جاري الحفظ..."})]}):e.jsxs(e.Fragment,{children:[y?e.jsx(M,{className:"ml-2"}):e.jsx(re,{className:"ml-2"}),e.jsx("span",{children:y?"تحديث المديونية":"إضافة المديونية"})]})})]})]})}),e.jsx(de,{isOpen:ue,onClose:()=>D(!1),title:"إضافة دفعة",size:"lg",children:e.jsxs("form",{onSubmit:Le,className:"space-y-5",children:[l&&e.jsxs("div",{className:"bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 p-4 rounded-lg border border-primary-200 dark:border-primary-800 mb-4",children:[e.jsxs("h3",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center",children:[e.jsx(i,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"تفاصيل المديونية"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(U,{className:"ml-2 text-gray-500 dark:text-gray-400"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"العميل:"}),e.jsx("span",{className:"mr-2 font-medium text-gray-900 dark:text-gray-100",children:(ee=l.customer)==null?void 0:ee.name})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(i,{className:"ml-2 text-gray-500 dark:text-gray-400"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"المبلغ الإجمالي:"}),e.jsxs("span",{className:"mr-2 font-medium text-gray-900 dark:text-gray-100",children:[l.amount.toFixed(2)," د.ل"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(C,{className:"ml-2 text-red-500 dark:text-red-400"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"المبلغ المتبقي:"}),e.jsxs("span",{className:"mr-2 font-medium text-red-600 dark:text-red-400",children:[l.remaining_amount.toFixed(2)," د.ل"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(ne,{className:"ml-2 text-gray-500 dark:text-gray-400"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"تاريخ الإنشاء:"}),e.jsx("span",{className:"mr-2 font-medium text-gray-900 dark:text-gray-100",children:new Date(l.created_at).toLocaleDateString("ar-LY")})]})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[e.jsx(C,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"معلومات الدفعة"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsx(ce,{label:"مبلغ الدفعة",name:"amount",value:m.amount.toString(),onChange:a=>{$({...m,amount:parseFloat(a)||0}),p.amount&&u({...p,amount:void 0})},placeholder:"أدخل مبلغ الدفعة",required:!0,min:0,max:l==null?void 0:l.remaining_amount,step:"0.01",currency:"د.ل",error:p.amount,icon:e.jsx(i,{}),dir:"ltr"}),e.jsx(oe,{label:"طريقة الدفع",name:"payment_method",value:m.payment_method,onChange:a=>$({...m,payment_method:a}),options:[{value:"cash",label:"نقدي",icon:e.jsx(i,{})},{value:"card",label:"بطاقة",icon:e.jsx(C,{})}],placeholder:"اختر طريقة الدفع",required:!0,icon:e.jsx(C,{})})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[e.jsx(M,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"ملاحظات إضافية"]}),e.jsx(me,{label:"ملاحظات",name:"notes",value:m.notes,onChange:a=>$({...m,notes:a}),placeholder:"أدخل ملاحظات الدفعة (اختياري)",rows:3})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsx("button",{type:"button",onClick:()=>D(!1),disabled:o,className:"btn-secondary flex items-center justify-center min-w-[120px]",children:e.jsx("span",{children:"إلغاء"})}),e.jsx("button",{type:"submit",disabled:o,className:"btn-primary flex items-center justify-center min-w-[140px]",children:o?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),e.jsx("span",{children:"جاري التسجيل..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(le,{className:"ml-2"}),e.jsx("span",{children:"تسجيل الدفعة"})]})})]})]})}),e.jsx(Ye,{isOpen:be,onClose:()=>A(!1),onConfirm:Ee,title:"تأكيد حذف المديونية",message:"هل أنت متأكد من رغبتك في حذف هذه المديونية؟",itemName:N?`مديونية ${((ae=N.customer)==null?void 0:ae.name)||"غير محدد"} - ${N.amount.toFixed(2)} د.ل`:"",isLoading:o}),e.jsx(Ke,{isOpen:je,onClose:()=>F(!1),title:"نجح العملية",message:fe})]})};export{ta as default};
