import{j as e,r as y,u as Ce,d as $e,a as Ae,b as M}from"./index-0NgsgVly.js";import{O as Me,j as J,o as se,a3 as Pe,a4 as Ie,e as Te,k as Ee,Y as ne,Z as Le,s as Re,a0 as Ue,a5 as He,a6 as ze,i as qe,D as de,T as ie,a1 as oe,a2 as le}from"./index-B7SL4KKz.js";import{D as ce}from"./DatePicker-Bwa9wxPB.js";import{S as B}from"./SelectBox-Dr23MlgE.js";const xe=({value:k,onChange:v,name:u,placeholder:S="0.00",className:P="",label:$,min:F,max:R,step:U="0.01",disabled:z=!1,dir:o="ltr"})=>{const w=p=>{v(p.target.value)};return e.jsxs("div",{className:"relative",children:[$&&e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:$}),e.jsx("div",{className:"relative",children:e.jsx("input",{type:"number",name:u,value:k,onChange:w,placeholder:S,min:F,max:R,step:U,disabled:z,dir:o,className:`w-full border border-gray-300 dark:border-gray-600 rounded-lg py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${z?"opacity-60 cursor-not-allowed":""} ${P}`})})]})},ue=k=>k?new Date(k):new Date,me=k=>{const v=ue(k),u=String(v.getDate()).padStart(2,"0"),S=String(v.getMonth()+1).padStart(2,"0"),P=v.getFullYear();return`${u}/${S}/${P}`},ge=k=>{const v=ue(k),u=String(v.getHours()).padStart(2,"0"),S=String(v.getMinutes()).padStart(2,"0");return`${u}:${S}`},H={startDate:"",endDate:"",minAmount:"",maxAmount:"",paymentMethod:"all",cashierId:"",customerId:"",page:1,limit:10},We=()=>{const[k,v]=y.useState([]),[u,S]=y.useState([]),[P,$]=y.useState(!0),[F,R]=y.useState(""),[U,z]=y.useState(!1),[o,w]=y.useState(H),[p,C]=y.useState(H),[pe,Y]=y.useState(!1),[i,he]=y.useState(null),[K,q]=y.useState(""),[Q,f]=y.useState(""),[s,T]=y.useState({page:1,limit:10,total:0,pages:1}),[E,O]=y.useState([]),[ye,X]=y.useState([]),[_,I]=y.useState([]),[W,V]=y.useState(!1),[be,L]=y.useState(!1),{user:d}=Ce();$e();const Z=Ae();y.useEffect(()=>{const t=async()=>{try{const r=await M.get("/api/auth/me");if(console.log("Current user:",r.data),r.data.role==="admin")try{const c=await M.get("/api/users/");console.log("All users:",c.data);const x=c.data.map(m=>({id:m.id,full_name:m.full_name||m.username||"User"}));O(x)}catch(c){console.error("Error fetching all users:",c),O([{id:r.data.id,full_name:r.data.full_name||r.data.username||"User"}])}else O([{id:r.data.id,full_name:r.data.full_name||r.data.username||"User"}])}catch(r){console.error("Error fetching current user:",r),O([{id:1,full_name:"Admin"}])}},a=async()=>{try{const r=await M.get("/api/customers/");console.log("Customers:",r.data);const c=r.data.map(x=>({id:x.id,name:x.name||"عميل غير معروف"}));X(c)}catch(r){console.error("Error fetching customers:",r),X([])}};t(),a(),G(),C(o)},[]),y.useEffect(()=>{console.log("Pagination changed:",o.page,o.limit);let t=[...k];if(o.startDate&&(t=t.filter(g=>{const N=new Date(g.created_at),h=new Date(o.startDate);return h.setHours(0,0,0,0),N>=h})),o.endDate&&(t=t.filter(g=>{const N=new Date(g.created_at),h=new Date(o.endDate);return h.setHours(23,59,59,999),N<=h})),o.minAmount&&(t=t.filter(g=>g.total_amount>=parseFloat(o.minAmount))),o.maxAmount&&(t=t.filter(g=>g.total_amount<=parseFloat(o.maxAmount))),o.paymentMethod!=="all"&&(t=t.filter(g=>g.payment_method===o.paymentMethod)),o.cashierId&&(t=t.filter(g=>{var N;return g.cashier_name&&g.cashier_name.includes(((N=E.find(h=>h.id.toString()===o.cashierId))==null?void 0:N.full_name)||"")})),o.customerId&&(t=t.filter(g=>g.customer_id&&g.customer_id.toString()===o.customerId)),F){const g=F.toLowerCase();t=t.filter(N=>N.id.toString().includes(F)||N.cashier_name&&N.cashier_name.toLowerCase().includes(g))}const a=t.length,r=o.page,c=o.limit,x=Math.max(1,Math.ceil(a/c)),m=(r-1)*c,b=m+c,l=t.slice(m,b),j={total:a,page:r,limit:c,pages:x};S(l),T(j)},[o,k,F,E]),y.useEffect(()=>{console.log("Current pagination state:",s)},[s]),y.useEffect(()=>{C(o)},[o]),y.useEffect(()=>{console.log("Current pagination state:",s)},[s]);const G=async()=>{$(!0);try{console.log("Fetching all sales data directly from database"),console.log("Sending request to API to get all sales data from database");const a=(await M.get("/api/auth/me")).data,r=a.role==="admin",c={all:"true",_t:new Date().getTime().toString()};r||(c.user_id=a.id);const x=await M.get("/api/sales/",{params:c});console.log("Raw API response data:",x.data);let m=x.data;console.log("Using data directly, length:",m.length);const b=m.map(n=>{console.log("Processing sale:",n);const D=n.total_amount+(n.tax_amount||0)-(n.discount_amount||0),A=n.amount_paid||0,De=Math.max(0,D-A);return{id:n.id,total_amount:typeof n.total_amount=="number"?n.total_amount:typeof n.total_amount=="string"?parseFloat(n.total_amount):0,payment_method:n.payment_method||"cash",amount_paid:typeof n.amount_paid=="number"?n.amount_paid:typeof n.amount_paid=="string"?parseFloat(n.amount_paid):0,payment_status:n.payment_status||"paid",change_amount:typeof n.change_amount=="number"?n.change_amount:typeof n.change_amount=="string"?parseFloat(n.change_amount):0,tax_amount:typeof n.tax_amount=="number"?n.tax_amount:typeof n.tax_amount=="string"?parseFloat(n.tax_amount):0,discount_amount:typeof n.discount_amount=="number"?n.discount_amount:typeof n.discount_amount=="string"?parseFloat(n.discount_amount):0,discount_type:n.discount_type||"fixed",created_at:n.created_at||new Date().toISOString(),items_count:typeof n.items_count=="number"?n.items_count:n.items&&Array.isArray(n.items)?n.items.length:0,debt_amount:De,customer_id:n.customer_id||null,customer_name:n.customer_name||(n.customer?n.customer.name:null),cashier_name:n.cashier_name||(n.user&&n.user.full_name?n.user.full_name:n.user_name||"")}});console.log("Formatted sales data:",b);let l=b.length,j=1,g=o.limit,N=Math.max(1,Math.ceil(l/g));console.log("Total count:",l),console.log("Current page:",j),console.log("Page limit:",g),console.log("Total pages:",N),v(b);let h=[...b];if(o.startDate&&(h=h.filter(n=>{const D=new Date(n.created_at),A=new Date(o.startDate);return A.setHours(0,0,0,0),D>=A})),o.endDate&&(h=h.filter(n=>{const D=new Date(n.created_at),A=new Date(o.endDate);return A.setHours(23,59,59,999),D<=A})),o.minAmount&&(h=h.filter(n=>n.total_amount>=parseFloat(o.minAmount))),o.maxAmount&&(h=h.filter(n=>n.total_amount<=parseFloat(o.maxAmount))),o.paymentMethod!=="all"&&(h=h.filter(n=>n.payment_method===o.paymentMethod)),o.cashierId&&(h=h.filter(n=>{var D;return n.cashier_name&&n.cashier_name.includes(((D=E.find(A=>A.id.toString()===o.cashierId))==null?void 0:D.full_name)||"")})),o.customerId&&(h=h.filter(n=>n.customer_id&&n.customer_id.toString()===o.customerId)),F){const n=F.toLowerCase();h=h.filter(D=>D.id.toString().includes(F)||D.cashier_name&&D.cashier_name.toLowerCase().includes(n))}l=h.length,j=o.page,g=o.limit,N=Math.max(1,Math.ceil(l/g));const ae=(j-1)*g,Se=ae+g,Fe=h.slice(ae,Se),re={total:l,page:j,limit:g,pages:N};console.log("Filtered data count:",l),console.log("Current page:",j),console.log("Page limit:",g),console.log("Total pages:",N),console.log("Pagination info:",re),S(Fe),T(re),$(!1)}catch(t){console.error("Error fetching sales:",t),f("فشل في تحميل المبيعات. يرجى المحاولة مرة أخرى."),v([]),S([]),$(!1),setTimeout(()=>f(""),3e3)}};y.useEffect(()=>{const t=setTimeout(()=>{console.log("Search term changed to:",F),w({...o,page:1})},500);return()=>clearTimeout(t)},[F]);const ee=()=>{console.log("Resetting all filters to initial state"),w(H),C(H),R("");let t=k.length,a=H.limit,r=Math.max(1,Math.ceil(t/a));const c=k.slice(0,a),x={total:t,page:1,limit:a,pages:r};S(c),T(x)},fe=async t=>{try{console.log(`Fetching details for sale ID: ${t}`);const a=await M.get(`/api/sales/${t}`);console.log("Sale details response:",a.data);let r=a.data;if(!r)throw new Error("No sale data returned from API");const c=r.total_amount+(r.tax_amount||0)-(r.discount_amount||0),x=r.amount_paid||0,m=Math.max(0,c-x),b={id:r.id,total_amount:r.total_amount||0,payment_method:r.payment_method||"cash",amount_paid:r.amount_paid||0,payment_status:r.payment_status||"paid",change_amount:r.change_amount||0,tax_amount:r.tax_amount||0,discount_amount:r.discount_amount||0,discount_type:r.discount_type||"fixed",created_at:r.created_at||new Date().toISOString(),updated_at:r.updated_at||null,created_by:r.created_by||r.user_id||0,debt_amount:m,customer_id:r.customer_id||null,customer_name:r.customer_name||(r.customer?r.customer.name:null),items:Array.isArray(r.items)?r.items.map(l=>({id:l.id,product_id:l.product_id,product_name:l.product&&l.product.name?l.product.name:"منتج غير معروف",quantity:l.quantity||0,price:l.unit_price||l.price||0,subtotal:l.subtotal||l.quantity*(l.unit_price||l.price)||0})):[],cashier_name:r.cashier_name||(r.user?r.user.full_name:"")};console.log("Formatted sale details:",b),he(b),Y(!0)}catch(a){console.error("Error fetching sale details:",a),f("فشل في تحميل تفاصيل البيع. يرجى المحاولة مرة أخرى."),setTimeout(()=>f(""),3e3)}},ke=async(t,a)=>{if((d==null?void 0:d.role)!=="admin"&&a&&a!==(d==null?void 0:d.id)){f("ليس لديك صلاحية لحذف هذا السجل"),setTimeout(()=>f(""),3e3);return}if(window.confirm("هل أنت متأكد من حذف هذا البيع؟ هذا الإجراء لا يمكن التراجع عنه."))try{await M.delete(`/api/sales/${t}`);const r=k.filter(b=>b.id!==t);v(r),S(u.filter(b=>b.id!==t)),I(_.filter(b=>b!==t));const c=r.length,x=Math.max(1,Math.ceil(c/s.limit));let m=s.page;m>x&&x>0&&(m=x),T({...s,total:c,pages:x,page:m}),q("تم حذف البيع بنجاح"),setTimeout(()=>q(""),3e3)}catch(r){console.error("Error deleting sale:",r),f("فشل في حذف البيع. يرجى المحاولة مرة أخرى."),setTimeout(()=>f(""),3e3)}},je=async()=>{if(_.length===0){f("لم يتم تحديد أي سجلات للحذف"),setTimeout(()=>f(""),3e3);return}L(!0)},Ne=async()=>{try{$(!0);let t=[..._];if((d==null?void 0:d.role)!=="admin"){const b=k.filter(l=>{var g;return(l.user_id||l.user&&l.user.id||typeof l.cashier_name=="string"&&((g=E.find(N=>N.full_name===l.cashier_name))==null?void 0:g.id))===(d==null?void 0:d.id)}).map(l=>l.id);if(t=_.filter(l=>b.includes(l)),t.length===0){f("ليس لديك صلاحية لحذف السجلات المحددة"),$(!1),L(!1),setTimeout(()=>f(""),3e3);return}t.length<_.length&&console.warn(`User can only delete ${t.length} out of ${_.length} selected sales`)}for(const m of t)await M.delete(`/api/sales/${m}`);const a=k.filter(m=>!t.includes(m.id));v(a),S(u.filter(m=>!t.includes(m.id)));const r=a.length,c=Math.max(1,Math.ceil(r/s.limit));let x=s.page;x>c&&c>0&&(x=c),T({...s,total:r,pages:c,page:x}),I([]),V(!1),L(!1),q(`تم حذف ${t.length} سجل بنجاح`),$(!1),setTimeout(()=>q(""),3e3)}catch(t){console.error("Error deleting multiple sales:",t),f("فشل في حذف بعض السجلات. يرجى المحاولة مرة أخرى."),$(!1),L(!1),setTimeout(()=>f(""),3e3)}},_e=()=>{if(W)I([]);else if((d==null?void 0:d.role)==="admin")I(u.map(t=>t.id));else{const t=u.filter(a=>a.user_id===(d==null?void 0:d.id)||a.user&&a.user.id===(d==null?void 0:d.id)||a.cashier_name&&a.cashier_name.includes((d==null?void 0:d.full_name)||""));I(t.map(a=>a.id))}V(!W)},ve=t=>{_.includes(t)?(I(_.filter(a=>a!==t)),V(!1)):(I([..._,t]),_.length+1===u.length&&V(!0))},te=t=>{Z(`/sales/${t}/print`)},we=()=>{if(!u||u.length===0){f("لا توجد بيانات للتصدير"),setTimeout(()=>f(""),3e3);return}try{const t=`رقم البيع,التاريخ,سعر المنتجات,الخصم,الضريبة,إجمالي الفاتورة,المبلغ المدفوع,قيمة الدين,طريقة الدفع,حالة الدفع,عدد المنتجات,الكاشير,العميل
`,a=u.map(m=>{const b=m.total_amount+(m.tax_amount||0)-(m.discount_amount||0),l=m.payment_method==="آجل"?"آجل":m.payment_method==="جزئي"?"جزئي":m.payment_method==="cash"?"نقدي":"بطاقة",j=m.payment_status==="paid"?"مدفوع كاملاً":m.payment_status==="partial"?"مدفوع جزئياً":"غير مدفوع";return`${m.id},"${new Date(m.created_at).toLocaleString("en-US",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1})}",${m.total_amount.toFixed(2)},${(m.discount_amount||0).toFixed(2)},${(m.tax_amount||0).toFixed(2)},${b.toFixed(2)},${(m.amount_paid||0).toFixed(2)},${(m.debt_amount||0).toFixed(2)},"${l}","${j}",${m.items_count},"${m.cashier_name||""}","${m.customer_name||""}"`}).join(`
`),r=`data:text/csv;charset=utf-8,${t}${a}`,c=encodeURI(r),x=document.createElement("a");x.setAttribute("href",c),x.setAttribute("download",`sales_${new Date().toISOString().slice(0,10)}.csv`),document.body.appendChild(x),x.click(),document.body.removeChild(x)}catch(t){console.error("Error exporting CSV:",t),f("حدث خطأ أثناء تصدير البيانات"),setTimeout(()=>f(""),3e3)}};return e.jsxs("div",{className:"container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx("button",{onClick:()=>Z("/"),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2.5 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm flex-shrink-0",title:"العودة للرئيسية",children:e.jsx(Me,{className:"text-sm"})}),e.jsxs("div",{className:"mr-3 sm:mr-4 min-w-0 flex-1",children:[e.jsxs("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center",children:[e.jsx(J,{className:"ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:"سجل المبيعات"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block",children:"عرض وإدارة جميع عمليات البيع"})]})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap",children:[e.jsx("button",{onClick:G,className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"تحديث البيانات",children:e.jsx(se,{className:`text-sm ${P?"animate-spin":""}`})}),e.jsxs("button",{onClick:()=>Z("/pos"),className:"bg-primary-600 text-white rounded-lg py-2.5 px-3 sm:px-4 lg:px-5 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg font-medium",children:[e.jsx(J,{className:"ml-2 text-sm"}),e.jsx("span",{className:"hidden sm:inline lg:inline",children:"عملية بيع جديدة"}),e.jsx("span",{className:"sm:hidden lg:hidden",children:"بيع جديد"})]})]})]})})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(Pe,{className:"text-primary-600 dark:text-primary-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-primary-700 dark:text-primary-300 mb-1",children:"عدد المبيعات"}),e.jsx("div",{className:"text-xl font-bold text-primary-600 dark:text-primary-400",children:u.length})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/20 rounded-lg border border-gray-100 dark:border-gray-600/30",children:[e.jsx("div",{className:"bg-gray-100 dark:bg-gray-700/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(J,{className:"text-gray-600 dark:text-gray-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:"إجمالي سعر المنتجات"}),e.jsxs("div",{className:"text-lg font-bold text-gray-600 dark:text-gray-400 truncate",children:[u.reduce((t,a)=>t+a.total_amount,0).toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"قبل الخصم والضريبة"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-100 dark:border-orange-800/30",children:[e.jsx("div",{className:"bg-orange-100 dark:bg-orange-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(Ie,{className:"text-orange-600 dark:text-orange-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-orange-700 dark:text-orange-300 mb-1",children:"إجمالي الخصومات"}),e.jsxs("div",{className:"text-lg font-bold text-orange-600 dark:text-orange-400 truncate",children:["-",u.reduce((t,a)=>t+(a.discount_amount||0),0).toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-orange-500 dark:text-orange-400",children:(()=>{const t=u.reduce((c,x)=>c+x.total_amount,0),a=u.reduce((c,x)=>c+(x.discount_amount||0),0);return`(${(t>0?a/t*100:0).toFixed(1)}%)`})()})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30",children:[e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(Te,{className:"text-blue-600 dark:text-blue-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-blue-700 dark:text-blue-300 mb-1",children:"إجمالي الضرائب"}),e.jsxs("div",{className:"text-lg font-bold text-blue-600 dark:text-blue-400 truncate",children:["+",u.reduce((t,a)=>t+(a.tax_amount||0),0).toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-blue-500 dark:text-blue-400",children:(()=>{const t=u.reduce((c,x)=>c+(x.total_amount-(x.discount_amount||0)),0),a=u.reduce((c,x)=>c+(x.tax_amount||0),0);return`(${(t>0?a/t*100:0).toFixed(1)}%)`})()})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30",children:[e.jsx("div",{className:"bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(Ee,{className:"text-success-600 dark:text-success-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-success-700 dark:text-success-300 mb-1",children:"المبلغ المدفوع فعلياً"}),e.jsxs("div",{className:"text-lg font-bold text-success-600 dark:text-success-400 truncate",children:[u.reduce((t,a)=>t+(a.amount_paid||0),0).toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-success-500 dark:text-success-400",children:"المبلغ المحصل"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg border border-danger-100 dark:border-danger-800/30",children:[e.jsx("div",{className:"bg-danger-100 dark:bg-danger-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(ne,{className:"text-danger-600 dark:text-danger-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-danger-700 dark:text-danger-300 mb-1",children:"الديون المستحقة"}),e.jsxs("div",{className:"text-lg font-bold text-danger-600 dark:text-danger-400 truncate",children:[u.reduce((t,a)=>t+(a.debt_amount||0),0).toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-danger-500 dark:text-danger-400",children:(()=>{const t=u.reduce((c,x)=>c+(x.debt_amount||0),0),a=u.reduce((c,x)=>{const m=x.total_amount+(x.tax_amount||0)-(x.discount_amount||0);return c+m},0);return`(${(a>0?t/a*100:0).toFixed(1)}%)`})()})]})]})]})}),K&&e.jsxs("div",{className:"bg-success-100 dark:bg-success-900/30 text-success-700 dark:text-success-300 p-3 rounded-xl mb-4 flex items-center",children:[e.jsx(Le,{className:"mr-2 flex-shrink-0"}),K]}),Q&&e.jsxs("div",{className:"bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 p-3 rounded-xl mb-4 flex items-center",children:[e.jsx(ne,{className:"mr-2 flex-shrink-0"}),Q]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(Re,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"}),e.jsx("input",{type:"text",placeholder:"بحث برقم البيع أو اسم الكاشير...",value:F,onChange:t=>R(t.target.value),className:"w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"})]})}),e.jsxs("div",{className:"flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>z(!U),className:`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${U?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(Ue,{className:"ml-2"}),"فلاتر"]}),e.jsxs("button",{onClick:G,className:"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center",title:"تحديث",children:[e.jsx(se,{className:`ml-2 ${P?"animate-spin":""}`}),"تحديث"]}),e.jsxs("button",{onClick:we,disabled:!u||u.length===0,className:`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${!u||u.length===0?"bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 border border-gray-200 dark:border-gray-600 cursor-not-allowed":"bg-success-100 dark:bg-success-900/30 text-success-700 dark:text-success-300 border border-success-200 dark:border-success-800 hover:bg-success-200 dark:hover:bg-success-900/50"}`,children:[e.jsx(He,{className:"ml-2"}),"تصدير CSV"]}),e.jsxs("button",{onClick:je,disabled:_.length===0,className:`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${_.length===0?"bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 border border-gray-200 dark:border-gray-600 cursor-not-allowed":"bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 border border-danger-200 dark:border-danger-800 hover:bg-danger-200 dark:hover:bg-danger-900/50"}`,children:[e.jsx(ze,{className:"ml-2"}),"حذف المحدد (",_.length,")"]})]})]}),U&&e.jsxs("div",{className:"mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(ce,{label:"تاريخ البدء",name:"startDate",value:p.startDate,onChange:t=>{C({...p,startDate:t})},placeholder:"اختر تاريخ البدء"})}),e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(ce,{label:"تاريخ الانتهاء",name:"endDate",value:p.endDate,onChange:t=>{C({...p,endDate:t})},placeholder:"اختر تاريخ الانتهاء"})}),e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(B,{label:"طريقة الدفع",name:"paymentMethod",value:p.paymentMethod,onChange:t=>{C({...p,paymentMethod:t})},options:[{value:"all",label:"الجميع"},{value:"cash",label:"نقدي"},{value:"card",label:"بطاقة"},{value:"آجل",label:"آجل"},{value:"جزئي",label:"جزئي"}]})}),e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(B,{label:"عدد العناصر",name:"limit",value:o.limit.toString(),onChange:t=>{const a=parseInt(t,10);w({...o,limit:a,page:1}),C({...p,limit:a,page:1})},options:[{value:"10",label:"10"},{value:"20",label:"20"},{value:"30",label:"30"},{value:"50",label:"50"}]})}),e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(B,{label:"الكاشير",name:"cashierId",value:p.cashierId,onChange:t=>{C({...p,cashierId:t})},options:[{value:"",label:"جميع الكاشيرية"},...E.map(t=>({value:t.id.toString(),label:t.full_name}))]})}),e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(B,{label:"العميل",name:"customerId",value:p.customerId,onChange:t=>{C({...p,customerId:t})},options:[{value:"",label:"جميع العملاء"},...ye.map(t=>({value:t.id.toString(),label:t.name}))]})}),e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(xe,{label:"الحد الأدنى للمبلغ",name:"minAmount",value:p.minAmount,onChange:t=>{C({...p,minAmount:t})},step:"0.01",min:0,placeholder:"0.00",dir:"ltr"})}),e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(xe,{label:"الحد الأعلى للمبلغ",name:"maxAmount",value:p.maxAmount,onChange:t=>{C({...p,maxAmount:t})},step:"0.01",min:0,placeholder:"0.00",dir:"ltr"})})]}),e.jsxs("div",{className:"flex justify-end gap-3 mt-4",children:[e.jsx("button",{type:"button",onClick:ee,className:"bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg transition-colors",children:"إعادة تعيين"}),e.jsx("button",{type:"button",onClick:()=>{console.log("Applying filters and resetting to page 1");const t={...p,page:1};w(t),R("");let a=[...k];p.startDate&&(a=a.filter(l=>{const j=new Date(l.created_at),g=new Date(p.startDate);return g.setHours(0,0,0,0),j>=g})),p.endDate&&(a=a.filter(l=>{const j=new Date(l.created_at),g=new Date(p.endDate);return g.setHours(23,59,59,999),j<=g})),p.minAmount&&(a=a.filter(l=>l.total_amount>=parseFloat(p.minAmount))),p.maxAmount&&(a=a.filter(l=>l.total_amount<=parseFloat(p.maxAmount))),p.paymentMethod!=="all"&&(a=a.filter(l=>l.payment_method===p.paymentMethod)),p.cashierId&&(a=a.filter(l=>{var j;return l.cashier_name&&l.cashier_name.includes(((j=E.find(g=>g.id.toString()===p.cashierId))==null?void 0:j.full_name)||"")})),p.customerId&&(a=a.filter(l=>l.customer_id&&l.customer_id.toString()===p.customerId));const r=a.length,c=p.limit,x=Math.max(1,Math.ceil(r/c)),m=a.slice(0,c),b={total:r,page:1,limit:c,pages:x};S(m),T(b)},className:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors",children:"تطبيق الفلاتر"})]})]})]}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden",children:P?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-10 w-10 border-b-2 border-primary-600 dark:border-primary-400"})}):u&&u.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"overflow-x-auto custom-scrollbar",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-700",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e.jsx("input",{type:"checkbox",checked:W,onChange:_e,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"})}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"رقم البيع"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"التاريخ"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الوقت"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"سعر المنتجات"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الخصم"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الضريبة"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المبلغ المستلم"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"قيمة الدين"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"طريقة الدفع"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"عدد المنتجات"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الكاشير"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"العميل"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الإجراءات"})]})}),e.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:u.map(t=>e.jsxs("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[e.jsx("td",{className:"px-3 py-4 whitespace-nowrap text-center",children:e.jsx("input",{type:"checkbox",checked:_.includes(t.id),onChange:()=>ve(t.id),disabled:(d==null?void 0:d.role)!=="admin"&&!(t.user_id===(d==null?void 0:d.id)||t.user&&t.user.id===(d==null?void 0:d.id)||t.cashier_name&&t.cashier_name.includes((d==null?void 0:d.full_name)||"")),className:`h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded
                            ${(d==null?void 0:d.role)!=="admin"&&!(t.user_id===(d==null?void 0:d.id)||t.user&&t.user.id===(d==null?void 0:d.id)||t.cashier_name&&t.cashier_name.includes((d==null?void 0:d.full_name)||""))?"opacity-50 cursor-not-allowed":""}`})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:["#",t.id]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:me(t.created_at)})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:ge(t.created_at)})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:[t.total_amount.toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"سعر المنتجات فقط"})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:t.discount_amount>0?e.jsxs("div",{children:[e.jsxs("span",{className:"text-orange-600 dark:text-orange-400 font-medium",children:["-",t.discount_amount.toFixed(2)," د.ل"]}),t.discount_type==="percentage"&&e.jsxs("div",{className:"text-xs text-orange-500 dark:text-orange-400",children:["(",(t.discount_amount/t.total_amount*100).toFixed(1),"%)"]}),t.discount_type==="fixed"&&e.jsx("div",{className:"text-xs text-orange-500 dark:text-orange-400",children:"مبلغ ثابت"})]}):e.jsx("span",{className:"text-gray-400 dark:text-gray-500",children:"لا يوجد"})})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:t.tax_amount>0?e.jsxs("div",{children:[e.jsxs("span",{className:"text-blue-600 dark:text-blue-400 font-medium",children:["+",t.tax_amount.toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-blue-500 dark:text-blue-400",children:(()=>{const a=t.total_amount-(t.discount_amount||0);return`(${(a>0?t.tax_amount/a*100:0).toFixed(1)}%)`})()})]}):e.jsx("span",{className:"text-gray-400 dark:text-gray-500",children:"لا يوجد"})})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsxs("div",{className:"text-sm font-bold text-success-600 dark:text-success-400",children:[t.amount_paid.toFixed(2)," د.ل"]}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"المبلغ المدفوع فعلياً"}),t.payment_status&&e.jsx("div",{className:`text-xs mt-1 ${t.payment_status==="paid"?"text-success-600 dark:text-success-400":t.payment_status==="partial"?"text-warning-600 dark:text-warning-400":"text-danger-600 dark:text-danger-400"}`,children:t.payment_status==="paid"?"مدفوع كاملاً":t.payment_status==="partial"?"مدفوع جزئياً":"غير مدفوع"})]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsx("div",{className:`text-sm font-bold ${(t.debt_amount||0)>0?"text-danger-600 dark:text-danger-400":"text-gray-400 dark:text-gray-500"}`,children:(t.debt_amount||0)>0?`${t.debt_amount.toFixed(2)} د.ل`:"لا يوجد"}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:(t.debt_amount||0)>0?"مبلغ مستحق":"لا توجد ديون"}),t.customer_name&&(t.debt_amount||0)>0&&e.jsxs("div",{className:"text-xs text-gray-600 dark:text-gray-400 mt-1",children:["على: ",t.customer_name]})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${t.payment_method==="آجل"?"bg-danger-100 dark:bg-danger-900/30 text-danger-800 dark:text-danger-300":t.payment_method==="جزئي"?"bg-warning-100 dark:bg-warning-900/30 text-warning-800 dark:text-warning-300":t.payment_method==="cash"?"bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-300":"bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300"}`,children:t.payment_method==="آجل"?"آجل":t.payment_method==="جزئي"?"جزئي":t.payment_method==="cash"?"نقدي":"بطاقة"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:t.items_count}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:t.cashier_name||"-"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:t.customer_name||"-"}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>fe(t.id),className:"text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300",title:"عرض التفاصيل",children:e.jsx(qe,{})}),e.jsx("button",{onClick:()=>te(t.id),className:"text-success-600 dark:text-success-400 hover:text-success-800 dark:hover:text-success-300",title:"طباعة الفاتورة",children:e.jsx(de,{})}),((d==null?void 0:d.role)==="admin"||t.user_id===(d==null?void 0:d.id)||t.user&&t.user.id===(d==null?void 0:d.id)||t.cashier_name&&t.cashier_name.includes((d==null?void 0:d.full_name)||""))&&e.jsx("button",{onClick:()=>ke(t.id,t.user_id||t.user&&t.user.id),className:"text-danger-600 dark:text-danger-400 hover:text-danger-800 dark:hover:text-danger-300",title:"حذف",children:e.jsx(ie,{})})]})})]},t.id))})]})}),s&&s.total>0&&e.jsxs("div",{className:"px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800",children:[e.jsxs("div",{className:"flex-1 flex justify-between items-center sm:hidden",children:[e.jsx("button",{onClick:()=>{const t=Math.max(1,s.page-1);w({...o,page:t})},disabled:s.page<=1,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${s.page<=1?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:"السابق"}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.jsx("span",{className:"font-medium",children:s.page})," / ",e.jsx("span",{className:"font-medium",children:s.pages})]}),e.jsx("button",{onClick:()=>{const t=Math.min(s.pages,s.page+1);w({...o,page:t})},disabled:s.page>=s.pages,className:`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${s.page>=s.pages?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:"التالي"})]}),e.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:[e.jsx("span",{className:"font-medium",children:s.page})," / ",e.jsx("span",{className:"font-medium",children:s.pages})," ",e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["(الإجمالي: ",s.total,")"]})]})}),e.jsx("div",{children:e.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse","aria-label":"Pagination",children:[e.jsxs("button",{onClick:()=>{const t=Math.max(1,s.page-1);w({...o,page:t})},disabled:s.page<=1,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${s.page<=1?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"sr-only",children:"السابق"}),e.jsx(oe,{className:"h-5 w-5"})]}),Array.from({length:Math.min(5,s.pages)},(t,a)=>{let r;return s.pages<=5||s.page<=3?r=a+1:s.page>=s.pages-2?r=s.pages-4+a:r=s.page-2+a,e.jsx("button",{onClick:()=>{w({...o,page:r})},className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${s.page===r?"z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:r},r)}),e.jsxs("button",{onClick:()=>{const t=Math.min(s.pages,s.page+1);w({...o,page:t})},disabled:s.page>=s.pages,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${s.page>=s.pages?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"sr-only",children:"التالي"}),e.jsx(le,{className:"h-5 w-5"})]})]})})]})]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-500 dark:text-gray-400 mb-4",children:"لا توجد مبيعات تطابق معايير البحث"}),e.jsx("button",{onClick:ee,className:"text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 px-4 py-2 border border-primary-200 dark:border-primary-800 rounded-lg transition-colors",children:"إعادة ضبط الفلاتر"})]}),s&&s.total>0&&e.jsx("div",{className:"px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800",children:e.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:[e.jsx("span",{className:"font-medium",children:s.page})," / ",e.jsx("span",{className:"font-medium",children:s.pages})," ",e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["(الإجمالي: ",s.total,")"]})]})}),e.jsx("div",{children:e.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse","aria-label":"Pagination",children:[e.jsxs("button",{onClick:()=>{const t=Math.max(1,s.page-1);w({...o,page:t})},disabled:s.page<=1,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${s.page<=1?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"sr-only",children:"السابق"}),e.jsx(oe,{className:"h-5 w-5"})]}),Array.from({length:Math.min(5,s.pages)},(t,a)=>{let r;return s.pages<=5||s.page<=3?r=a+1:s.page>=s.pages-2?r=s.pages-4+a:r=s.page-2+a,e.jsx("button",{onClick:()=>{w({...o,page:r})},className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${s.page===r?"z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:r},r)}),e.jsxs("button",{onClick:()=>{const t=Math.min(s.pages,s.page+1);w({...o,page:t})},disabled:s.page>=s.pages,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${s.page>=s.pages?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"sr-only",children:"التالي"}),e.jsx(le,{className:"h-5 w-5"})]})]})})]})})]})}),be&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-auto bg-black bg-opacity-60 flex items-center justify-center",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-md mx-4 animate-fadeIn",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h3",{className:"text-xl font-medium text-secondary-900 dark:text-secondary-100",children:"تأكيد الحذف"}),e.jsx("button",{onClick:()=>L(!1),className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-2xl",children:"×"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("p",{className:"text-gray-700 dark:text-gray-300 mb-4",children:["هل أنت متأكد من حذف ",_.length," سجل؟ هذا الإجراء لا يمكن التراجع عنه."]}),e.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg",children:e.jsx("p",{children:"ملاحظة: سيتم استعادة كميات المنتجات المباعة إلى المخزون."})})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("button",{onClick:()=>L(!1),className:"px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:"إلغاء"}),e.jsxs("button",{onClick:Ne,className:"px-4 py-2 bg-danger-600 text-white rounded-lg hover:bg-danger-700 flex items-center gap-2 transition-colors",children:[e.jsx(ie,{}),e.jsx("span",{children:"تأكيد الحذف"})]})]})]})}),pe&&i&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-auto bg-black bg-opacity-60 flex items-center justify-center",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 w-full max-w-2xl mx-4 animate-fadeIn",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("h3",{className:"text-xl font-medium text-secondary-900 dark:text-secondary-100",children:["تفاصيل البيع #",i.id]}),e.jsx("button",{onClick:()=>Y(!1),className:"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-2xl",children:"×"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"التاريخ:"}),e.jsx("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:me(i.created_at)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"الوقت:"}),e.jsx("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:ge(i.created_at)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"طريقة الدفع:"}),e.jsx("p",{className:`font-medium ${i.payment_method==="آجل"?"text-danger-600 dark:text-danger-400":i.payment_method==="جزئي"?"text-warning-600 dark:text-warning-400":"text-gray-900 dark:text-gray-100"}`,children:i.payment_method==="آجل"?"آجل":i.payment_method==="جزئي"?"جزئي":i.payment_method==="cash"?"نقدي":"بطاقة"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"حالة الدفع:"}),e.jsx("p",{className:`font-medium ${i.payment_status==="paid"?"text-success-600 dark:text-success-400":i.payment_status==="partial"?"text-warning-600 dark:text-warning-400":"text-danger-600 dark:text-danger-400"}`,children:i.payment_status==="paid"?"مدفوع كاملاً":i.payment_status==="partial"?"مدفوع جزئياً":"غير مدفوع"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"الكاشير:"}),e.jsx("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:i.cashier_name||"-"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"المبلغ المدفوع فعلياً:"}),e.jsxs("p",{className:"font-medium text-success-600 dark:text-success-400",children:[i.amount_paid.toFixed(2)," د.ل"]})]}),(i.debt_amount||0)>0&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"قيمة الدين:"}),e.jsxs("p",{className:"font-medium text-danger-600 dark:text-danger-400",children:[i.debt_amount.toFixed(2)," د.ل"]}),i.customer_name&&e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["على: ",i.customer_name]})]}),i.payment_method==="cash"&&i.change_amount>0&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"المتبقي:"}),e.jsxs("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:[i.change_amount.toFixed(2)," د.ل"]})]})]}),e.jsxs("div",{className:"border-t dark:border-gray-700 pt-4 mb-6",children:[e.jsx("h4",{className:"font-medium mb-3 text-gray-900 dark:text-gray-100",children:"المنتجات:"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-700",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المنتج"}),e.jsx("th",{scope:"col",className:"px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الكمية"}),e.jsx("th",{scope:"col",className:"px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"السعر"}),e.jsx("th",{scope:"col",className:"px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المجموع"})]})}),e.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:i.items.map(t=>e.jsxs("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100",children:t.product_name}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:t.quantity}),e.jsxs("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:[t.price.toFixed(2)," د.ل"]}),e.jsxs("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:[t.subtotal.toFixed(2)," د.ل"]})]},t.id))}),e.jsxs("tfoot",{children:[e.jsxs("tr",{className:"bg-gray-50 dark:bg-gray-700",children:[e.jsx("td",{colSpan:3,className:"px-4 py-2 text-sm font-medium text-gray-900 dark:text-gray-100 text-right",children:"إجمالي سعر المنتجات:"}),e.jsxs("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100",children:[i.total_amount.toFixed(2)," د.ل",e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"قبل الخصم والضريبة"})]})]}),i.discount_amount>0&&e.jsxs("tr",{className:"bg-orange-50 dark:bg-orange-900/20",children:[e.jsxs("td",{colSpan:3,className:"px-4 py-2 text-sm font-medium text-orange-700 dark:text-orange-300 text-right",children:["الخصم (",i.discount_type==="percentage"?"نسبة مئوية":"مبلغ ثابت","):"]}),e.jsxs("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-orange-700 dark:text-orange-300",children:["-",i.discount_amount.toFixed(2)," د.ل",e.jsx("div",{className:"text-xs text-orange-600 dark:text-orange-400",children:i.discount_type==="percentage"?`(${(i.discount_amount/i.total_amount*100).toFixed(1)}% من سعر المنتجات)`:"(مبلغ ثابت)"})]})]}),e.jsxs("tr",{className:"bg-gray-50 dark:bg-gray-700",children:[e.jsx("td",{colSpan:3,className:"px-4 py-2 text-sm font-medium text-gray-900 dark:text-gray-100 text-right",children:"المجموع بعد الخصم:"}),e.jsxs("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100",children:[(i.total_amount-(i.discount_amount||0)).toFixed(2)," د.ل",e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[i.total_amount.toFixed(2)," - ",(i.discount_amount||0).toFixed(2)]})]})]}),i.tax_amount>0&&e.jsxs("tr",{className:"bg-blue-50 dark:bg-blue-900/20",children:[e.jsx("td",{colSpan:3,className:"px-4 py-2 text-sm font-medium text-blue-700 dark:text-blue-300 text-right",children:"الضريبة:"}),e.jsxs("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-blue-700 dark:text-blue-300",children:["+",i.tax_amount.toFixed(2)," د.ل",e.jsx("div",{className:"text-xs text-blue-600 dark:text-blue-400",children:(()=>{const t=i.total_amount-(i.discount_amount||0);return`(${(t>0?i.tax_amount/t*100:0).toFixed(1)}% من المبلغ بعد الخصم)`})()})]})]}),e.jsxs("tr",{className:"bg-gray-100 dark:bg-gray-600 border-t-2 border-gray-300 dark:border-gray-500",children:[e.jsx("td",{colSpan:3,className:"px-4 py-3 text-base font-bold text-gray-700 dark:text-gray-300 text-right",children:"إجمالي قيمة الفاتورة:"}),e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap text-base font-bold text-gray-700 dark:text-gray-300",children:[(()=>{const t=i.discount_amount||0,a=i.total_amount-t,r=i.tax_amount||0;return(a+r).toFixed(2)})()," د.ل",e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(i.total_amount-(i.discount_amount||0)).toFixed(2)," + ",(i.tax_amount||0).toFixed(2)]})]})]}),e.jsxs("tr",{className:"bg-success-50 dark:bg-success-900/20",children:[e.jsx("td",{colSpan:3,className:"px-4 py-3 text-base font-bold text-success-700 dark:text-success-300 text-right",children:"المبلغ المدفوع فعلياً:"}),e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap text-base font-bold text-success-700 dark:text-success-300",children:[i.amount_paid.toFixed(2)," د.ل",e.jsx("div",{className:"text-xs text-success-600 dark:text-success-400",children:i.payment_status==="paid"?"مدفوع كاملاً":i.payment_status==="partial"?"مدفوع جزئياً":"غير مدفوع"})]})]}),(i.debt_amount||0)>0&&e.jsxs("tr",{className:"bg-danger-50 dark:bg-danger-900/20",children:[e.jsx("td",{colSpan:3,className:"px-4 py-3 text-base font-bold text-danger-700 dark:text-danger-300 text-right",children:"قيمة الدين المستحق:"}),e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap text-base font-bold text-danger-700 dark:text-danger-300",children:[i.debt_amount.toFixed(2)," د.ل",e.jsx("div",{className:"text-xs text-danger-600 dark:text-danger-400",children:i.customer_name?`على: ${i.customer_name}`:"مبلغ مستحق"})]})]})]})]})})]}),e.jsxs("div",{className:"flex justify-between border-t dark:border-gray-700 pt-4",children:[e.jsxs("button",{onClick:()=>te(i.id),className:"px-4 py-2 bg-success-600 text-white rounded-lg hover:bg-success-700 flex items-center gap-2 transition-colors",children:[e.jsx(de,{}),e.jsx("span",{children:"طباعة الفاتورة"})]}),e.jsx("button",{onClick:()=>Y(!1),className:"px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:"إغلاق"})]})]})})]})};export{We as default};
