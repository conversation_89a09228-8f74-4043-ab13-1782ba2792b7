var ft=Object.defineProperty;var jt=(c,s,r)=>s in c?ft(c,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):c[s]=r;var ze=(c,s,r)=>jt(c,typeof s!="symbol"?s+"":s,r);import{b as D,c as kt,j as e,r as w,u as Qe,f as Nt,a as vt,d as wt}from"./index-BtFmrjEm.js";import{Y as ye,ao as _,ap as pe,y as ue,aj as be,T as X,o as W,aq as he,Z as St,aa as Ft,O as Ct,c as Ee,D as Dt,ah as $t,ar as He,n as K,v as ke,as as Pe,E as Te,G as At,H as Bt,t as G,j as ge,u as _e,w as Me,z as Lt}from"./index-BEpVSoxh.js";import{f as Q,R as Ne}from"./dateTimeService-BhQ7vgNU.js";import{D as Ke}from"./DatePicker-DsqHADpa.js";import{D as zt,S as Et}from"./SuccessModal-QFjPA0lw.js";import{M as Ue}from"./Modal-BgtJQkhC.js";const Z=class Z{static getInstance(){return Z.instance||(Z.instance=new Z),Z.instance}async getCurrentDaySales(){try{console.log("جلب بيانات مبيعات اليوم الحالي (24 ساعة كاملة)");const s=await D.get("/api/dashboard/sales-trends/day",{params:{previous:!1}});return console.log("تم جلب بيانات اليوم الحالي بنجاح (24 ساعة كاملة):",s.data),s.data}catch(s){return console.error("خطأ في جلب بيانات اليوم الحالي:",s),Array.from({length:24},(r,t)=>({date:`${t.toString().padStart(2,"0")}:00`,amount:0}))}}async getCurrentWeekSales(){try{console.log("جلب بيانات مبيعات الأسبوع الحالي");const s=await D.get("/api/dashboard/sales-trends/week",{params:{previous:!1}});return console.log("تم جلب بيانات الأسبوع الحالي بنجاح:",s.data),s.data}catch(s){console.error("خطأ في جلب بيانات الأسبوع الحالي:",s);const r=new Date,t=[];for(let n=6;n>=0;n--){const x=new Date(r);x.setDate(r.getDate()-n),t.push({date:x.toISOString().split("T")[0],amount:0})}return t}}async getCurrentMonthSales(){try{console.log("جلب بيانات مبيعات الشهر الحالي");const s=await D.get("/api/dashboard/sales-trends/month",{params:{previous:!1}});return console.log("تم جلب بيانات الشهر الحالي بنجاح:",s.data),s.data}catch(s){console.error("خطأ في جلب بيانات الشهر الحالي:",s);const r=new Date,t=[];for(let n=29;n>=0;n--){const x=new Date(r);x.setDate(r.getDate()-n),t.push({date:x.toISOString().split("T")[0],amount:0})}return t}}async getCurrentYearSales(){try{console.log("جلب بيانات مبيعات السنة الحالية");const s=await D.get("/api/dashboard/sales-trends/year",{params:{previous:!1}});return console.log("تم جلب بيانات السنة الحالية بنجاح:",s.data),s.data}catch(s){console.error("خطأ في جلب بيانات السنة الحالية:",s);const r=new Date,t=[];for(let n=11;n>=0;n--){const x=new Date(r);x.setMonth(r.getMonth()-n),t.push({date:`${x.getFullYear()}-${(x.getMonth()+1).toString().padStart(2,"0")}`,amount:0})}return t}}async getSalesByPeriod(s){switch(console.log(`جلب بيانات الفترة الحالية: ${s}`),s){case"day":return this.getCurrentDaySales();case"week":return this.getCurrentWeekSales();case"month":return this.getCurrentMonthSales();case"year":return this.getCurrentYearSales();default:return console.error(`نوع فترة غير مدعوم: ${s}`),[]}}async getCurrentPeriodTotal(s){try{console.log(`جلب إجمالي الفترة الحالية: ${s}`);const t=(await this.getSalesByPeriod(s)).reduce((n,x)=>n+x.amount,0);return console.log(`إجمالي الفترة الحالية ${s}: ${t}`),t}catch(r){return console.error("خطأ في جلب إجمالي الفترة الحالية:",r),0}}async refreshPeriodData(s){return console.log(`تحديث بيانات الفترة الحالية: ${s}`),this.getSalesByPeriod(s)}validateSalesData(s){if(!Array.isArray(s))return console.error("البيانات ليست مصفوفة صحيحة"),!1;for(const r of s)if(!r.date||typeof r.amount!="number")return console.error("عنصر بيانات غير صحيح:",r),!1;return!0}formatDataForChart(s){return this.validateSalesData(s)?s.map(r=>({...r,amount:Math.round(r.amount*100)/100})):(console.warn("بيانات غير صحيحة، إرجاع بيانات فارغة"),[])}};ze(Z,"instance");let Re=Z;const Oe=Re.getInstance(),J=class J{static getInstance(){return J.instance||(J.instance=new J),J.instance}async getPreviousDaySales(){try{console.log("جلب بيانات مبيعات اليوم السابق (أمس - 24 ساعة كاملة)");const s=await D.get("/api/dashboard/sales-trends/day",{params:{previous:!0}});return console.log("تم جلب بيانات اليوم السابق بنجاح (أمس كاملاً):",s.data),s.data}catch(s){return console.error("خطأ في جلب بيانات اليوم السابق:",s),Array.from({length:24},(r,t)=>({date:`${t.toString().padStart(2,"0")}:00`,amount:0}))}}async getPreviousWeekSales(){try{console.log("جلب بيانات مبيعات الأسبوع السابق");const s=await D.get("/api/dashboard/sales-trends/week",{params:{previous:!0}});return console.log("تم جلب بيانات الأسبوع السابق بنجاح:",s.data),s.data}catch(s){console.error("خطأ في جلب بيانات الأسبوع السابق:",s);const r=new Date,t=[];for(let n=13;n>=7;n--){const x=new Date(r);x.setDate(r.getDate()-n),t.push({date:x.toISOString().split("T")[0],amount:0})}return t}}async getPreviousMonthSales(){try{console.log("جلب بيانات مبيعات الشهر السابق");const s=await D.get("/api/dashboard/sales-trends/month",{params:{previous:!0}});return console.log("تم جلب بيانات الشهر السابق بنجاح:",s.data),s.data}catch(s){console.error("خطأ في جلب بيانات الشهر السابق:",s);const r=new Date,t=[];for(let n=59;n>=30;n--){const x=new Date(r);x.setDate(r.getDate()-n),t.push({date:x.toISOString().split("T")[0],amount:0})}return t}}async getPreviousYearSales(){try{console.log("جلب بيانات مبيعات السنة السابقة");const s=await D.get("/api/dashboard/sales-trends/year",{params:{previous:!0}});return console.log("تم جلب بيانات السنة السابقة بنجاح:",s.data),s.data}catch(s){console.error("خطأ في جلب بيانات السنة السابقة:",s);const r=new Date,t=[];for(let n=23;n>=12;n--){const x=new Date(r);x.setMonth(r.getMonth()-n),t.push({date:`${x.getFullYear()}-${(x.getMonth()+1).toString().padStart(2,"0")}`,amount:0})}return t}}async getSalesByPeriod(s){switch(console.log(`جلب بيانات الفترة السابقة: ${s}`),s){case"day":return this.getPreviousDaySales();case"week":return this.getPreviousWeekSales();case"month":return this.getPreviousMonthSales();case"year":return this.getPreviousYearSales();default:return console.error(`نوع فترة غير مدعوم: ${s}`),[]}}async getPreviousPeriodTotal(s){try{console.log(`جلب إجمالي الفترة السابقة: ${s}`);const t=(await this.getSalesByPeriod(s)).reduce((n,x)=>n+x.amount,0);return console.log(`إجمالي الفترة السابقة ${s}: ${t}`),t}catch(r){return console.error("خطأ في جلب إجمالي الفترة السابقة:",r),0}}async compareWithCurrentPeriod(s,r){try{console.log(`مقارنة الفترة ${s} مع السابقة`);const t=await this.getPreviousPeriodTotal(s);let n=0;t>0?n=(r-t)/t*100:r>0&&(n=100);const x={current:r,previous:t,growthRate:Math.round(n*100)/100,isPositive:n>=0,difference:r-t};return console.log(`نتيجة المقارنة للفترة ${s}:`,x),x}catch(t){return console.error("خطأ في مقارنة الفترات:",t),{current:r,previous:0,growthRate:0,isPositive:!0,difference:0}}}async refreshPeriodData(s){return console.log(`تحديث بيانات الفترة السابقة: ${s}`),this.getSalesByPeriod(s)}validateSalesData(s){if(!Array.isArray(s))return console.error("البيانات ليست مصفوفة صحيحة"),!1;for(const r of s)if(!r.date||typeof r.amount!="number")return console.error("عنصر بيانات غير صحيح:",r),!1;return!0}formatDataForChart(s){return this.validateSalesData(s)?s.map(r=>({...r,amount:Math.round(r.amount*100)/100})):(console.warn("بيانات غير صحيحة، إرجاع بيانات فارغة"),[])}};ze(J,"instance");let Ie=J;const ve=Ie.getInstance(),Pt={totalSales:0,totalItems:0,averageSale:0,totalRevenue:0,todaySales:0,productCount:0,lowStockCount:0},Tt={totalUsers:0,activeUsers:0,lastLogin:"غير متاح",systemUptime:"0 أيام",lastBackup:"لم يتم النسخ الاحتياطي بعد",databaseSize:"0 MB"},_t=kt((c,s)=>({salesTrends:[],previousPeriodSales:[],previousPeriodTotal:0,productCategories:[],inactiveProducts:0,inventoryStatus:[],reportSummary:Pt,topProducts:[],recentSales:[],systemStats:Tt,isLoading:!1,error:null,selectedPeriod:"day",selectedReportType:"sales",fetchSalesTrends:async r=>{try{c({isLoading:!0,error:null}),console.log(`جلب بيانات المبيعات للفترة: ${r} باستخدام الخدمات المنفصلة`);const[t,n]=await Promise.all([Oe.getSalesByPeriod(r),ve.getSalesByPeriod(r)]);console.log("تم جلب البيانات من الخدمات المنفصلة بنجاح"),console.log(`بيانات الفترة الحالية (${r}):`,t),console.log(`بيانات الفترة السابقة (${r}):`,n);const x=Oe.formatDataForChart(t),f=ve.formatDataForChart(n);console.log(`تم تنسيق ${x.length} عنصر للفترة الحالية`),console.log(`تم تنسيق ${f.length} عنصر للفترة السابقة`);const g=x.length,k=x.reduce((h,l)=>h+l.amount,0),N=g>0?k/g:0,p=s().reportSummary;c({salesTrends:x,previousPeriodSales:f,isLoading:!1,selectedPeriod:r,reportSummary:{...p,totalSales:g,totalItems:g,averageSale:N,totalRevenue:k}})}catch(t){console.error("خطأ في جلب بيانات المبيعات باستخدام الخدمات المنفصلة:",t),console.log("محاولة إنشاء بيانات فارغة باستخدام الخدمات...");try{const n=await Oe.getSalesByPeriod(r).catch(()=>[]),x=await ve.getSalesByPeriod(r).catch(()=>[]);let f=n,g=x;(n.length===0||x.length===0)&&(console.log("إنشاء بيانات فارغة يدوياً للفترة:",r),r==="day"?(f=Array.from({length:24},(k,N)=>({date:`${N.toString().padStart(2,"0")}:00`,amount:0})),g=[...f]):r==="week"?(f=Array.from({length:7},(k,N)=>{const p=new Date;return p.setDate(p.getDate()-(6-N)),{date:p.toISOString().split("T")[0],amount:0}}),g=[...f]):r==="month"?(f=Array.from({length:30},(k,N)=>{const p=new Date;return p.setDate(p.getDate()-(29-N)),{date:p.toISOString().split("T")[0],amount:0}}),g=[...f]):r==="year"&&(f=Array.from({length:12},(k,N)=>{const p=new Date;return p.setMonth(p.getMonth()-(11-N)),{date:`${p.getFullYear()}-${(p.getMonth()+1).toString().padStart(2,"0")}`,amount:0}}),g=[...f])),c({salesTrends:f,previousPeriodSales:g,isLoading:!1,selectedPeriod:r,reportSummary:{totalSales:0,totalItems:0,averageSale:0,totalRevenue:0,todaySales:0,productCount:0,lowStockCount:0},error:"فشل في جلب البيانات من الخادم - تم عرض بيانات فارغة"})}catch(n){console.error("خطأ في إنشاء البيانات الفارغة:",n),c({salesTrends:[],previousPeriodSales:[],isLoading:!1,selectedPeriod:r,reportSummary:{totalSales:0,totalItems:0,averageSale:0,totalRevenue:0,todaySales:0,productCount:0,lowStockCount:0},error:"خطأ شديد في جلب البيانات"})}}},fetchPreviousPeriodTotal:async r=>{try{console.log(`جلب إجمالي الفترة السابقة للفترة: ${r} باستخدام الخدمة المنفصلة`);const t=await ve.getPreviousPeriodTotal(r);console.log(`إجمالي الفترة السابقة للفترة ${r}: ${t}`),c({previousPeriodTotal:t})}catch(t){console.error("خطأ في جلب إجمالي الفترة السابقة:",t),c({previousPeriodTotal:0})}},fetchProductCategories:async()=>{try{c({isLoading:!0,error:null});const r=await D.get("/api/dashboard/product-categories");c({productCategories:r.data.categories||r.data,inactiveProducts:r.data.inactive_products||0,isLoading:!1})}catch(r){console.error("Error fetching product categories:",r),c({productCategories:[],isLoading:!1,error:"فشل في جلب بيانات فئات المنتجات"})}},fetchInventoryStatus:async()=>{try{c({isLoading:!0,error:null});const r=await D.get("/api/dashboard/inventory-status");console.log("Fetched inventory status:",r.data),c({inventoryStatus:r.data,isLoading:!1})}catch(r){console.error("Error fetching inventory status:",r),c({inventoryStatus:[],isLoading:!1,error:"فشل في جلب بيانات حالة المخزون"})}},fetchSystemStats:async()=>{try{c({isLoading:!0,error:null});const r=await D.get("/api/dashboard/system-stats");c({systemStats:r.data,isLoading:!1})}catch(r){console.error("Error fetching system stats:",r),c({systemStats:{totalUsers:0,activeUsers:0,lastLogin:"غير متاح",systemUptime:"غير متاح",lastBackup:"غير متاح",databaseSize:"0 MB"},isLoading:!1,error:null})}},fetchDashboardStats:async()=>{try{c({isLoading:!0,error:null});const t=(await D.get("/api/dashboard/stats")).data,n=s().reportSummary;c({reportSummary:{...n,totalRevenue:t.totalRevenue||0,todaySales:t.todaySales||0,productCount:t.productCount||0,lowStockCount:t.lowStockCount||0},topProducts:t.topProducts||[],recentSales:t.recentSales||[],isLoading:!1})}catch(r){console.error("Error fetching dashboard stats:",r);const t=[{id:1,name:"هاتف ذكي",quantity:15,total:7500},{id:2,name:"لابتوب",quantity:8,total:12e3},{id:3,name:"سماعات لاسلكية",quantity:25,total:2500},{id:4,name:"شاحن متنقل",quantity:30,total:1800},{id:5,name:"ساعة ذكية",quantity:12,total:3600}],n=[{id:101,total:1250,createdAt:Q().toISOString(),items:3},{id:102,total:850,createdAt:Q().toISOString(),items:2},{id:103,total:2100,createdAt:Q().toISOString(),items:5},{id:104,total:450,createdAt:Q().toISOString(),items:1},{id:105,total:1800,createdAt:Q().toISOString(),items:4}];c({topProducts:t,recentSales:n,isLoading:!1,error:null})}},setReportType:r=>{c({selectedReportType:r});const t=s();r==="sales"?(t.fetchSalesTrends(t.selectedPeriod),t.fetchDashboardStats()):r==="products"?t.fetchProductCategories():r==="inventory"?t.fetchInventoryStatus():r==="system"&&t.fetchSystemStats()},setPeriod:r=>{c({selectedPeriod:r}),s().fetchSalesTrends(r)}})),Mt=({isOpen:c,onClose:s,onConfirm:r,backupInfo:t,isLoading:n=!1})=>t?e.jsx(Ue,{isOpen:c,onClose:s,title:"تأكيد استعادة النسخة الاحتياطية",size:"lg",zIndex:"high",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/30 mb-6",children:e.jsx(ye,{className:"h-8 w-8 text-orange-600 dark:text-orange-400"})}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-3",children:"⚠️ تحذير مهم"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-4",children:"هل أنت متأكد من استعادة النسخة الاحتياطية التالية؟"})]}),e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6 text-right",children:e.jsxs("div",{className:"grid grid-cols-1 gap-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(_,{className:"text-primary-600 dark:text-primary-400 ml-3 flex-shrink-0"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"اسم الملف"}),e.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 break-all",children:t.name})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(pe,{className:"text-success-600 dark:text-success-400 ml-3 flex-shrink-0"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"حجم الملف"}),e.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:t.size})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(ue,{className:"text-warning-600 dark:text-warning-400 ml-3 flex-shrink-0"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"تاريخ الإنشاء"}),e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:[t.created_date," - ",t.created_time]})]})]})]})}),e.jsxs("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6",children:[e.jsx("h5",{className:"text-sm font-semibold text-red-800 dark:text-red-300 mb-2",children:"سيتم تنفيذ العمليات التالية:"}),e.jsxs("ul",{className:"text-xs text-red-700 dark:text-red-400 space-y-1 text-right",children:[e.jsx("li",{children:"• إنشاء نسخة احتياطية من البيانات الحالية تلقائياً"}),e.jsx("li",{children:"• استبدال جميع البيانات الحالية بالنسخة المحددة"}),e.jsx("li",{children:"• فقدان جميع التغييرات التي تمت بعد تاريخ هذه النسخة"}),e.jsx("li",{children:"• إعادة تحميل النظام لتطبيق التغييرات"})]}),e.jsx("p",{className:"text-xs font-semibold text-red-800 dark:text-red-300 mt-3",children:"⚠️ هذا الإجراء لا يمكن التراجع عنه!"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center",children:[e.jsx("button",{onClick:s,disabled:n,className:"btn-secondary flex items-center justify-center min-w-[140px]",children:e.jsx("span",{children:"إلغاء"})}),e.jsx("button",{onClick:r,disabled:n,className:"btn-warning flex items-center justify-center min-w-[140px]",children:n?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),e.jsx("span",{children:"جاري الاستعادة..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(be,{className:"ml-2"}),e.jsx("span",{children:"تأكيد الاستعادة"})]})})]})]})}):null,Ot=({isOpen:c,onClose:s,onConfirm:r,title:t,message:n,description:x,type:f,isLoading:g=!1})=>{const k=()=>{switch(f){case"backup":return e.jsx(_,{className:"h-8 w-8 text-primary-600 dark:text-primary-400"});case"update":return e.jsx(W,{className:"h-8 w-8 text-blue-600 dark:text-blue-400"});case"clear-cache":return e.jsx(X,{className:"h-8 w-8 text-orange-600 dark:text-orange-400"});default:return e.jsx(ye,{className:"h-8 w-8 text-gray-600 dark:text-gray-400"})}},N=()=>{switch(f){case"backup":return"bg-primary-100 dark:bg-primary-900/30";case"update":return"bg-blue-100 dark:bg-blue-900/30";case"clear-cache":return"bg-orange-100 dark:bg-orange-900/30";default:return"bg-gray-100 dark:bg-gray-900/30"}},p=()=>{switch(f){case"backup":return"btn-primary";case"update":return"btn-info";case"clear-cache":return"btn-warning";default:return"btn-secondary"}},h=()=>{switch(f){case"backup":return g?"جاري الإنشاء...":"إنشاء النسخة";case"update":return g?"جاري التحديث...":"تحديث النظام";case"clear-cache":return g?"جاري المسح...":"مسح التخزين";default:return g?"جاري التنفيذ...":"تأكيد"}},y=(()=>{switch(f){case"backup":return{title:"سيتم تنفيذ العمليات التالية:",items:["• إنشاء نسخة احتياطية من قاعدة البيانات الحالية","• حفظ النسخة في مجلد النسخ الاحتياطية","• تحديث إحصائيات النظام","• عرض تفاصيل النسخة الجديدة"],note:"هذه العملية آمنة ولا تؤثر على البيانات الحالية"};case"update":return{title:"سيتم تنفيذ العمليات التالية:",items:["• فحص التحديثات المتاحة","• تحديث ملفات النظام","• إعادة تشغيل الخدمات المطلوبة","• التحقق من سلامة النظام"],note:"قد يستغرق التحديث بضع دقائق"};case"clear-cache":return{title:"سيتم تنفيذ العمليات التالية:",items:["• مسح جميع بيانات التخزين المحلي","• مسح بيانات الجلسة المؤقتة","• إعادة تحميل الصفحة تلقائياً","• إعادة تسجيل الدخول مطلوبة"],note:"⚠️ ستحتاج لإعادة تسجيل الدخول بعد هذه العملية"};default:return null}})();return e.jsx(Ue,{isOpen:c,onClose:s,title:t,size:"md",zIndex:"high",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`mx-auto flex h-16 w-16 items-center justify-center rounded-full ${N()} mb-6`,children:k()}),e.jsxs("div",{className:"mb-6",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-4",children:n}),x&&e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-4",children:x})]}),y&&e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-6",children:[e.jsx("h5",{className:"text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2",children:y.title}),e.jsx("ul",{className:"text-xs text-gray-700 dark:text-gray-300 space-y-1 text-right mb-3",children:y.items.map(($,z)=>e.jsx("li",{children:$},z))}),e.jsx("p",{className:"text-xs font-medium text-gray-600 dark:text-gray-400",children:y.note})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center",children:[e.jsx("button",{onClick:s,disabled:g,className:"btn-secondary flex items-center justify-center min-w-[120px]",children:e.jsx("span",{children:"إلغاء"})}),e.jsx("button",{onClick:r,disabled:g,className:`${p()} flex items-center justify-center min-w-[140px]`,children:g?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),e.jsx("span",{children:h()})]}):e.jsxs(e.Fragment,{children:[k(),e.jsx("span",{className:"ml-2",children:h()})]})})]})]})})},Rt=({isOpen:c,onClose:s,onRestore:r,onDelete:t})=>{const[n,x]=w.useState([]),[f,g]=w.useState(!1),k=async()=>{try{g(!0);const h=await fetch("/api/dashboard/backups"),l=await h.json();h.ok?x(l.backups||[]):(console.error("Error fetching backups:",l),x([]))}catch(h){console.error("Error fetching backups:",h),x([])}finally{g(!1)}};w.useEffect(()=>{c&&k()},[c]);const N=h=>{r(h),s()},p=h=>{t(h)};return e.jsx(Ue,{isOpen:c,onClose:s,title:"جميع النسخ الاحتياطية",size:"xl",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(_,{className:"text-primary-600 dark:text-primary-400 ml-3 flex-shrink-0"}),e.jsxs("div",{className:"min-w-0",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"إدارة النسخ الاحتياطية"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:"عرض وإدارة جميع النسخ الاحتياطية المتاحة"})]})]}),e.jsxs("button",{onClick:k,className:"btn-outline-sm flex items-center justify-center sm:justify-start",disabled:f,children:[e.jsx(W,{className:`ml-2 ${f?"animate-spin":""}`}),e.jsx("span",{children:"تحديث"})]})]}),f?e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"}),e.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-300",children:"جاري تحميل النسخ الاحتياطية..."})]}):n.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(_,{className:"mx-auto h-16 w-16 text-gray-400 dark:text-gray-500 mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"لا توجد نسخ احتياطية"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"لم يتم العثور على أي نسخ احتياطية. قم بإنشاء نسخة احتياطية أولاً."})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4 text-center",children:[e.jsxs("div",{className:"p-3 sm:p-0",children:[e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-primary-600 dark:text-primary-400",children:n.length}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-300",children:"إجمالي النسخ"})]}),e.jsxs("div",{className:"p-3 sm:p-0",children:[e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-success-600 dark:text-success-400",children:n.reduce((h,l)=>h+l.size_bytes,0)>1024*1024?`${(n.reduce((h,l)=>h+l.size_bytes,0)/(1024*1024)).toFixed(1)} MB`:`${(n.reduce((h,l)=>h+l.size_bytes,0)/1024).toFixed(1)} KB`}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-300",children:"الحجم الإجمالي"})]}),e.jsxs("div",{className:"p-3 sm:p-0",children:[e.jsx("p",{className:"text-xl sm:text-2xl font-bold text-warning-600 dark:text-warning-400",children:10-n.length}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-300",children:"نسخ متاحة"})]})]})}),e.jsx("div",{className:"space-y-3",children:n.map((h,l)=>e.jsxs("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-200",children:[e.jsxs("div",{className:"hidden sm:flex items-center justify-between",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(_,{className:"text-primary-600 dark:text-primary-400 ml-3 flex-shrink-0"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h5",{className:"font-medium text-gray-900 dark:text-gray-100 break-all",children:h.name}),e.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse mt-1",children:[e.jsxs("div",{className:"flex items-center text-sm text-gray-600 dark:text-gray-300",children:[e.jsx(pe,{className:"ml-1"}),e.jsx("span",{children:h.size})]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-600 dark:text-gray-300",children:[e.jsx(ue,{className:"ml-1"}),e.jsxs("span",{children:[h.created_date," - ",h.created_time]})]})]})]})]})}),e.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[e.jsx("button",{onClick:()=>N(h.name),className:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200 p-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20",title:"استعادة النسخة الاحتياطية",children:e.jsx(be,{className:"h-5 w-5"})}),e.jsx("button",{onClick:()=>p(h.name),className:"text-danger-600 hover:text-danger-900 dark:text-danger-400 dark:hover:text-danger-300 transition-colors duration-200 p-2 rounded-lg hover:bg-danger-50 dark:hover:bg-danger-900/20",title:"حذف النسخة الاحتياطية",children:e.jsx(X,{className:"h-5 w-5"})})]})]}),e.jsxs("div",{className:"sm:hidden",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center flex-1 min-w-0",children:[e.jsx(_,{className:"text-primary-600 dark:text-primary-400 ml-2 flex-shrink-0"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("h5",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:["نسخة احتياطية #",l+1]}),l===0&&e.jsx("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300 mt-1",children:"الأحدث"})]})]}),e.jsxs("div",{className:"flex items-center space-x-1 space-x-reverse ml-2",children:[e.jsx("button",{onClick:()=>N(h.name),className:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200 p-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20",title:"استعادة",children:e.jsx(be,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>p(h.name),className:"text-danger-600 hover:text-danger-900 dark:text-danger-400 dark:hover:text-danger-300 transition-colors duration-200 p-2 rounded-lg hover:bg-danger-50 dark:hover:bg-danger-900/20",title:"حذف",children:e.jsx(X,{className:"h-4 w-4"})})]})]}),e.jsx("div",{className:"mb-3 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs text-gray-600 dark:text-gray-300 break-all",children:h.name}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center text-gray-500 dark:text-gray-400 mb-1",children:[e.jsx(pe,{className:"ml-1 h-3 w-3"}),e.jsx("span",{className:"text-xs",children:"الحجم"})]}),e.jsx("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:h.size})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center text-gray-500 dark:text-gray-400 mb-1",children:[e.jsx(ue,{className:"ml-1 h-3 w-3"}),e.jsx("span",{className:"text-xs",children:"التاريخ"})]}),e.jsx("p",{className:"font-medium text-gray-900 dark:text-gray-100",children:h.created_date}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:h.created_time})]})]})]}),l===0&&e.jsx("div",{className:"hidden sm:block mt-2",children:e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300",children:"أحدث نسخة احتياطية"})})]},h.name))}),e.jsx("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mt-6",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-300",children:"حد النسخ الاحتياطية"}),e.jsx("p",{className:"mt-1 text-sm text-yellow-700 dark:text-yellow-400",children:"يحتفظ النظام بحد أقصى 10 نسخ احتياطية. عند إنشاء النسخة الحادية عشرة، سيتم حذف أقدم نسخة تلقائياً."})]})]})})]}),e.jsx("div",{className:"flex justify-end mt-6 pt-4 border-t border-gray-200 dark:border-gray-700",children:e.jsx("button",{onClick:s,className:"btn-secondary",children:"إغلاق"})})]})})},It=({className:c=""})=>{const{token:s,isAuthenticated:r,isInitialized:t,isLoading:n,error:x,clearAuth:f}=Qe(),[g,k]=w.useState({status:"idle",message:""}),N=()=>{f(),window.location.reload()},p=async()=>{if(!s){k({status:"error",message:"لا يوجد توكن للاختبار"});return}k({status:"testing",message:"جاري اختبار التوكن..."});try{const $=await fetch(`${(()=>{const z=window.location.hostname;return z==="localhost"||z==="127.0.0.1"?"http://localhost:8002":`http://${z}:8002`})()}/api/auth/me`,{headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if($.ok){const z=await $.json();console.log("Token test successful:",z),k({status:"success",message:`التوكن صالح - مستخدم: ${z.username}`})}else console.error("Token test failed:",$.status,$.statusText),k({status:"error",message:`فشل اختبار التوكن: ${$.status}`})}catch(y){console.error("Token test error:",y),k({status:"error",message:"خطأ في الاتصال بالخادم"})}},l=(()=>{if(!s)return null;try{const y=JSON.parse(atob(s.split(".")[1])),$=new Date(y.exp*1e3),re=$<new Date;return{username:y.sub,role:y.role,expiresAt:$.toLocaleString("ar-LY"),isExpired:re}}catch{return null}})();return e.jsxs("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${c}`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3",children:e.jsx(he,{className:"text-xl"})}),e.jsx("h3",{className:"text-lg font-bold text-gray-900 dark:text-gray-100",children:"حالة التوثيق"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"حالة التهيئة:"}),e.jsx("span",{className:`text-sm font-bold ${t?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:t?"مُهيأ":"غير مُهيأ"})]}),e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"حالة المصادقة:"}),e.jsx("span",{className:`text-sm font-bold ${r?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:r?"مُصادق":"غير مُصادق"})]}),e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"حالة التحميل:"}),e.jsx("span",{className:`text-sm font-bold ${n?"text-yellow-600 dark:text-yellow-400":"text-gray-600 dark:text-gray-400"}`,children:n?"يحمل...":"مكتمل"})]}),e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"التوكن:"}),e.jsx("span",{className:`text-sm font-bold ${s?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:s?"موجود":"غير موجود"})]})]}),l&&e.jsxs("div",{className:"mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800",children:[e.jsx("h4",{className:"text-sm font-bold text-green-900 dark:text-green-100 mb-2",children:"معلومات التوكن:"}),e.jsxs("div",{className:"space-y-1 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-green-700 dark:text-green-300",children:"المستخدم:"}),e.jsx("span",{className:"font-medium text-green-900 dark:text-green-100",children:l.username})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-green-700 dark:text-green-300",children:"الدور:"}),e.jsx("span",{className:"font-medium text-green-900 dark:text-green-100",children:l.role})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-green-700 dark:text-green-300",children:"ينتهي في:"}),e.jsx("span",{className:`font-medium ${l.isExpired?"text-red-600 dark:text-red-400":"text-green-900 dark:text-green-100"}`,children:l.expiresAt})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-green-700 dark:text-green-300",children:"الحالة:"}),e.jsx("span",{className:`font-medium ${l.isExpired?"text-red-600 dark:text-red-400":"text-green-600 dark:text-green-400"}`,children:l.isExpired?"منتهي الصلاحية":"صالح"})]})]})]}),x&&e.jsxs("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(ye,{className:"text-red-600 dark:text-red-400 ml-2"}),e.jsx("h4",{className:"text-sm font-bold text-red-900 dark:text-red-100",children:"خطأ في التوثيق:"})]}),e.jsx("p",{className:"text-sm text-red-700 dark:text-red-300 mt-1",children:x})]}),g.status!=="idle"&&e.jsx("div",{className:`mb-6 p-4 rounded-lg border ${g.status==="success"?"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800":g.status==="error"?"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800":"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800"}`,children:e.jsxs("div",{className:"flex items-center",children:[g.status==="success"&&e.jsx(St,{className:"text-green-600 dark:text-green-400 ml-2"}),g.status==="error"&&e.jsx(ye,{className:"text-red-600 dark:text-red-400 ml-2"}),g.status==="testing"&&e.jsx(W,{className:"text-yellow-600 dark:text-yellow-400 ml-2 animate-spin"}),e.jsx("p",{className:`text-sm font-medium ${g.status==="success"?"text-green-900 dark:text-green-100":g.status==="error"?"text-red-900 dark:text-red-100":"text-yellow-900 dark:text-yellow-100"}`,children:g.message})]})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsxs("button",{onClick:p,disabled:!s||g.status==="testing",className:"flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors text-sm font-medium",children:[e.jsx(Ft,{className:"ml-2"}),e.jsx("span",{children:"اختبار التوكن"})]}),e.jsxs("button",{onClick:N,className:"flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm font-medium",children:[e.jsx(X,{className:"ml-2"}),e.jsx("span",{children:"مسح البيانات"})]})]})]})},Zt=()=>{const{type:c}=Nt(),s=vt(),{theme:r}=wt(),t=r==="dark",{salesTrends:n,previousPeriodSales:x,previousPeriodTotal:f,productCategories:g,inactiveProducts:k,inventoryStatus:N,systemStats:p,isLoading:h,selectedPeriod:l,selectedReportType:y,fetchSalesTrends:$,fetchPreviousPeriodTotal:z,fetchProductCategories:re,fetchInventoryStatus:we,fetchSystemStats:ne,fetchDashboardStats:We,setReportType:Se,setPeriod:Ze}=_t(),{user:R}=Qe(),[ee,Ye]=w.useState([]),[Fe,qe]=w.useState(!1),[le,de]=w.useState({isOpen:!1,backupName:"",isLoading:!1}),[ie,oe]=w.useState({isOpen:!1,backupInfo:null,isLoading:!1}),[ce,M]=w.useState({isOpen:!1,title:"",message:"",details:null,autoClose:!1}),[V,B]=w.useState({isOpen:!1,type:"backup",title:"",message:"",description:"",isLoading:!1}),[Je,Ce]=w.useState(!1),fe=async()=>{try{qe(!0);const i=(await D.get("/api/dashboard/backups")).data;Ye(i.backups||[])}catch(a){console.error("Error fetching backups:",a),Ye([])}finally{qe(!1)}},De=a=>{de({isOpen:!0,backupName:a,isLoading:!1})},Xe=async()=>{de(a=>({...a,isLoading:!0}));try{const i=(await D.delete(`/api/dashboard/backups/${le.backupName}`)).data;if(i.success)de({isOpen:!1,backupName:"",isLoading:!1}),M({isOpen:!0,title:"تم الحذف بنجاح",message:"تم حذف النسخة الاحتياطية بنجاح",details:{backup_name:le.backupName},autoClose:!0}),fe(),ne();else throw new Error(i.message||"خطأ غير معروف")}catch(a){console.error("Error deleting backup:",a),de(i=>({...i,isLoading:!1})),M({isOpen:!0,title:"فشل في الحذف",message:`فشل في حذف النسخة الاحتياطية: ${a instanceof Error?a.message:"خطأ غير معروف"}`,details:null,autoClose:!1})}},$e=a=>{const i=ee.find(j=>j.name===a);oe({isOpen:!0,backupInfo:i,isLoading:!1})},et=async()=>{if(ie.backupInfo){oe(a=>({...a,isLoading:!0}));try{const i=(await D.post(`/api/dashboard/restore-backup/${ie.backupInfo.name}`)).data;if(i.success)oe({isOpen:!1,backupInfo:null,isLoading:!1}),M({isOpen:!0,title:"تم الاستعادة بنجاح",message:"تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تحميل النظام خلال 3 ثوانٍ لتطبيق التغييرات.",details:{backup_name:i.backup_name,size:i.size,restored_at:i.restored_at,backup_before_restore:i.backup_before_restore},autoClose:!0}),setTimeout(()=>{window.location.reload()},3e3);else throw new Error(i.message||"خطأ غير معروف")}catch(a){console.error("Error restoring backup:",a),oe(i=>({...i,isLoading:!1})),M({isOpen:!0,title:"فشل في الاستعادة",message:`فشل في استعادة النسخة الاحتياطية: ${a instanceof Error?a.message:"خطأ غير معروف"}`,details:null,autoClose:!1})}}},[E,Ae]=w.useState(null),[te,Be]=w.useState(!1),[Ge,tt]=w.useState(new Date),[O,Ve]=w.useState(()=>{const a=Q(),i=a.getFullYear(),j=(a.getMonth()+1).toString().padStart(2,"0"),F=a.getDate().toString().padStart(2,"0");return`${i}-${j}-${F}`}),se=async(a,i=!1)=>{try{const j=a||O;if(te||!i&&E&&E.date===j)return;if(Be(!0),!R||R.role!=="admin"){Ae(null),Be(!1);return}const L=(await D.get(`/api/dashboard/daily-user-sales?date=${j}`)).data;Ae(L),tt(new Date)}catch(j){console.error("Error fetching daily user sales:",j),Ae(null)}finally{Be(!1)}};w.useEffect(()=>{c&&(c==="sales"||c==="products"||c==="inventory"||c==="customers"||c==="system"||c==="daily-users")&&Se(c)},[c,Se]),w.useEffect(()=>{y==="sales"?($(l),z(l),We()):y==="products"?re():y==="inventory"?we():y==="system"?(ne(),fe()):y==="daily-users"&&se(O)},[y,l,O,$,z,re,we,ne,We]),w.useEffect(()=>{y==="daily-users"&&O&&R&&R.role==="admin"&&se(O)},[]);const xe=a=>{Se(a),s(`/reports/${a}`)},je=a=>{Ze(a)},st=()=>{const a=n.map(o=>({date:o.date,amount:o.amount})),i=a.reduce((o,b)=>o+(typeof b.amount=="number"?b.amount:0),0),j=a.map(o=>typeof o.amount=="number"?o.amount:0).filter(o=>o>0),F=j.length>0?Math.max(...j):0,L=j.length>0?Math.min(...j):0,d=a.length>0?i/a.length:0;console.log("Sales Statistics:",{chartDataLength:a.length,validAmountsLength:j.length,totalSales:i,maxSale:F,minSale:L,avgSale:d,validAmounts:j.slice(0,5)});const m=a.reduce((o,b)=>{const C=b.amount-d;return o+C*C},0)/a.length,u=Math.sqrt(m),Y=(()=>{if(a.length===0)return{rate:0,currentTotal:0,previousTotal:0,hasData:!1};const o=a.reduce((P,T)=>P+(typeof T.amount=="number"?T.amount:0),0),b=f;return console.log("Growth Rate Calculation:",{chartDataLength:a.length,currentPeriodTotal:o,actualPreviousPeriodTotal:b,selectedPeriod:l}),b===0?{rate:o>0?100:0,currentTotal:o,previousTotal:0,hasData:!0}:{rate:(o-b)/b*100,currentTotal:o,previousTotal:b,hasData:!0}})(),A=Y.rate,v=l==="day"?"line":l==="week"?"area":l==="month"?"bar":"line",H=x.map(o=>{const b=typeof o.amount=="number"?o.amount:0;return Math.round(b*100)/100}),I={chart:{type:v,fontFamily:"Tajawal, sans-serif",toolbar:{show:!0,tools:{download:!0,selection:!1,zoom:!0,zoomin:!0,zoomout:!0,pan:!1,reset:!0},export:{csv:{filename:"تقرير_المبيعات",columnDelimiter:",",headerCategory:"التاريخ",headerValue:"القيمة"},svg:{filename:"مخطط_المبيعات"},png:{filename:"مخطط_المبيعات"}}},zoom:{enabled:!0,type:"x",autoScaleYaxis:!0},animations:{enabled:!0,speed:1e3,animateGradually:{enabled:!0,delay:200},dynamicAnimation:{enabled:!0,speed:400}},background:"transparent",locales:[{name:"ar",options:{months:["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"],shortMonths:["ي","ف","م","أ","م","ي","ي","أ","س","أ","ن","د"],days:["الأحد","الإثنين","الثلاثاء","الأربعاء","الخميس","الجمعة","السبت"],shortDays:["أحد","إثن","ثلا","أرب","خمي","جمع","سبت"],toolbar:{exportToSVG:"تحميل SVG",exportToPNG:"تحميل PNG",exportToCSV:"تحميل CSV",selection:"التحديد",selectionZoom:"تكبير التحديد",zoomIn:"تكبير",zoomOut:"تصغير",pan:"تحريك",reset:"إعادة تعيين التكبير"}}}],defaultLocale:"ar"},colors:v==="bar"?t?["#10B981","rgba(248, 113, 113, 0.7)"]:["#059669","rgba(220, 38, 38, 0.7)"]:t?["#3B82F6","rgba(248, 113, 113, 0.7)"]:["#1D4ED8","rgba(220, 38, 38, 0.7)"],fill:{type:v==="bar"?"solid":"gradient",gradient:{shade:t?"dark":"light",shadeIntensity:.8,opacityFrom:v==="area"?.7:.9,opacityTo:v==="area"?.1:.2,stops:[0,90,100],colorStops:[[{offset:0,color:t?"#3B82F6":"#1D4ED8",opacity:v==="area"?.7:.9},{offset:100,color:t?"#1E40AF":"#1E3A8A",opacity:v==="area"?.1:.2}],[{offset:0,color:t?"#F87171":"#DC2626",opacity:v==="area"?.5:.7},{offset:100,color:t?"#DC2626":"#B91C1C",opacity:v==="area"?.1:.2}]]}},dataLabels:{enabled:v==="bar",style:{fontSize:"12px",fontWeight:"bold",colors:[t?"#F9FAFB":"#fff"]},formatter:o=>o>=1e3?`${(o/1e3).toFixed(1)}K`:o.toFixed(0)},stroke:{curve:v==="line"?"smooth":"straight",width:v==="bar"?0:[4,3],dashArray:[0,8]},grid:{borderColor:t?"rgba(156, 163, 175, 0.3)":"rgba(107, 114, 128, 0.1)",row:{colors:["transparent","transparent"],opacity:t?.3:.5},padding:{top:0,right:0,bottom:0,left:10},xaxis:{lines:{show:!0}},yaxis:{lines:{show:!0}}},markers:{size:[6,5],colors:[t?"#1F2937":"#FFFFFF",t?"#1F2937":"#FFFFFF"],strokeColors:[t?"#3B82F6":"#1D4ED8",t?"#F87171":"#DC2626"],strokeWidth:[3,3],shape:["circle","circle"],hover:{size:9,sizeOffset:4}},xaxis:{categories:a.map(o=>o.date),labels:{style:{colors:t?"#9CA3AF":"#6B7280",fontSize:window.innerWidth<768?"10px":"12px"},formatter:o=>{if(!o)return"";try{if(l==="day")return o;if(l==="week"||l==="month"){if(o.includes("-")){const b=o.split("-");if(b.length===3)return b[2]}return o}else{if(o.includes("-")){const b=o.split("-");if(b.length===2){const C=parseInt(b[1]);if(!isNaN(C)&&C>=1&&C<=12)return["ي","ف","م","أ","م","ي","ي","أ","س","أ","ن","د"][C-1]}}return o}}catch(b){return console.error("Error formatting x-axis label:",b,o),o}}},axisBorder:{show:!0,color:t?"rgba(156, 163, 175, 0.4)":"rgba(107, 114, 128, 0.3)"},axisTicks:{show:!0,color:t?"rgba(156, 163, 175, 0.4)":"rgba(107, 114, 128, 0.3)"},tickAmount:l==="day"?8:void 0},yaxis:{labels:{style:{colors:t?"#9CA3AF":"#6B7280",fontSize:window.innerWidth<768?"10px":"12px"},formatter:o=>o.toFixed(0)}},legend:{show:!0,position:"bottom",horizontalAlign:"center",floating:!1,fontSize:"14px",fontFamily:"Cairo, sans-serif",fontWeight:600,labels:{colors:t?"#E5E7EB":"#374151",useSeriesColors:!0},markers:{size:10,strokeWidth:3,fillColors:[t?"#3B82F6":"#1D4ED8",t?"#F87171":"#DC2626"],shape:"circle",offsetX:0,offsetY:0},itemMargin:{horizontal:20,vertical:8},offsetY:15},tooltip:{enabled:!0,shared:!0,intersect:!1,theme:t?"dark":"light",fillSeriesColor:!1,style:{fontSize:"13px",fontFamily:"Cairo, sans-serif"},custom:function({series:o,dataPointIndex:b}){try{if(b<0||b>=a.length)return"";const C=o[0]?o[0][b]:0,P=o[1]?o[1][b]:0,T=a[b].date;let ae="";if(l==="day")ae=`الساعة ${T}`;else if(l==="week"||l==="month")if(T&&T.includes("-")){const q=T.split("-");if(q.length===3){const Le=q[0],U=parseInt(q[1]),me=parseInt(q[2]);!isNaN(U)&&!isNaN(me)&&U>=1&&U<=12&&me>=1&&me<=31&&(ae=`${me} ${["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"][U-1]} ${Le}`)}}else ae=T||"";else if(l==="year")if(T&&T.includes("-")){const q=T.split("-");if(q.length===2){const Le=q[0],U=parseInt(q[1]);!isNaN(U)&&U>=1&&U<=12&&(ae=`${["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"][U-1]} ${Le}`)}}else ae=T||"";const ut=l==="day"?"اليوم":l==="week"?"هذا الأسبوع":l==="month"?"هذا الشهر":"هذه السنة",bt=l==="day"?"أمس":l==="week"?"الأسبوع السابق":l==="month"?"الشهر السابق":"السنة السابقة";return`
              <div style="
                background: ${t?"rgba(31, 41, 55, 0.95)":"rgba(255, 255, 255, 0.96)"};
                border: 1px solid ${t?"rgba(55, 65, 81, 0.3)":"rgba(229, 231, 235, 0.4)"};
                border-radius: 2px;
                padding: 16px 20px;
                box-shadow: 0 4px 15px ${t?"rgba(0, 0, 0, 0.4)":"rgba(0, 0, 0, 0.05)"},
                           0 2px 6px ${t?"rgba(0, 0, 0, 0.25)":"rgba(0, 0, 0, 0.1)"};
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                font-family: 'Cairo', sans-serif;
                direction: rtl;
                min-width: 200px;
                max-width: 260px;
                position: relative;
                overflow: hidden;
              ">
                <!-- Header with date -->
                <div style="
                  background: ${t?"rgba(59, 130, 246, 0.08)":"rgba(59, 130, 246, 0.04)"};
                  margin: -16px -20px 14px -20px;
                  padding: 10px 20px;
                  border-bottom: 1px solid ${t?"rgba(59, 130, 246, 0.12)":"rgba(59, 130, 246, 0.08)"};
                  border-radius: 2px 2px 0 0;
                ">
                  <div style="
                    color: ${t?"#E2E8F0":"#475569"};
                    font-size: 11px;
                    font-weight: 500;
                    text-align: center;
                    letter-spacing: 0.3px;
                  ">
                    ${ae}
                  </div>
                </div>

                <!-- Data rows -->
                <div style="
                  display: flex;
                  flex-direction: column;
                  gap: 10px;
                ">
                  <!-- Current period -->
                  <div style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 10px 14px;
                    background: ${t?"rgba(59, 130, 246, 0.06)":"rgba(59, 130, 246, 0.03)"};
                    border-radius: 12px;
                    border: 1px solid ${t?"rgba(59, 130, 246, 0.12)":"rgba(59, 130, 246, 0.08)"};
                  ">
                    <div style="
                      display: flex;
                      align-items: center;
                      gap: 8px;
                    ">
                      <div style="
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background: ${t?"#3B82F6":"#2563EB"};
                        box-shadow: 0 0 0 2px ${t?"rgba(59, 130, 246, 0.3)":"rgba(37, 99, 235, 0.2)"};
                        flex-shrink: 0;
                      "></div>
                      <span style="
                        color: ${t?"#F8FAFC":"#1E293B"};
                        font-size: 12px;
                        font-weight: 600;
                      ">
                        ${ut}
                      </span>
                    </div>
                    <div style="
                      color: ${t?"#3B82F6":"#2563EB"};
                      font-size: 13px;
                      font-weight: 700;
                      direction: ltr;
                    ">
                      ${C.toLocaleString()} د.ل
                    </div>
                  </div>

                  <!-- Previous period -->
                  <div style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 10px 14px;
                    background: ${t?"rgba(248, 113, 113, 0.06)":"rgba(239, 68, 68, 0.03)"};
                    border-radius: 12px;
                    border: 1px solid ${t?"rgba(248, 113, 113, 0.12)":"rgba(239, 68, 68, 0.08)"};
                  ">
                    <div style="
                      display: flex;
                      align-items: center;
                      gap: 8px;
                    ">
                      <div style="
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background: ${t?"rgba(248, 113, 113, 0.8)":"rgba(239, 68, 68, 0.8)"};
                        box-shadow: 0 0 0 2px ${t?"rgba(248, 113, 113, 0.3)":"rgba(239, 68, 68, 0.2)"};
                        flex-shrink: 0;
                      "></div>
                      <span style="
                        color: ${t?"#F8FAFC":"#1E293B"};
                        font-size: 12px;
                        font-weight: 600;
                      ">
                        ${bt}
                      </span>
                    </div>
                    <div style="
                      color: ${t?"rgba(248, 113, 113, 0.9)":"rgba(239, 68, 68, 0.9)"};
                      font-size: 13px;
                      font-weight: 700;
                      direction: ltr;
                    ">
                      ${P.toLocaleString()} د.ل
                    </div>
                  </div>
                </div>

                ${C>0&&P>0?`
                  <!-- Comparison section -->
                  <div style="
                    margin-top: 14px;
                    padding: 10px 14px;
                    background: ${t?"rgba(156, 163, 175, 0.06)":"rgba(156, 163, 175, 0.03)"};
                    border-radius: 12px;
                    border: 1px solid ${t?"rgba(156, 163, 175, 0.12)":"rgba(156, 163, 175, 0.08)"};
                    text-align: center;
                  ">
                    <div style="
                      color: ${t?"#9CA3AF":"#6B7280"};
                      font-size: 10px;
                      font-weight: 500;
                      margin-bottom: 2px;
                    ">
                      نسبة التغيير
                    </div>
                    <div style="
                      color: ${(C-P)/P*100>=0?t?"#10B981":"#059669":t?"#F87171":"#DC2626"};
                      font-size: 12px;
                      font-weight: 700;
                      direction: ltr;
                    ">
                      ${(C-P)/P*100>=0?"+":""}${((C-P)/P*100).toFixed(1)}%
                    </div>
                  </div>
                `:""}
              </div>
            `}catch(C){return console.error("Error formatting tooltip:",C),""}}},responsive:[{breakpoint:768,options:{chart:{height:280},markers:{size:[5,4],strokeWidth:[2,2]},xaxis:{labels:{style:{fontSize:"10px",colors:t?"#9CA3AF":"#6B7280"},rotate:0,offsetY:0}},yaxis:{labels:{style:{fontSize:"10px",colors:t?"#9CA3AF":"#6B7280"},formatter:o=>o>=1e3?`${(o/1e3).toFixed(1)}K`:o.toFixed(0)}}}}]},pt=[{name:`المبيعات ${l==="day"?"اليوم":l==="week"?"هذا الأسبوع":l==="month"?"هذا الشهر":"هذه السنة"}`,type:v,data:a.map(o=>{const b=typeof o.amount=="number"?o.amount:0;return Math.round(b*100)/100})},{name:`${l==="day"?"أمس":l==="week"?"الأسبوع السابق":l==="month"?"الشهر السابق":"السنة السابقة"}`,type:"line",data:H}];return e.jsxs("div",{className:"touch-card",children:[e.jsx("div",{className:`h-96 md:h-80 ${t?"dark-chart":"light-chart"}`,children:e.jsx(Ne,{type:v,height:"100%",width:"100%",options:I,series:pt})}),e.jsxs("div",{className:"mt-6 space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider",children:"أعلى قيمة"}),e.jsxs("p",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2",children:[F.toLocaleString()," ",e.jsx("span",{className:"text-lg font-medium text-gray-600 dark:text-gray-400",children:"د.ل"})]}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"أعلى مبيعات في الفترة"})]}),e.jsx("div",{className:"w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:e.jsx(G,{className:"text-lg text-green-600 dark:text-green-400"})})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider",children:"متوسط المبيعات"}),e.jsxs("p",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2",children:[Math.round(d).toLocaleString()," ",e.jsx("span",{className:"text-lg font-medium text-gray-600 dark:text-gray-400",children:"د.ل"})]}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"متوسط قيمة المبيعات"})]}),e.jsx("div",{className:"w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:e.jsx(K,{className:"text-lg text-blue-600 dark:text-blue-400"})})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider",children:"إجمالي المبيعات"}),e.jsxs("p",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2",children:[i.toLocaleString()," ",e.jsx("span",{className:"text-lg font-medium text-gray-600 dark:text-gray-400",children:"د.ل"})]}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"مجموع مبيعات الفترة"})]}),e.jsx("div",{className:"w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:e.jsx(ge,{className:"text-lg text-indigo-600 dark:text-indigo-400"})})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider",children:"أقل قيمة"}),e.jsxs("p",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2",children:[L>0?L.toLocaleString():"0"," ",e.jsx("span",{className:"text-lg font-medium text-gray-600 dark:text-gray-400",children:"د.ل"})]}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:L>0?"أدنى مبيعات في الفترة":"لا توجد مبيعات في الفترة"})]}),e.jsx("div",{className:"w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:e.jsx(_e,{className:"text-lg text-orange-600 dark:text-orange-400"})})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm font-bold uppercase tracking-wider mb-3",children:"معدل النمو"}),e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsxs("p",{className:`text-2xl sm:text-3xl font-bold leading-tight ${A>=0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`,children:[A>=0?"+":"",A.toFixed(1),"%"]}),e.jsxs("div",{className:`flex items-center px-2 py-1 rounded-full text-xs font-medium ${A>=0?"bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400":"bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400"}`,children:[A>=0?e.jsx(G,{className:"text-xs mr-1"}):e.jsx(_e,{className:"text-xs mr-1"}),e.jsx("span",{children:A>=0?"نمو":"انخفاض"})]})]}),h?e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"جاري تحميل البيانات..."}):Y.hasData?e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:l==="day"?"مقارنة مع اليوم السابق":l==="week"?"مقارنة مع الأسبوع السابق":l==="month"?"مقارنة مع الشهر السابق":"مقارنة مع السنة السابقة"}),e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-green-500 dark:bg-green-600 flex-shrink-0"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-300 font-medium",children:"الحالية:"}),e.jsxs("span",{className:"text-gray-900 dark:text-gray-100 font-bold",children:[Y.currentTotal.toLocaleString("ar-LY",{minimumFractionDigits:1,maximumFractionDigits:1})," د.ل"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-400 dark:bg-gray-500 flex-shrink-0"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-300 font-medium",children:"السابقة:"}),e.jsxs("span",{className:"text-gray-900 dark:text-gray-100 font-bold",children:[Y.previousTotal.toLocaleString("ar-LY",{minimumFractionDigits:1,maximumFractionDigits:1})," د.ل"]})]})]})]}):e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"لا توجد بيانات للفترة السابقة للمقارنة"})]}),e.jsx("div",{className:"w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:e.jsx(Ee,{className:`text-lg ${A>=0?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"}`})})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider",children:"الانحراف المعياري"}),e.jsx("p",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2",children:Math.round(u).toLocaleString()}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"مقياس تشتت البيانات"})]}),e.jsx("div",{className:"w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:e.jsx(W,{className:"text-lg text-blue-600 dark:text-blue-400"})})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-gray-600 dark:text-gray-300 text-sm font-bold mb-4 uppercase tracking-wider",children:"عدد النقاط"}),e.jsxs("p",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 leading-tight mb-2",children:[a.length," ",e.jsx("span",{className:"text-lg font-medium text-gray-600 dark:text-gray-400",children:"نقطة"})]}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"عدد نقاط البيانات"})]}),e.jsx("div",{className:"w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center",children:e.jsx(ue,{className:"text-lg text-purple-600 dark:text-purple-400"})})]})})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-5 lg:p-6 rounded-xl border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3",children:e.jsx(K,{className:"text-gray-600 dark:text-gray-300 text-sm"})}),e.jsx("span",{className:"text-gray-900 dark:text-gray-100 font-bold text-lg",children:"مؤشر الأداء"})]}),e.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400 font-medium",children:l==="day"?"مقارنة بالساعات":l==="week"||l==="month"?"مقارنة بالأيام":"مقارنة بالأشهر"})]}),e.jsx("div",{className:"w-full h-4 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden shadow-inner",children:e.jsx("div",{className:`h-full rounded-full transition-all duration-1000 shadow-sm ${d>F*.7?"bg-gradient-to-r from-green-400 to-green-600":d>F*.4?"bg-gradient-to-r from-yellow-400 to-orange-500":"bg-gradient-to-r from-red-400 to-red-600"}`,style:{width:`${Math.min(d/F*100,100)}%`}})}),e.jsxs("div",{className:"flex justify-between items-center text-xs font-medium mt-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full mr-2"}),e.jsx("span",{className:"text-red-600 dark:text-red-400",children:"ضعيف"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 bg-yellow-500 rounded-full mr-2"}),e.jsx("span",{className:"text-yellow-600 dark:text-yellow-400",children:"متوسط"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-2"}),e.jsx("span",{className:"text-green-600 dark:text-green-400",children:"ممتاز"})]})]}),e.jsx("div",{className:"mt-3 text-center",children:e.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["النسبة الحالية: ",e.jsxs("span",{className:"font-bold text-gray-900 dark:text-gray-100",children:[Math.min(d/F*100,100).toFixed(1),"%"]})]})})]})]})]})},at=()=>{const a=g.map(u=>u.value),i=g.map(u=>u.name),j={chart:{type:"donut",fontFamily:"Tajawal, sans-serif",toolbar:{show:!1},animations:{enabled:!0,speed:500,animateGradually:{enabled:!0,delay:150},dynamicAnimation:{enabled:!0,speed:350}},background:"transparent"},theme:{mode:t?"dark":"light",palette:"palette1"},colors:t?["#34D399","#FBBF24","#F87171","#60A5FA","#A78BFA","#F472B6","#38BDF8","#4ADE80"]:["#10B981","#D97706","#DC2626","#2563EB","#7C3AED","#DB2777","#0284C7","#16A34A"],labels:i,dataLabels:{enabled:!0,formatter:u=>`${u.toFixed(1)}%`,style:{fontSize:"13px",fontFamily:"Cairo, sans-serif",fontWeight:"700",colors:["#FFFFFF"]},dropShadow:{enabled:!0,top:1,left:1,blur:2,color:"#000000",opacity:.6}},stroke:{show:!0,curve:"smooth",lineCap:"round",width:3,colors:[t?"#1F2937":"#FFFFFF"]},fill:{type:"solid"},plotOptions:{pie:{donut:{size:"75%",labels:{show:!0,name:{show:!0,fontSize:"14px",fontFamily:"Cairo, sans-serif",fontWeight:600,color:t?"#9CA3AF":"#6B7280",offsetY:-25,formatter:function(){return"النسبة المئوية"}},value:{show:!0,fontSize:"32px",fontFamily:"Cairo, sans-serif",fontWeight:800,color:t?"#60A5FA":"#3B82F6",offsetY:20,formatter:function(u){return`${u}%`}},total:{show:!0,showAlways:!0,label:"توزيع الفئات",fontSize:"28px",fontFamily:"Cairo, sans-serif",fontWeight:800,color:t?"#60A5FA":"#2563EB",formatter:function(){return"100%"}}}},expandOnClick:!0}},legend:{show:!1},tooltip:{theme:t?"dark":"light",style:{fontSize:"13px",fontFamily:"Cairo, sans-serif"},custom:function({series:u,seriesIndex:S,w:Y}){const A=Y.config.labels[S],v=u[S],H=g[S],I=(H==null?void 0:H.count)||0;return`
            <div style="
              background: ${t?"rgba(31, 41, 55, 0.95)":"rgba(255, 255, 255, 0.96)"};
              border: 1px solid ${t?"rgba(55, 65, 81, 0.3)":"rgba(229, 231, 235, 0.4)"};
              border-radius: 2px;
              padding: 12px 16px;
              box-shadow: 0 4px 15px ${t?"rgba(0, 0, 0, 0.4)":"rgba(0, 0, 0, 0.05)"},
                         0 2px 6px ${t?"rgba(0, 0, 0, 0.25)":"rgba(0, 0, 0, 0.1)"};
              backdrop-filter: blur(10px);
              font-family: 'Cairo', sans-serif;
              direction: rtl;
              min-width: 160px;
            ">
              <div style="
                color: ${t?"#F3F4F6":"#1F2937"};
                font-size: 13px;
                font-weight: 600;
                margin-bottom: 6px;
              ">
                ${A}
              </div>
              <div style="
                color: ${t?"#E2E8F0":"#475569"};
                font-size: 11px;
                margin-bottom: 4px;
              ">
                عدد المنتجات: ${I}
              </div>
              <div style="
                color: ${t?"#60A5FA":"#2563EB"};
                font-size: 14px;
                font-weight: 700;
              ">
                ${parseFloat(v).toFixed(1)}%
              </div>
            </div>
          `}},responsive:[{breakpoint:768,options:{chart:{height:300},legend:{position:"bottom",fontSize:"12px"},dataLabels:{enabled:!1}}}]},F=a,L=g.length,d=g.reduce((u,S)=>u+(S.count||0),0),m=g.reduce((u,S)=>(S.count||0)>(u.count||0)?S:u,g[0]||{name:"",count:0});return e.jsxs("div",{className:"touch-card",children:[e.jsx("div",{className:"h-80 md:h-96",children:e.jsx(Ne,{type:"donut",height:"100%",width:"100%",options:j,series:F})}),e.jsx("div",{className:"mt-6 flex flex-wrap justify-center gap-4",children:g.map((u,S)=>e.jsxs("div",{className:"flex items-center gap-3 px-4 py-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200",children:[e.jsx("div",{className:"w-4 h-4 rounded-full shadow-sm",style:{backgroundColor:(t?["#34D399","#FBBF24","#F87171","#60A5FA","#A78BFA","#F472B6","#38BDF8","#4ADE80"]:["#10B981","#D97706","#DC2626","#2563EB","#7C3AED","#DB2777","#0284C7","#16A34A"])[S%8]}}),e.jsxs("div",{className:"text-center",children:[e.jsx("span",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 block",children:u.name}),e.jsxs("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:[u.value.toFixed(1),"% • ",u.count||0," منتج"]})]})]},S))}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"إجمالي الفئات"}),e.jsx("p",{className:"text-2xl font-bold text-secondary-900 dark:text-secondary-100",children:L})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center",children:e.jsx(He,{className:"text-primary-600 dark:text-primary-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"إجمالي المنتجات"}),e.jsx("p",{className:"text-2xl font-bold text-secondary-900 dark:text-secondary-100",children:d.toLocaleString()})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center",children:e.jsx(ge,{className:"text-success-600 dark:text-success-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"أكبر فئة"}),e.jsx("p",{className:"text-lg font-bold text-secondary-900 dark:text-secondary-100",children:m.name||"لا توجد"}),e.jsxs("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:[m.count||0," منتج"]})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-warning-100 dark:bg-warning-900/30 flex items-center justify-center",children:e.jsx(G,{className:"text-warning-600 dark:text-warning-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"منتجات غير نشطة"}),e.jsx("p",{className:"text-2xl font-bold text-secondary-900 dark:text-secondary-100",children:k})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-danger-100 dark:bg-danger-900/30 flex items-center justify-center",children:e.jsx(K,{className:"text-danger-600 dark:text-danger-400 text-xl"})})]})})]})]})},rt=()=>{const a=N.map(m=>({x:m.name,y:m.quantity})),i={chart:{type:"bar",fontFamily:"Cairo, sans-serif",toolbar:{show:!1},zoom:{enabled:!1},animations:{enabled:!0,speed:800},background:"transparent"},theme:{mode:t?"dark":"light",palette:"palette1"},colors:t?["#34D399","#FBBF24","#F87171"]:["#10B981","#D97706","#DC2626"],dataLabels:{enabled:!0,formatter:m=>m>0?m.toString():"",style:{fontSize:"13px",fontFamily:"Cairo, sans-serif",fontWeight:"700",colors:["#FFFFFF"]},offsetY:-25,dropShadow:{enabled:!0,top:1,left:1,blur:2,color:"#000000",opacity:.3}},grid:{borderColor:t?"#374151":"#E5E7EB",strokeDashArray:3,xaxis:{lines:{show:!1}}},xaxis:{type:"category",labels:{style:{colors:t?"#9CA3AF":"#6B7280",fontSize:"12px"}},axisBorder:{show:!1},axisTicks:{show:!1}},yaxis:{labels:{style:{colors:t?"#9CA3AF":"#6B7280",fontSize:"12px"}}},tooltip:{theme:t?"dark":"light",style:{fontSize:"13px",fontFamily:"Cairo, sans-serif"},custom:function({series:m,dataPointIndex:u}){try{if(u<0||u>=N.length)return"";const S=N[u],Y=m[0][u],A=S.name,v=S.total_quantity||0,H=S.value||0;let I="";return A==="متوفر"?I=t?"#34D399":"#10B981":A==="منخفض"?I=t?"#FBBF24":"#D97706":A==="نفذ"&&(I=t?"#F87171":"#DC2626"),`
              <div style="
                background: ${t?"rgba(31, 41, 55, 0.95)":"rgba(255, 255, 255, 0.96)"};
                border: 1px solid ${t?"rgba(55, 65, 81, 0.3)":"rgba(229, 231, 235, 0.4)"};
                border-radius: 2px;
                padding: 12px 16px;
                box-shadow: 0 4px 15px ${t?"rgba(0, 0, 0, 0.4)":"rgba(0, 0, 0, 0.05)"},
                           0 2px 6px ${t?"rgba(0, 0, 0, 0.25)":"rgba(0, 0, 0, 0.1)"};
                backdrop-filter: blur(10px);
                font-family: 'Cairo', sans-serif;
                direction: rtl;
                min-width: 180px;
              ">
                <div style="
                  color: ${t?"#F3F4F6":"#1F2937"};
                  font-size: 14px;
                  font-weight: 600;
                  margin-bottom: 8px;
                  display: flex;
                  align-items: center;
                ">
                  <div style="
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: ${I};
                    margin-left: 8px;
                  "></div>
                  ${A}
                </div>
                <div style="
                  color: ${t?"#E2E8F0":"#475569"};
                  font-size: 11px;
                  margin-bottom: 4px;
                ">
                  عدد المنتجات: ${Y}
                </div>
                <div style="
                  color: ${t?"#E2E8F0":"#475569"};
                  font-size: 11px;
                  margin-bottom: 4px;
                ">
                  إجمالي الكمية: ${v.toLocaleString()}
                </div>
                <div style="
                  color: ${I};
                  font-size: 13px;
                  font-weight: 700;
                ">
                  القيمة: ${H.toLocaleString()} د.ل
                </div>
              </div>
            `}catch{return""}}},plotOptions:{bar:{borderRadius:8,columnWidth:"65%",dataLabels:{position:"top"},distributed:!0}},legend:{show:!0,position:"bottom",horizontalAlign:"center",fontSize:"12px",fontFamily:"Cairo, sans-serif",fontWeight:500,labels:{colors:t?"#E5E7EB":"#374151"},markers:{size:8,shape:"square"},itemMargin:{horizontal:15,vertical:5}},responsive:[{breakpoint:768,options:{chart:{height:280},plotOptions:{bar:{columnWidth:"80%"}},xaxis:{labels:{style:{fontSize:"10px"}}}}}]},j=[{name:"الكمية",data:a}],F=a.reduce((m,u)=>m+(typeof u.y=="number"?u.y:0),0),L=Math.max(...a.map(m=>typeof m.y=="number"?m.y:0)),d=a.length>0?F/a.length:0;return e.jsxs("div",{className:"touch-card",children:[e.jsx("div",{className:"h-80 md:h-64",children:e.jsx(Ne,{type:"bar",height:"100%",width:"100%",options:i,series:j})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"أعلى كمية"}),e.jsx("p",{className:"text-2xl font-bold text-secondary-900 dark:text-secondary-100",children:L.toLocaleString()}),e.jsx("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:"وحدة"})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center",children:e.jsx(G,{className:"text-success-600 dark:text-success-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"متوسط الكمية"}),e.jsx("p",{className:"text-2xl font-bold text-secondary-900 dark:text-secondary-100",children:Math.round(d).toLocaleString()}),e.jsx("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:"وحدة"})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center",children:e.jsx(K,{className:"text-primary-600 dark:text-primary-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"إجمالي المخزون"}),e.jsx("p",{className:"text-2xl font-bold text-secondary-900 dark:text-secondary-100",children:F.toLocaleString()}),e.jsx("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:"وحدة"})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-warning-100 dark:bg-warning-900/30 flex items-center justify-center",children:e.jsx(pe,{className:"text-warning-600 dark:text-warning-400 text-xl"})})]})})]})]})},nt=()=>e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3",children:e.jsx(Pe,{className:"text-xl"})}),e.jsx("h2",{className:"font-bold text-xl text-secondary-900 dark:text-secondary-100",children:"معلومات النظام"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6",children:[e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"إجمالي المستخدمين"}),e.jsx("p",{className:"text-2xl font-bold text-secondary-900 dark:text-secondary-100",children:p.totalUsers})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center",children:e.jsx(ke,{className:"text-primary-600 dark:text-primary-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"المستخدمين النشطين"}),e.jsx("p",{className:"text-2xl font-bold text-secondary-900 dark:text-secondary-100",children:p.activeUsers}),e.jsx("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:"آخر 30 يوم"})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center",children:e.jsx(he,{className:"text-success-600 dark:text-success-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"نسبة النشاط"}),e.jsxs("p",{className:"text-2xl font-bold text-success-600 dark:text-success-400",children:[p.totalUsers>0?Math.round(p.activeUsers/p.totalUsers*100):0,"%"]})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-info-100 dark:bg-info-900/30 flex items-center justify-center",children:e.jsx(G,{className:"text-info-600 dark:text-info-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"آخر تسجيل دخول"}),e.jsx("p",{className:"text-lg font-bold text-secondary-900 dark:text-secondary-100",children:p.lastLogin})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-warning-100 dark:bg-warning-900/30 flex items-center justify-center",children:e.jsx(he,{className:"text-warning-600 dark:text-warning-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"وقت تشغيل النظام"}),e.jsx("p",{className:"text-2xl font-bold text-secondary-900 dark:text-secondary-100",children:p.systemUptime})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center",children:e.jsx(Lt,{className:"text-purple-600 dark:text-purple-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"حجم قاعدة البيانات"}),e.jsx("p",{className:"text-2xl font-bold text-secondary-900 dark:text-secondary-100",children:p.databaseSize})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-secondary-100 dark:bg-secondary-900/30 flex items-center justify-center",children:e.jsx(pe,{className:"text-secondary-600 dark:text-secondary-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"آخر نسخة احتياطية"}),e.jsx("p",{className:"text-lg font-bold text-secondary-900 dark:text-secondary-100",children:p.lastBackup})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-danger-100 dark:bg-danger-900/30 flex items-center justify-center",children:e.jsx(_,{className:"text-danger-600 dark:text-danger-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"إصدار النظام"}),e.jsx("p",{className:"text-lg font-bold text-secondary-900 dark:text-secondary-100",children:"SmartPOS v1.0.0"}),e.jsx("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:"React + FastAPI"})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center",children:e.jsx(Me,{className:"text-blue-600 dark:text-blue-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"حالة الخادم"}),e.jsx("p",{className:"text-lg font-bold text-success-600 dark:text-success-400",children:"متصل"}),e.jsx("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:"localhost:8002"})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center",children:e.jsx(Pe,{className:"text-success-600 dark:text-success-400 text-xl"})})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"touch-card",children:[e.jsx("div",{className:"flex justify-between items-center mb-5",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-secondary-100 dark:bg-secondary-900/30 text-secondary-600 dark:text-secondary-300 p-2 rounded-lg ml-3",children:e.jsx(Me,{className:"text-xl"})}),e.jsx("h3",{className:"font-bold text-lg text-secondary-900 dark:text-secondary-100",children:"إجراءات النظام"})]})}),e.jsxs("div",{className:"flex flex-wrap gap-4 mb-6",children:[e.jsxs("button",{onClick:it,className:"btn-primary flex items-center",disabled:h,children:[e.jsx(_,{className:"ml-2"}),e.jsx("span",{children:"إنشاء نسخة احتياطية"})]}),e.jsxs("button",{onClick:ct,className:"btn-secondary flex items-center",disabled:h,children:[e.jsx(W,{className:"ml-2"}),e.jsx("span",{children:"تحديث النظام"})]}),e.jsxs("button",{onClick:mt,className:"btn-outline flex items-center",disabled:h,children:[e.jsx(X,{className:"ml-2"}),e.jsx("span",{children:"مسح التخزين المؤقت"})]})]}),e.jsx(It,{})]}),e.jsxs("div",{className:"touch-card",children:[e.jsxs("div",{className:"flex justify-between items-center mb-5",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300 p-2 rounded-lg ml-3",children:e.jsx(_,{className:"text-xl"})}),e.jsx("h3",{className:"font-bold text-lg text-secondary-900 dark:text-secondary-100",children:"النسخ الاحتياطية"})]}),e.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[e.jsxs("button",{onClick:()=>Ce(!0),className:"btn-outline-sm flex items-center",children:[e.jsx(_,{className:"ml-2"}),e.jsx("span",{children:"عرض الكل"})]}),e.jsxs("button",{onClick:fe,className:"btn-outline-sm flex items-center",disabled:Fe,children:[e.jsx(W,{className:`ml-2 ${Fe?"animate-spin":""}`}),e.jsx("span",{children:"تحديث"})]})]})]}),Fe?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"}),e.jsx("p",{className:"mt-2 text-secondary-600 dark:text-secondary-300",children:"جاري التحميل..."})]}):ee.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(_,{className:"mx-auto h-12 w-12 text-secondary-400 dark:text-secondary-500 mb-3"}),e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300",children:"لا توجد نسخ احتياطية"}),e.jsx("p",{className:"text-sm text-secondary-500 dark:text-secondary-400 mt-1",children:"قم بإنشاء نسخة احتياطية أولاً"})]}):e.jsxs("div",{children:[e.jsx("div",{className:"hidden md:block overflow-x-auto custom-scrollbar",children:e.jsxs("table",{className:"min-w-full divide-y divide-secondary-200 dark:divide-secondary-700",children:[e.jsx("thead",{className:"bg-secondary-50 dark:bg-secondary-800",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-3 text-right text-xs font-medium text-secondary-500 dark:text-secondary-300 uppercase tracking-wider",children:"اسم الملف"}),e.jsx("th",{className:"px-4 py-3 text-right text-xs font-medium text-secondary-500 dark:text-secondary-300 uppercase tracking-wider",children:"الحجم"}),e.jsx("th",{className:"px-4 py-3 text-right text-xs font-medium text-secondary-500 dark:text-secondary-300 uppercase tracking-wider",children:"التاريخ"}),e.jsx("th",{className:"px-4 py-3 text-right text-xs font-medium text-secondary-500 dark:text-secondary-300 uppercase tracking-wider",children:"الإجراءات"})]})}),e.jsx("tbody",{className:"bg-white dark:bg-secondary-800 divide-y divide-secondary-200 dark:divide-secondary-700",children:ee.slice(0,4).map((a,i)=>e.jsxs("tr",{className:i%2===0?"bg-white dark:bg-secondary-800":"bg-secondary-50 dark:bg-secondary-700/50",children:[e.jsx("td",{className:"px-4 py-3 text-sm font-medium text-secondary-900 dark:text-secondary-100",children:e.jsx("div",{className:"truncate max-w-xs",title:a.name,children:a.name})}),e.jsx("td",{className:"px-4 py-3 text-sm text-secondary-600 dark:text-secondary-300",children:a.size}),e.jsx("td",{className:"px-4 py-3 text-sm text-secondary-600 dark:text-secondary-300",children:e.jsxs("div",{children:[e.jsx("div",{children:a.created_date}),e.jsx("div",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:a.created_time})]})}),e.jsx("td",{className:"px-4 py-3 text-sm font-medium",children:e.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[e.jsx("button",{onClick:()=>$e(a.name),className:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200 p-1 rounded",title:"استعادة النسخة الاحتياطية",children:e.jsx(be,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>De(a.name),className:"text-danger-600 hover:text-danger-900 dark:text-danger-400 dark:hover:text-danger-300 transition-colors duration-200 p-1 rounded",title:"حذف النسخة الاحتياطية",children:e.jsx(X,{className:"h-4 w-4"})})]})})]},a.name))})]})}),e.jsx("div",{className:"md:hidden space-y-3",children:ee.slice(0,4).map((a,i)=>e.jsxs("div",{className:"bg-white dark:bg-secondary-800 border border-secondary-200 dark:border-secondary-700 rounded-lg p-4 shadow-sm",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(_,{className:"text-primary-600 dark:text-primary-400 ml-2 flex-shrink-0"}),e.jsxs("h4",{className:"text-sm font-medium text-secondary-900 dark:text-secondary-100 truncate",children:["نسخة احتياطية #",i+1]})]}),e.jsx("p",{className:"text-xs text-secondary-600 dark:text-secondary-300 break-all",children:a.name})]}),e.jsxs("div",{className:"flex items-center space-x-1 space-x-reverse ml-3",children:[e.jsx("button",{onClick:()=>$e(a.name),className:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 transition-colors duration-200 p-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20",title:"استعادة النسخة الاحتياطية",children:e.jsx(be,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>De(a.name),className:"text-danger-600 hover:text-danger-900 dark:text-danger-400 dark:hover:text-danger-300 transition-colors duration-200 p-2 rounded-lg hover:bg-danger-50 dark:hover:bg-danger-900/20",title:"حذف النسخة الاحتياطية",children:e.jsx(X,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-secondary-500 dark:text-secondary-400 text-xs",children:"الحجم"}),e.jsx("p",{className:"font-medium text-secondary-900 dark:text-secondary-100",children:a.size})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-secondary-500 dark:text-secondary-400 text-xs",children:"التاريخ"}),e.jsx("p",{className:"font-medium text-secondary-900 dark:text-secondary-100",children:a.created_date}),e.jsx("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:a.created_time})]})]}),i===0&&e.jsx("div",{className:"mt-3 pt-3 border-t border-secondary-200 dark:border-secondary-700",children:e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300",children:"أحدث نسخة احتياطية"})})]},a.name))}),ee.length>4&&e.jsx("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 mt-4 rounded-lg border border-gray-200 dark:border-gray-600 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:["يتم عرض 4 نسخ من أصل ",ee.length," نسخة احتياطية."," ",e.jsx("button",{onClick:()=>Ce(!0),className:"text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 font-medium",children:"عرض جميع النسخ"})]})})]})]})]})]}),lt=Q().toLocaleDateString("ar-LY",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),dt=()=>{y==="sales"?$(l):y==="products"?re():y==="inventory"?we():y==="system"&&ne()},it=()=>{B({isOpen:!0,type:"backup",title:"تأكيد إنشاء نسخة احتياطية",message:"هل تريد إنشاء نسخة احتياطية من قاعدة البيانات الحالية؟",description:"سيتم إنشاء نسخة كاملة من جميع البيانات وحفظها في مجلد النسخ الاحتياطية.",isLoading:!1})},ot=async()=>{B(a=>({...a,isLoading:!0}));try{const a=await fetch("/api/dashboard/create-backup",{method:"POST",headers:{"Content-Type":"application/json"}}),i=await a.json();if(a.ok&&i.success)B(j=>({...j,isOpen:!1,isLoading:!1})),M({isOpen:!0,title:"تم الإنشاء بنجاح",message:"تم إنشاء النسخة الاحتياطية بنجاح",details:{backup_name:i.backup_name,size:i.size,created_at:i.created_at},autoClose:!0}),ne(),fe();else throw new Error(i.message||"خطأ غير معروف")}catch(a){console.error("Error creating backup:",a),B(i=>({...i,isLoading:!1})),M({isOpen:!0,title:"فشل في الإنشاء",message:`فشل في إنشاء النسخة الاحتياطية: ${a instanceof Error?a.message:"خطأ غير معروف"}`,details:null,autoClose:!1})}},ct=()=>{B({isOpen:!0,type:"update",title:"تأكيد تحديث النظام",message:"هل تريد تحديث النظام إلى أحدث إصدار؟",description:"سيتم فحص التحديثات المتاحة وتطبيقها على النظام.",isLoading:!1})},xt=async()=>{B(a=>({...a,isLoading:!0}));try{await new Promise(a=>setTimeout(a,2e3)),B(a=>({...a,isOpen:!1,isLoading:!1})),M({isOpen:!0,title:"تم التحديث بنجاح",message:"تم تحديث النظام بنجاح",details:{backup_name:"تحديث النظام",size:"مكتمل",created_at:new Date().toLocaleString("ar-LY")},autoClose:!0})}catch(a){console.error("Error updating system:",a),B(i=>({...i,isLoading:!1})),M({isOpen:!0,title:"فشل في التحديث",message:"فشل في تحديث النظام",details:null,autoClose:!1})}},mt=()=>{B({isOpen:!0,type:"clear-cache",title:"تأكيد مسح التخزين المؤقت",message:"هل تريد مسح جميع بيانات التخزين المؤقت؟",description:"سيتم مسح جميع البيانات المحفوظة محلياً وإعادة تحميل الصفحة.",isLoading:!1})},gt=async()=>{B(a=>({...a,isLoading:!0}));try{await new Promise(a=>setTimeout(a,1e3)),localStorage.clear(),sessionStorage.clear(),B(a=>({...a,isOpen:!1,isLoading:!1})),M({isOpen:!0,title:"تم المسح بنجاح",message:"تم مسح التخزين المؤقت بنجاح",details:{backup_name:"مسح التخزين المؤقت",size:"مكتمل",created_at:new Date().toLocaleString("ar-LY")},autoClose:!0}),setTimeout(()=>{window.location.reload()},2e3)}catch(a){console.error("Error clearing cache:",a),B(i=>({...i,isLoading:!1})),M({isOpen:!0,title:"فشل في المسح",message:"فشل في مسح التخزين المؤقت",details:null,autoClose:!1})}},ht=()=>{switch(V.type){case"backup":ot();break;case"update":xt();break;case"clear-cache":gt();break}},yt=()=>{var L;if(!R||R.role!=="admin")return e.jsxs("div",{className:"text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg",children:[e.jsx("div",{className:"bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300 p-4 rounded-full mb-3 inline-block",children:e.jsx(Me,{className:"text-3xl"})}),e.jsx("h3",{className:"text-xl font-bold mb-2 text-gray-900 dark:text-gray-100",children:"غير مصرح"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"هذا التقرير متاح للمديرين فقط"})]});if(te)return e.jsx("div",{className:"flex justify-center items-center h-64 bg-gray-50 dark:bg-gray-700/30 rounded-lg",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 dark:border-primary-400"})});if(!E)return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-gray-50 dark:bg-gray-700/30 p-4 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Te,{className:"text-primary-600 dark:text-primary-400"}),e.jsx("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"تاريخ التقرير:"}),e.jsx("div",{className:"w-48",children:e.jsx(Ke,{name:"reportDate",value:O,onChange:d=>{Ve(d),se(d)},placeholder:"اختر التاريخ",className:"text-sm"})})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["آخر تحديث: ",Ge.toLocaleString("ar-LY")]}),e.jsxs("button",{onClick:()=>se(O,!0),disabled:te,className:"flex items-center gap-2 px-3 py-1.5 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-md disabled:cursor-not-allowed",title:"تحديث البيانات",children:[e.jsx(W,{className:`text-xs ${te?"animate-spin":""}`}),"تحديث"]})]})]}),e.jsxs("div",{className:"text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg",children:[e.jsx("div",{className:"bg-orange-100 dark:bg-orange-900/50 text-orange-700 dark:text-orange-300 p-4 rounded-full mb-3 inline-block",children:e.jsx(ke,{className:"text-3xl"})}),e.jsx("h3",{className:"text-xl font-bold mb-2 text-gray-900 dark:text-gray-100",children:"لا توجد بيانات مبيعات"}),e.jsxs("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:["لا توجد بيانات مبيعات للتاريخ المحدد: ",O]}),e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400 space-y-1",children:[e.jsx("p",{children:"• تأكد من أن التاريخ المحدد صحيح"}),e.jsx("p",{children:"• تأكد من وجود مبيعات في هذا التاريخ"}),e.jsx("p",{children:"• جرب تاريخ آخر أو اليوم الحالي"})]})]})]});const a=E.top_users.map((d,m)=>({name:d.full_name||d.username,value:parseFloat(d.total_received)||0,sales_count:parseInt(d.total_sales)||0,products_total:parseFloat(d.products_total)||0,discounts:parseFloat(d.total_discounts)||0,taxes:parseFloat(d.total_taxes)||0,debts:parseFloat(d.total_debts)||0,color:m})),i=t?["#60A5FA","#34D399","#FBBF24","#F87171","#A78BFA","#F472B6","#38BDF8","#4ADE80"]:["#2563EB","#059669","#D97706","#DC2626","#7C3AED","#DB2777","#0284C7","#16A34A"],j={chart:{type:"bar",fontFamily:"Cairo, sans-serif",toolbar:{show:!0,tools:{download:!0,selection:!1,zoom:!1,zoomin:!1,zoomout:!1,pan:!1,reset:!1}},animations:{enabled:!0,speed:800,animateGradually:{enabled:!0,delay:150}},background:"transparent"},theme:{mode:t?"dark":"light",palette:"palette1"},colors:i,plotOptions:{bar:{horizontal:!0,borderRadius:8,columnWidth:"75%",dataLabels:{position:"top"},distributed:!0}},dataLabels:{enabled:!1},xaxis:{categories:a.map(d=>d.name),labels:{style:{colors:t?"#9CA3AF":"#6B7280",fontSize:"12px",fontFamily:"Cairo, sans-serif"},formatter:function(d){return d.length>15?d.substring(0,15)+"...":d}},axisBorder:{show:!1},axisTicks:{show:!1}},yaxis:{labels:{style:{colors:t?"#9CA3AF":"#6B7280",fontSize:"12px",fontFamily:"Cairo, sans-serif"},formatter:function(d){const m=parseFloat(d);return isNaN(m)?"":`${m.toFixed(0)} د.ل`}}},grid:{borderColor:t?"#374151":"#E5E7EB",strokeDashArray:3,yaxis:{lines:{show:!0}},xaxis:{lines:{show:!1}}},tooltip:{theme:t?"dark":"light",style:{fontSize:"13px",fontFamily:"Cairo, sans-serif"},custom:function({dataPointIndex:d}){try{if(d<0||d>=a.length)return"";const m=a[d],u=i[d%i.length];return`
              <div style="
                background: ${t?"rgba(31, 41, 55, 0.95)":"rgba(255, 255, 255, 0.96)"};
                border: 1px solid ${t?"rgba(55, 65, 81, 0.3)":"rgba(229, 231, 235, 0.4)"};
                border-radius: 2px;
                padding: 16px 20px;
                box-shadow: 0 4px 15px ${t?"rgba(0, 0, 0, 0.4)":"rgba(0, 0, 0, 0.05)"},
                           0 2px 6px ${t?"rgba(0, 0, 0, 0.25)":"rgba(0, 0, 0, 0.1)"};
                backdrop-filter: blur(10px);
                font-family: 'Cairo', sans-serif;
                direction: rtl;
                min-width: 220px;
              ">
                <div style="
                  color: ${t?"#F3F4F6":"#1F2937"};
                  font-size: 15px;
                  font-weight: 700;
                  margin-bottom: 12px;
                  display: flex;
                  align-items: center;
                ">
                  <div style="
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background: ${u};
                    margin-left: 8px;
                  "></div>
                  ${m.name}
                </div>
                <div style="
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  gap: 8px;
                  margin-bottom: 8px;
                ">
                  <div style="
                    color: ${t?"#E2E8F0":"#475569"};
                    font-size: 11px;
                  ">
                    عدد المبيعات: <span style="font-weight: 600;">${m.sales_count}</span>
                  </div>
                  <div style="
                    color: ${t?"#E2E8F0":"#475569"};
                    font-size: 11px;
                  ">
                    سعر المنتجات: <span style="font-weight: 600;">${m.products_total.toFixed(0)} د.ل</span>
                  </div>
                  <div style="
                    color: ${t?"#FBBF24":"#D97706"};
                    font-size: 11px;
                  ">
                    الخصومات: <span style="font-weight: 600;">-${m.discounts.toFixed(0)} د.ل</span>
                  </div>
                  <div style="
                    color: ${t?"#60A5FA":"#2563EB"};
                    font-size: 11px;
                  ">
                    الضرائب: <span style="font-weight: 600;">+${m.taxes.toFixed(0)} د.ل</span>
                  </div>
                </div>
                <div style="
                  border-top: 1px solid ${t?"rgba(55, 65, 81, 0.5)":"rgba(229, 231, 235, 0.5)"};
                  padding-top: 8px;
                  margin-top: 8px;
                ">
                  <div style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    margin-bottom: 8px;
                  ">
                    <div style="
                      color: ${t?"#10B981":"#059669"};
                      font-size: 12px;
                      font-weight: 700;
                      text-align: center;
                    ">
                      المبلغ المستلم: ${m.value.toFixed(0)} د.ل
                    </div>
                    <div style="
                      color: ${t?"#F87171":"#DC2626"};
                      font-size: 12px;
                      font-weight: 700;
                      text-align: center;
                    ">
                      الديون: ${m.debts.toFixed(0)} د.ل
                    </div>
                  </div>
                  <div style="
                    color: ${u};
                    font-size: 14px;
                    font-weight: 800;
                    text-align: center;
                    border-top: 1px solid ${t?"rgba(55, 65, 81, 0.3)":"rgba(229, 231, 235, 0.3)"};
                    padding-top: 6px;
                  ">
                    الإجمالي: ${(m.value+m.debts).toFixed(0)} د.ل
                  </div>
                </div>
              </div>
            `}catch{return""}}},legend:{show:!1},responsive:[{breakpoint:768,options:{chart:{height:300},legend:{show:!1}}}]},F=[{name:"المبلغ المستلم",data:a.map(d=>parseFloat(d.value)||0)}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-gray-50 dark:bg-gray-700/30 p-4 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Te,{className:"text-primary-600 dark:text-primary-400"}),e.jsx("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:"تاريخ التقرير:"}),e.jsx("div",{className:"w-48",children:e.jsx(Ke,{name:"reportDate",value:O,onChange:d=>{Ve(d),se(d)},placeholder:"اختر التاريخ",className:"text-sm"})})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["آخر تحديث: ",Ge.toLocaleString("ar-LY")]}),e.jsxs("button",{onClick:()=>se(O,!0),disabled:te,className:"flex items-center gap-2 px-3 py-1.5 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-md disabled:cursor-not-allowed",title:"تحديث البيانات",children:[e.jsx(W,{className:`text-xs ${te?"animate-spin":""}`}),"تحديث"]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"عدد المبيعات"}),e.jsx("p",{className:"text-lg font-bold text-gray-900 dark:text-gray-100",children:E.summary.total_sales_count})]}),e.jsx(ge,{className:"text-blue-500 text-xl"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"سعر المنتجات"}),e.jsxs("p",{className:"text-lg font-bold text-gray-600 dark:text-gray-400",children:[E.summary.total_products_amount.toFixed(2)," د.ل"]})]}),e.jsx(ge,{className:"text-gray-500 text-xl"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"إجمالي الخصومات"}),e.jsxs("p",{className:"text-lg font-bold text-orange-600 dark:text-orange-400",children:["-",E.summary.total_discounts_amount.toFixed(2)," د.ل"]})]}),e.jsx(_e,{className:"text-orange-500 text-xl"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"إجمالي الضرائب"}),e.jsxs("p",{className:"text-lg font-bold text-blue-600 dark:text-blue-400",children:["+",E.summary.total_taxes_amount.toFixed(2)," د.ل"]})]}),e.jsx(G,{className:"text-blue-500 text-xl"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"المبلغ المستلم"}),e.jsxs("p",{className:"text-lg font-bold text-success-600 dark:text-success-400",children:[E.summary.total_received_amount.toFixed(2)," د.ل"]})]}),e.jsx(G,{className:"text-success-500 text-xl"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"الديون اليوم"}),e.jsxs("p",{className:"text-lg font-bold text-danger-600 dark:text-danger-400",children:[((L=E.summary.total_debts_amount)==null?void 0:L.toFixed(2))||"0.00"," د.ل"]})]}),e.jsx(ye,{className:"text-danger-500 text-xl"})]})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"المستخدمين النشطين"}),e.jsx("p",{className:"text-lg font-bold text-primary-600 dark:text-primary-400",children:E.summary.active_users_count})]}),e.jsx(he,{className:"text-primary-500 text-xl"})]})})]}),a.length>0&&e.jsxs("div",{className:"touch-card",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center",children:[e.jsx(K,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"أفضل المستخدمين مبيعاً"]}),e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["أعلى ",a.length," مستخدمين"]})]}),e.jsx("div",{className:"h-80 md:h-96",children:e.jsx(Ne,{type:"bar",height:"100%",width:"100%",options:j,series:F})}),e.jsx("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:a.map((d,m)=>e.jsxs("div",{className:"flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700/50 rounded-lg",children:[e.jsx("div",{className:"w-3 h-3 rounded-sm",style:{backgroundColor:i[m%i.length]}}),e.jsxs("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[d.name,": ",e.jsxs("span",{className:"font-bold text-gray-900 dark:text-gray-100",children:[d.value.toFixed(0)," د.ل"]})]})]},m))}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4",children:[a[0]&&e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"أفضل مستخدم"}),e.jsx("p",{className:"text-lg font-bold text-secondary-900 dark:text-secondary-100",children:a[0].name}),e.jsxs("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:[a[0].sales_count," مبيعة"]})]}),e.jsx("div",{className:"w-12 h-12 rounded-full flex items-center justify-center",style:{backgroundColor:i[0]+"20"},children:e.jsx(he,{className:"text-xl",style:{color:i[0]}})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"إجمالي المبيعات"}),e.jsx("p",{className:"text-lg font-bold text-secondary-900 dark:text-secondary-100",children:a.reduce((d,m)=>d+m.sales_count,0)}),e.jsxs("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:["من ",a.length," مستخدمين"]})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-success-100 dark:bg-success-900/30 flex items-center justify-center",children:e.jsx(ge,{className:"text-success-600 dark:text-success-400 text-xl"})})]})}),e.jsx("div",{className:"touch-card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-secondary-600 dark:text-secondary-300 text-sm font-medium mb-1",children:"إجمالي المبلغ"}),e.jsxs("p",{className:"text-lg font-bold text-secondary-900 dark:text-secondary-100",children:[a.reduce((d,m)=>d+m.value,0).toFixed(2)," د.ل"]}),e.jsx("p",{className:"text-xs text-secondary-500 dark:text-secondary-400",children:"المبلغ المستلم"})]}),e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center",children:e.jsx(G,{className:"text-primary-600 dark:text-primary-400 text-xl"})})]})})]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:e.jsxs("h3",{className:"text-lg font-bold text-gray-900 dark:text-gray-100 flex items-center",children:[e.jsx(ke,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"تفاصيل مبيعات المستخدمين"]})}),e.jsx("div",{className:"overflow-x-auto custom-scrollbar",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-700",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المستخدم"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"عدد المبيعات"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"سعر المنتجات"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الخصومات"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الضرائب"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المبلغ المستلم"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الديون"})]})}),e.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:E.users.map(d=>{var m;return e.jsxs("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700/50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium",children:d.full_name?d.full_name.charAt(0):d.username.charAt(0)}),e.jsxs("div",{className:"mr-3",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:d.full_name||d.username}),e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["@",d.username]})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:d.total_sales}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400",children:[d.products_total.toFixed(2)," د.ل"]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-orange-600 dark:text-orange-400",children:["-",d.total_discounts.toFixed(2)," د.ل"]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400",children:["+",d.total_taxes.toFixed(2)," د.ل"]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-bold text-success-600 dark:text-success-400",children:[d.total_received.toFixed(2)," د.ل"]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-bold text-danger-600 dark:text-danger-400",children:[((m=d.total_debts)==null?void 0:m.toFixed(2))||"0.00"," د.ل"]})]},d.id)})})]})})]})]})};return e.jsxs("div",{className:"touch-container",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx("button",{onClick:()=>s("/"),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2.5 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm flex-shrink-0",title:"العودة للرئيسية",children:e.jsx(Ct,{className:"text-sm"})}),e.jsxs("div",{className:"mr-3 sm:mr-4 min-w-0 flex-1",children:[e.jsxs("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center",children:[e.jsx(Ee,{className:"ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:"التقارير"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block",children:"تقارير وإحصائيات شاملة للنظام"})]})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap",children:[e.jsx("button",{onClick:dt,className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"تحديث البيانات",children:e.jsx(W,{className:"text-sm"})}),e.jsx("button",{className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"طباعة التقرير",children:e.jsx(Dt,{className:"text-sm"})}),e.jsx("button",{className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"تصدير التقرير",children:e.jsx($t,{className:"text-sm"})})]})]})})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6",children:e.jsxs("div",{className:"text-secondary-600 dark:text-secondary-300 text-sm text-center",children:[e.jsx("span",{className:"font-medium",children:lt}),e.jsx("span",{className:"mx-2",children:"|"}),e.jsx("span",{children:(R==null?void 0:R.role)==="admin"?"جميع المستخدمين":"مبيعاتك فقط"})]})}),e.jsxs("div",{className:"touch-card bg-white dark:bg-gray-800 shadow-soft rounded-xl p-4 mb-6",children:[e.jsxs("div",{className:"flex border-b border-gray-200 dark:border-gray-700 mb-4 overflow-x-auto pb-1 custom-scrollbar-thin",children:[e.jsxs("button",{onClick:()=>xe("sales"),className:`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${y==="sales"?"border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[e.jsx(Ee,{className:"ml-2"}),e.jsx("span",{children:"المبيعات"})]}),e.jsxs("button",{onClick:()=>xe("products"),className:`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${y==="products"?"border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[e.jsx(He,{className:"ml-2"}),e.jsx("span",{children:"المنتجات"})]}),e.jsxs("button",{onClick:()=>xe("inventory"),className:`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${y==="inventory"?"border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[e.jsx(K,{className:"ml-2"}),e.jsx("span",{children:"المخزون"})]}),e.jsxs("button",{onClick:()=>xe("daily-users"),className:`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${y==="daily-users"?"border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[e.jsx(ke,{className:"ml-2"}),e.jsx("span",{children:"مبيعات المستخدمين"})]}),e.jsxs("button",{onClick:()=>xe("system"),className:`py-3 px-6 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${y==="system"?"border-primary-600 dark:border-primary-400 text-primary-600 dark:text-primary-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[e.jsx(Pe,{className:"ml-2"}),e.jsx("span",{children:"النظام"})]})]}),y==="sales"&&e.jsxs("div",{className:"flex space-x-2 space-x-reverse mb-4 overflow-x-auto pb-1 custom-scrollbar-thin",children:[e.jsxs("button",{onClick:()=>je("day"),className:`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${l==="day"?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(Te,{className:"ml-1.5"}),e.jsx("span",{children:"اليوم"})]}),e.jsxs("button",{onClick:()=>je("week"),className:`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${l==="week"?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(At,{className:"ml-1.5"}),e.jsx("span",{children:"الأسبوع"})]}),e.jsxs("button",{onClick:()=>je("month"),className:`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${l==="month"?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(ue,{className:"ml-1.5"}),e.jsx("span",{children:"الشهر"})]}),e.jsxs("button",{onClick:()=>je("year"),className:`flex items-center px-3 py-1.5 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${l==="year"?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(Bt,{className:"ml-1.5"}),e.jsx("span",{children:"السنة"})]})]}),h?e.jsx("div",{className:"flex justify-center items-center h-64 bg-gray-50 dark:bg-gray-700/30 rounded-lg",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 dark:border-primary-400"})}):e.jsxs(e.Fragment,{children:[y==="sales"&&st(),y==="products"&&at(),y==="inventory"&&rt(),y==="daily-users"&&yt(),y==="system"&&nt(),y==="customers"&&e.jsxs("div",{className:"text-center py-12 bg-gray-50 dark:bg-gray-700/30 rounded-lg",children:[e.jsx("div",{className:"bg-secondary-100 dark:bg-secondary-900/50 text-secondary-700 dark:text-secondary-300 p-4 rounded-full mb-3 inline-block",children:e.jsx(K,{className:"text-3xl"})}),e.jsx("h3",{className:"text-xl font-bold mb-2 text-secondary-900 dark:text-secondary-100",children:"تقرير العملاء قيد التطوير"}),e.jsx("p",{className:"text-secondary-600 dark:text-secondary-400",children:"سيتم إضافة هذه الميزة قريباً"})]})]})]}),e.jsx(zt,{isOpen:le.isOpen,onClose:()=>de({isOpen:!1,backupName:"",isLoading:!1}),onConfirm:Xe,title:"تأكيد حذف النسخة الاحتياطية",message:"هل أنت متأكد من حذف النسخة الاحتياطية التالية؟",itemName:le.backupName,isLoading:le.isLoading}),e.jsx(Mt,{isOpen:ie.isOpen,onClose:()=>oe({isOpen:!1,backupInfo:null,isLoading:!1}),onConfirm:et,backupInfo:ie.backupInfo,isLoading:ie.isLoading}),e.jsx(Et,{isOpen:ce.isOpen,onClose:()=>M({isOpen:!1,title:"",message:"",details:null,autoClose:!1}),title:ce.title,message:ce.message,details:ce.details,autoClose:ce.autoClose,autoCloseDelay:3e3}),e.jsx(Ot,{isOpen:V.isOpen,onClose:()=>B({isOpen:!1,type:"backup",title:"",message:"",description:"",isLoading:!1}),onConfirm:ht,title:V.title,message:V.message,description:V.description,type:V.type,isLoading:V.isLoading}),e.jsx(Rt,{isOpen:Je,onClose:()=>Ce(!1),onRestore:$e,onDelete:De})]})};export{Zt as default};
