import{r as P,j as i,f as He,a as Ke,b as O}from"./index-DmuezwQ-.js";import{w as Ee,an as qe,am as Je,ao as be,I as Ye,D as ve,O as we,e as ne,ap as Qe}from"./index-Cn5nbJXI.js";import{M as Ge}from"./Modal-CFBGiLJ5.js";var K={},We=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},_e={},I={};let me;const Ze=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];I.getSymbolSize=function(e){if(!e)throw new Error('"version" cannot be null or undefined');if(e<1||e>40)throw new Error('"version" should be in range from 1 to 40');return e*4+17};I.getSymbolTotalCodewords=function(e){return Ze[e]};I.getBCHDigit=function(n){let e=0;for(;n!==0;)e++,n>>>=1;return e};I.setToSJISFunction=function(e){if(typeof e!="function")throw new Error('"toSJISFunc" is not a valid function.');me=e};I.isKanjiModeEnabled=function(){return typeof me<"u"};I.toSJIS=function(e){return me(e)};var Z={};(function(n){n.L={bit:1},n.M={bit:0},n.Q={bit:3},n.H={bit:2};function e(t){if(typeof t!="string")throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return n.L;case"m":case"medium":return n.M;case"q":case"quartile":return n.Q;case"h":case"high":return n.H;default:throw new Error("Unknown EC Level: "+t)}}n.isValid=function(o){return o&&typeof o.bit<"u"&&o.bit>=0&&o.bit<4},n.from=function(o,r){if(n.isValid(o))return o;try{return e(o)}catch{return r}}})(Z);function Ae(){this.buffer=[],this.length=0}Ae.prototype={get:function(n){const e=Math.floor(n/8);return(this.buffer[e]>>>7-n%8&1)===1},put:function(n,e){for(let t=0;t<e;t++)this.putBit((n>>>e-t-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(n){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),n&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var Xe=Ae;function q(n){if(!n||n<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=n,this.data=new Uint8Array(n*n),this.reservedBit=new Uint8Array(n*n)}q.prototype.set=function(n,e,t,o){const r=n*this.size+e;this.data[r]=t,o&&(this.reservedBit[r]=!0)};q.prototype.get=function(n,e){return this.data[n*this.size+e]};q.prototype.xor=function(n,e,t){this.data[n*this.size+e]^=t};q.prototype.isReserved=function(n,e){return this.reservedBit[n*this.size+e]};var et=q,Se={};(function(n){const e=I.getSymbolSize;n.getRowColCoords=function(o){if(o===1)return[];const r=Math.floor(o/7)+2,s=e(o),a=s===145?26:Math.ceil((s-13)/(2*r-2))*2,l=[s-7];for(let c=1;c<r-1;c++)l[c]=l[c-1]-a;return l.push(6),l.reverse()},n.getPositions=function(o){const r=[],s=n.getRowColCoords(o),a=s.length;for(let l=0;l<a;l++)for(let c=0;c<a;c++)l===0&&c===0||l===0&&c===a-1||l===a-1&&c===0||r.push([s[l],s[c]]);return r}})(Se);var Be={};const tt=I.getSymbolSize,Ne=7;Be.getPositions=function(e){const t=tt(e);return[[0,0],[t-Ne,0],[0,t-Ne]]};var ke={};(function(n){n.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e={N1:3,N2:3,N3:40,N4:10};n.isValid=function(r){return r!=null&&r!==""&&!isNaN(r)&&r>=0&&r<=7},n.from=function(r){return n.isValid(r)?parseInt(r,10):void 0},n.getPenaltyN1=function(r){const s=r.size;let a=0,l=0,c=0,m=null,u=null;for(let E=0;E<s;E++){l=c=0,m=u=null;for(let d=0;d<s;d++){let h=r.get(E,d);h===m?l++:(l>=5&&(a+=e.N1+(l-5)),m=h,l=1),h=r.get(d,E),h===u?c++:(c>=5&&(a+=e.N1+(c-5)),u=h,c=1)}l>=5&&(a+=e.N1+(l-5)),c>=5&&(a+=e.N1+(c-5))}return a},n.getPenaltyN2=function(r){const s=r.size;let a=0;for(let l=0;l<s-1;l++)for(let c=0;c<s-1;c++){const m=r.get(l,c)+r.get(l,c+1)+r.get(l+1,c)+r.get(l+1,c+1);(m===4||m===0)&&a++}return a*e.N2},n.getPenaltyN3=function(r){const s=r.size;let a=0,l=0,c=0;for(let m=0;m<s;m++){l=c=0;for(let u=0;u<s;u++)l=l<<1&2047|r.get(m,u),u>=10&&(l===1488||l===93)&&a++,c=c<<1&2047|r.get(u,m),u>=10&&(c===1488||c===93)&&a++}return a*e.N3},n.getPenaltyN4=function(r){let s=0;const a=r.data.length;for(let c=0;c<a;c++)s+=r.data[c];return Math.abs(Math.ceil(s*100/a/5)-10)*e.N4};function t(o,r,s){switch(o){case n.Patterns.PATTERN000:return(r+s)%2===0;case n.Patterns.PATTERN001:return r%2===0;case n.Patterns.PATTERN010:return s%3===0;case n.Patterns.PATTERN011:return(r+s)%3===0;case n.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(s/3))%2===0;case n.Patterns.PATTERN101:return r*s%2+r*s%3===0;case n.Patterns.PATTERN110:return(r*s%2+r*s%3)%2===0;case n.Patterns.PATTERN111:return(r*s%3+(r+s)%2)%2===0;default:throw new Error("bad maskPattern:"+o)}}n.applyMask=function(r,s){const a=s.size;for(let l=0;l<a;l++)for(let c=0;c<a;c++)s.isReserved(c,l)||s.xor(c,l,t(r,c,l))},n.getBestMask=function(r,s){const a=Object.keys(n.Patterns).length;let l=0,c=1/0;for(let m=0;m<a;m++){s(m),n.applyMask(m,r);const u=n.getPenaltyN1(r)+n.getPenaltyN2(r)+n.getPenaltyN3(r)+n.getPenaltyN4(r);n.applyMask(m,r),u<c&&(c=u,l=m)}return l}})(ke);var X={};const F=Z,Y=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],Q=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];X.getBlocksCount=function(e,t){switch(t){case F.L:return Y[(e-1)*4+0];case F.M:return Y[(e-1)*4+1];case F.Q:return Y[(e-1)*4+2];case F.H:return Y[(e-1)*4+3];default:return}};X.getTotalCodewordsCount=function(e,t){switch(t){case F.L:return Q[(e-1)*4+0];case F.M:return Q[(e-1)*4+1];case F.Q:return Q[(e-1)*4+2];case F.H:return Q[(e-1)*4+3];default:return}};var Te={},ee={};const V=new Uint8Array(512),G=new Uint8Array(256);(function(){let e=1;for(let t=0;t<255;t++)V[t]=e,G[e]=t,e<<=1,e&256&&(e^=285);for(let t=255;t<512;t++)V[t]=V[t-255]})();ee.log=function(e){if(e<1)throw new Error("log("+e+")");return G[e]};ee.exp=function(e){return V[e]};ee.mul=function(e,t){return e===0||t===0?0:V[G[e]+G[t]]};(function(n){const e=ee;n.mul=function(o,r){const s=new Uint8Array(o.length+r.length-1);for(let a=0;a<o.length;a++)for(let l=0;l<r.length;l++)s[a+l]^=e.mul(o[a],r[l]);return s},n.mod=function(o,r){let s=new Uint8Array(o);for(;s.length-r.length>=0;){const a=s[0];for(let c=0;c<r.length;c++)s[c]^=e.mul(r[c],a);let l=0;for(;l<s.length&&s[l]===0;)l++;s=s.slice(l)}return s},n.generateECPolynomial=function(o){let r=new Uint8Array([1]);for(let s=0;s<o;s++)r=n.mul(r,new Uint8Array([1,e.exp(s)]));return r}})(Te);const Ie=Te;function ge(n){this.genPoly=void 0,this.degree=n,this.degree&&this.initialize(this.degree)}ge.prototype.initialize=function(e){this.degree=e,this.genPoly=Ie.generateECPolynomial(this.degree)};ge.prototype.encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");const t=new Uint8Array(e.length+this.degree);t.set(e);const o=Ie.mod(t,this.genPoly),r=this.degree-o.length;if(r>0){const s=new Uint8Array(this.degree);return s.set(o,r),s}return o};var rt=ge,Me={},L={},fe={};fe.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40};var R={};const Pe="[0-9]+",nt="[A-Z $%*+\\-./:]+";let H="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";H=H.replace(/u/g,"\\u");const st="(?:(?![A-Z0-9 $%*+\\-./:]|"+H+`)(?:.|[\r
]))+`;R.KANJI=new RegExp(H,"g");R.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");R.BYTE=new RegExp(st,"g");R.NUMERIC=new RegExp(Pe,"g");R.ALPHANUMERIC=new RegExp(nt,"g");const ot=new RegExp("^"+H+"$"),it=new RegExp("^"+Pe+"$"),at=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");R.testKanji=function(e){return ot.test(e)};R.testNumeric=function(e){return it.test(e)};R.testAlphanumeric=function(e){return at.test(e)};(function(n){const e=fe,t=R;n.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},n.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},n.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},n.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},n.MIXED={bit:-1},n.getCharCountIndicator=function(s,a){if(!s.ccBits)throw new Error("Invalid mode: "+s);if(!e.isValid(a))throw new Error("Invalid version: "+a);return a>=1&&a<10?s.ccBits[0]:a<27?s.ccBits[1]:s.ccBits[2]},n.getBestModeForData=function(s){return t.testNumeric(s)?n.NUMERIC:t.testAlphanumeric(s)?n.ALPHANUMERIC:t.testKanji(s)?n.KANJI:n.BYTE},n.toString=function(s){if(s&&s.id)return s.id;throw new Error("Invalid mode")},n.isValid=function(s){return s&&s.bit&&s.ccBits};function o(r){if(typeof r!="string")throw new Error("Param is not a string");switch(r.toLowerCase()){case"numeric":return n.NUMERIC;case"alphanumeric":return n.ALPHANUMERIC;case"kanji":return n.KANJI;case"byte":return n.BYTE;default:throw new Error("Unknown mode: "+r)}}n.from=function(s,a){if(n.isValid(s))return s;try{return o(s)}catch{return a}}})(L);(function(n){const e=I,t=X,o=Z,r=L,s=fe,a=7973,l=e.getBCHDigit(a);function c(d,h,w){for(let N=1;N<=40;N++)if(h<=n.getCapacity(N,w,d))return N}function m(d,h){return r.getCharCountIndicator(d,h)+4}function u(d,h){let w=0;return d.forEach(function(N){const S=m(N.mode,h);w+=S+N.getBitsLength()}),w}function E(d,h){for(let w=1;w<=40;w++)if(u(d,w)<=n.getCapacity(w,h,r.MIXED))return w}n.from=function(h,w){return s.isValid(h)?parseInt(h,10):w},n.getCapacity=function(h,w,N){if(!s.isValid(h))throw new Error("Invalid QR Code version");typeof N>"u"&&(N=r.BYTE);const S=e.getSymbolTotalCodewords(h),b=t.getTotalCodewordsCount(h,w),j=(S-b)*8;if(N===r.MIXED)return j;const x=j-m(N,h);switch(N){case r.NUMERIC:return Math.floor(x/10*3);case r.ALPHANUMERIC:return Math.floor(x/11*2);case r.KANJI:return Math.floor(x/13);case r.BYTE:default:return Math.floor(x/8)}},n.getBestVersionForData=function(h,w){let N;const S=o.from(w,o.M);if(Array.isArray(h)){if(h.length>1)return E(h,S);if(h.length===0)return 1;N=h[0]}else N=h;return c(N.mode,N.getLength(),S)},n.getEncodedBits=function(h){if(!s.isValid(h)||h<7)throw new Error("Invalid QR Code version");let w=h<<12;for(;e.getBCHDigit(w)-l>=0;)w^=a<<e.getBCHDigit(w)-l;return h<<12|w}})(Me);var Re={};const le=I,Fe=1335,ct=21522,je=le.getBCHDigit(Fe);Re.getEncodedBits=function(e,t){const o=e.bit<<3|t;let r=o<<10;for(;le.getBCHDigit(r)-je>=0;)r^=Fe<<le.getBCHDigit(r)-je;return(o<<10|r)^ct};var Le={};const lt=L;function D(n){this.mode=lt.NUMERIC,this.data=n.toString()}D.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)};D.prototype.getLength=function(){return this.data.length};D.prototype.getBitsLength=function(){return D.getBitsLength(this.data.length)};D.prototype.write=function(e){let t,o,r;for(t=0;t+3<=this.data.length;t+=3)o=this.data.substr(t,3),r=parseInt(o,10),e.put(r,10);const s=this.data.length-t;s>0&&(o=this.data.substr(t),r=parseInt(o,10),e.put(r,s*3+1))};var dt=D;const ut=L,se=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function U(n){this.mode=ut.ALPHANUMERIC,this.data=n}U.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)};U.prototype.getLength=function(){return this.data.length};U.prototype.getBitsLength=function(){return U.getBitsLength(this.data.length)};U.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let o=se.indexOf(this.data[t])*45;o+=se.indexOf(this.data[t+1]),e.put(o,11)}this.data.length%2&&e.put(se.indexOf(this.data[t]),6)};var ht=U;const mt=L;function $(n){this.mode=mt.BYTE,typeof n=="string"?this.data=new TextEncoder().encode(n):this.data=new Uint8Array(n)}$.getBitsLength=function(e){return e*8};$.prototype.getLength=function(){return this.data.length};$.prototype.getBitsLength=function(){return $.getBitsLength(this.data.length)};$.prototype.write=function(n){for(let e=0,t=this.data.length;e<t;e++)n.put(this.data[e],8)};var gt=$;const ft=L,pt=I;function z(n){this.mode=ft.KANJI,this.data=n}z.getBitsLength=function(e){return e*13};z.prototype.getLength=function(){return this.data.length};z.prototype.getBitsLength=function(){return z.getBitsLength(this.data.length)};z.prototype.write=function(n){let e;for(e=0;e<this.data.length;e++){let t=pt.toSJIS(this.data[e]);if(t>=33088&&t<=40956)t-=33088;else if(t>=57408&&t<=60351)t-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);t=(t>>>8&255)*192+(t&255),n.put(t,13)}};var yt=z,De={exports:{}};(function(n){var e={single_source_shortest_paths:function(t,o,r){var s={},a={};a[o]=0;var l=e.PriorityQueue.make();l.push(o,0);for(var c,m,u,E,d,h,w,N,S;!l.empty();){c=l.pop(),m=c.value,E=c.cost,d=t[m]||{};for(u in d)d.hasOwnProperty(u)&&(h=d[u],w=E+h,N=a[u],S=typeof a[u]>"u",(S||N>w)&&(a[u]=w,l.push(u,w),s[u]=m))}if(typeof r<"u"&&typeof a[r]>"u"){var b=["Could not find a path from ",o," to ",r,"."].join("");throw new Error(b)}return s},extract_shortest_path_from_predecessor_list:function(t,o){for(var r=[],s=o;s;)r.push(s),t[s],s=t[s];return r.reverse(),r},find_path:function(t,o,r){var s=e.single_source_shortest_paths(t,o,r);return e.extract_shortest_path_from_predecessor_list(s,r)},PriorityQueue:{make:function(t){var o=e.PriorityQueue,r={},s;t=t||{};for(s in o)o.hasOwnProperty(s)&&(r[s]=o[s]);return r.queue=[],r.sorter=t.sorter||o.default_sorter,r},default_sorter:function(t,o){return t.cost-o.cost},push:function(t,o){var r={value:t,cost:o};this.queue.push(r),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};n.exports=e})(De);var xt=De.exports;(function(n){const e=L,t=dt,o=ht,r=gt,s=yt,a=R,l=I,c=xt;function m(b){return unescape(encodeURIComponent(b)).length}function u(b,j,x){const y=[];let v;for(;(v=b.exec(x))!==null;)y.push({data:v[0],index:v.index,mode:j,length:v[0].length});return y}function E(b){const j=u(a.NUMERIC,e.NUMERIC,b),x=u(a.ALPHANUMERIC,e.ALPHANUMERIC,b);let y,v;return l.isKanjiModeEnabled()?(y=u(a.BYTE,e.BYTE,b),v=u(a.KANJI,e.KANJI,b)):(y=u(a.BYTE_KANJI,e.BYTE,b),v=[]),j.concat(x,y,v).sort(function(p,_){return p.index-_.index}).map(function(p){return{data:p.data,mode:p.mode,length:p.length}})}function d(b,j){switch(j){case e.NUMERIC:return t.getBitsLength(b);case e.ALPHANUMERIC:return o.getBitsLength(b);case e.KANJI:return s.getBitsLength(b);case e.BYTE:return r.getBitsLength(b)}}function h(b){return b.reduce(function(j,x){const y=j.length-1>=0?j[j.length-1]:null;return y&&y.mode===x.mode?(j[j.length-1].data+=x.data,j):(j.push(x),j)},[])}function w(b){const j=[];for(let x=0;x<b.length;x++){const y=b[x];switch(y.mode){case e.NUMERIC:j.push([y,{data:y.data,mode:e.ALPHANUMERIC,length:y.length},{data:y.data,mode:e.BYTE,length:y.length}]);break;case e.ALPHANUMERIC:j.push([y,{data:y.data,mode:e.BYTE,length:y.length}]);break;case e.KANJI:j.push([y,{data:y.data,mode:e.BYTE,length:m(y.data)}]);break;case e.BYTE:j.push([{data:y.data,mode:e.BYTE,length:m(y.data)}])}}return j}function N(b,j){const x={},y={start:{}};let v=["start"];for(let g=0;g<b.length;g++){const p=b[g],_=[];for(let f=0;f<p.length;f++){const C=p[f],A=""+g+f;_.push(A),x[A]={node:C,lastCount:0},y[A]={};for(let k=0;k<v.length;k++){const T=v[k];x[T]&&x[T].node.mode===C.mode?(y[T][A]=d(x[T].lastCount+C.length,C.mode)-d(x[T].lastCount,C.mode),x[T].lastCount+=C.length):(x[T]&&(x[T].lastCount=C.length),y[T][A]=d(C.length,C.mode)+4+e.getCharCountIndicator(C.mode,j))}}v=_}for(let g=0;g<v.length;g++)y[v[g]].end=0;return{map:y,table:x}}function S(b,j){let x;const y=e.getBestModeForData(b);if(x=e.from(j,y),x!==e.BYTE&&x.bit<y.bit)throw new Error('"'+b+'" cannot be encoded with mode '+e.toString(x)+`.
 Suggested mode is: `+e.toString(y));switch(x===e.KANJI&&!l.isKanjiModeEnabled()&&(x=e.BYTE),x){case e.NUMERIC:return new t(b);case e.ALPHANUMERIC:return new o(b);case e.KANJI:return new s(b);case e.BYTE:return new r(b)}}n.fromArray=function(j){return j.reduce(function(x,y){return typeof y=="string"?x.push(S(y,null)):y.data&&x.push(S(y.data,y.mode)),x},[])},n.fromString=function(j,x){const y=E(j,l.isKanjiModeEnabled()),v=w(y),g=N(v,x),p=c.find_path(g.map,"start","end"),_=[];for(let f=1;f<p.length-1;f++)_.push(g.table[p[f]].node);return n.fromArray(h(_))},n.rawSplit=function(j){return n.fromArray(E(j,l.isKanjiModeEnabled()))}})(Le);const te=I,oe=Z,bt=Xe,wt=et,Nt=Se,jt=Be,de=ke,ue=X,Ct=rt,W=Me,Et=Re,vt=L,ie=Le;function _t(n,e){const t=n.size,o=jt.getPositions(e);for(let r=0;r<o.length;r++){const s=o[r][0],a=o[r][1];for(let l=-1;l<=7;l++)if(!(s+l<=-1||t<=s+l))for(let c=-1;c<=7;c++)a+c<=-1||t<=a+c||(l>=0&&l<=6&&(c===0||c===6)||c>=0&&c<=6&&(l===0||l===6)||l>=2&&l<=4&&c>=2&&c<=4?n.set(s+l,a+c,!0,!0):n.set(s+l,a+c,!1,!0))}}function At(n){const e=n.size;for(let t=8;t<e-8;t++){const o=t%2===0;n.set(t,6,o,!0),n.set(6,t,o,!0)}}function St(n,e){const t=Nt.getPositions(e);for(let o=0;o<t.length;o++){const r=t[o][0],s=t[o][1];for(let a=-2;a<=2;a++)for(let l=-2;l<=2;l++)a===-2||a===2||l===-2||l===2||a===0&&l===0?n.set(r+a,s+l,!0,!0):n.set(r+a,s+l,!1,!0)}}function Bt(n,e){const t=n.size,o=W.getEncodedBits(e);let r,s,a;for(let l=0;l<18;l++)r=Math.floor(l/3),s=l%3+t-8-3,a=(o>>l&1)===1,n.set(r,s,a,!0),n.set(s,r,a,!0)}function ae(n,e,t){const o=n.size,r=Et.getEncodedBits(e,t);let s,a;for(s=0;s<15;s++)a=(r>>s&1)===1,s<6?n.set(s,8,a,!0):s<8?n.set(s+1,8,a,!0):n.set(o-15+s,8,a,!0),s<8?n.set(8,o-s-1,a,!0):s<9?n.set(8,15-s-1+1,a,!0):n.set(8,15-s-1,a,!0);n.set(o-8,8,1,!0)}function kt(n,e){const t=n.size;let o=-1,r=t-1,s=7,a=0;for(let l=t-1;l>0;l-=2)for(l===6&&l--;;){for(let c=0;c<2;c++)if(!n.isReserved(r,l-c)){let m=!1;a<e.length&&(m=(e[a]>>>s&1)===1),n.set(r,l-c,m),s--,s===-1&&(a++,s=7)}if(r+=o,r<0||t<=r){r-=o,o=-o;break}}}function Tt(n,e,t){const o=new bt;t.forEach(function(c){o.put(c.mode.bit,4),o.put(c.getLength(),vt.getCharCountIndicator(c.mode,n)),c.write(o)});const r=te.getSymbolTotalCodewords(n),s=ue.getTotalCodewordsCount(n,e),a=(r-s)*8;for(o.getLengthInBits()+4<=a&&o.put(0,4);o.getLengthInBits()%8!==0;)o.putBit(0);const l=(a-o.getLengthInBits())/8;for(let c=0;c<l;c++)o.put(c%2?17:236,8);return It(o,n,e)}function It(n,e,t){const o=te.getSymbolTotalCodewords(e),r=ue.getTotalCodewordsCount(e,t),s=o-r,a=ue.getBlocksCount(e,t),l=o%a,c=a-l,m=Math.floor(o/a),u=Math.floor(s/a),E=u+1,d=m-u,h=new Ct(d);let w=0;const N=new Array(a),S=new Array(a);let b=0;const j=new Uint8Array(n.buffer);for(let p=0;p<a;p++){const _=p<c?u:E;N[p]=j.slice(w,w+_),S[p]=h.encode(N[p]),w+=_,b=Math.max(b,_)}const x=new Uint8Array(o);let y=0,v,g;for(v=0;v<b;v++)for(g=0;g<a;g++)v<N[g].length&&(x[y++]=N[g][v]);for(v=0;v<d;v++)for(g=0;g<a;g++)x[y++]=S[g][v];return x}function Mt(n,e,t,o){let r;if(Array.isArray(n))r=ie.fromArray(n);else if(typeof n=="string"){let m=e;if(!m){const u=ie.rawSplit(n);m=W.getBestVersionForData(u,t)}r=ie.fromString(n,m||40)}else throw new Error("Invalid data");const s=W.getBestVersionForData(r,t);if(!s)throw new Error("The amount of data is too big to be stored in a QR Code");if(!e)e=s;else if(e<s)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+s+`.
`);const a=Tt(e,t,r),l=te.getSymbolSize(e),c=new wt(l);return _t(c,e),At(c),St(c,e),ae(c,t,0),e>=7&&Bt(c,e),kt(c,a),isNaN(o)&&(o=de.getBestMask(c,ae.bind(null,c,t))),de.applyMask(o,c),ae(c,t,o),{modules:c,version:e,errorCorrectionLevel:t,maskPattern:o,segments:r}}_e.create=function(e,t){if(typeof e>"u"||e==="")throw new Error("No input text");let o=oe.M,r,s;return typeof t<"u"&&(o=oe.from(t.errorCorrectionLevel,oe.M),r=W.from(t.version),s=de.from(t.maskPattern),t.toSJISFunc&&te.setToSJISFunction(t.toSJISFunc)),Mt(e,r,o,s)};var Ue={},pe={};(function(n){function e(t){if(typeof t=="number"&&(t=t.toString()),typeof t!="string")throw new Error("Color should be defined as hex string");let o=t.slice().replace("#","").split("");if(o.length<3||o.length===5||o.length>8)throw new Error("Invalid hex color: "+t);(o.length===3||o.length===4)&&(o=Array.prototype.concat.apply([],o.map(function(s){return[s,s]}))),o.length===6&&o.push("F","F");const r=parseInt(o.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:r&255,hex:"#"+o.slice(0,6).join("")}}n.getOptions=function(o){o||(o={}),o.color||(o.color={});const r=typeof o.margin>"u"||o.margin===null||o.margin<0?4:o.margin,s=o.width&&o.width>=21?o.width:void 0,a=o.scale||4;return{width:s,scale:s?4:a,margin:r,color:{dark:e(o.color.dark||"#000000ff"),light:e(o.color.light||"#ffffffff")},type:o.type,rendererOpts:o.rendererOpts||{}}},n.getScale=function(o,r){return r.width&&r.width>=o+r.margin*2?r.width/(o+r.margin*2):r.scale},n.getImageWidth=function(o,r){const s=n.getScale(o,r);return Math.floor((o+r.margin*2)*s)},n.qrToImageData=function(o,r,s){const a=r.modules.size,l=r.modules.data,c=n.getScale(a,s),m=Math.floor((a+s.margin*2)*c),u=s.margin*c,E=[s.color.light,s.color.dark];for(let d=0;d<m;d++)for(let h=0;h<m;h++){let w=(d*m+h)*4,N=s.color.light;if(d>=u&&h>=u&&d<m-u&&h<m-u){const S=Math.floor((d-u)/c),b=Math.floor((h-u)/c);N=E[l[S*a+b]?1:0]}o[w++]=N.r,o[w++]=N.g,o[w++]=N.b,o[w]=N.a}}})(pe);(function(n){const e=pe;function t(r,s,a){r.clearRect(0,0,s.width,s.height),s.style||(s.style={}),s.height=a,s.width=a,s.style.height=a+"px",s.style.width=a+"px"}function o(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}n.render=function(s,a,l){let c=l,m=a;typeof c>"u"&&(!a||!a.getContext)&&(c=a,a=void 0),a||(m=o()),c=e.getOptions(c);const u=e.getImageWidth(s.modules.size,c),E=m.getContext("2d"),d=E.createImageData(u,u);return e.qrToImageData(d.data,s,c),t(E,m,u),E.putImageData(d,0,0),m},n.renderToDataURL=function(s,a,l){let c=l;typeof c>"u"&&(!a||!a.getContext)&&(c=a,a=void 0),c||(c={});const m=n.render(s,a,c),u=c.type||"image/png",E=c.rendererOpts||{};return m.toDataURL(u,E.quality)}})(Ue);var $e={};const Pt=pe;function Ce(n,e){const t=n.a/255,o=e+'="'+n.hex+'"';return t<1?o+" "+e+'-opacity="'+t.toFixed(2).slice(1)+'"':o}function ce(n,e,t){let o=n+e;return typeof t<"u"&&(o+=" "+t),o}function Rt(n,e,t){let o="",r=0,s=!1,a=0;for(let l=0;l<n.length;l++){const c=Math.floor(l%e),m=Math.floor(l/e);!c&&!s&&(s=!0),n[l]?(a++,l>0&&c>0&&n[l-1]||(o+=s?ce("M",c+t,.5+m+t):ce("m",r,0),r=0,s=!1),c+1<e&&n[l+1]||(o+=ce("h",a),a=0)):r++}return o}$e.render=function(e,t,o){const r=Pt.getOptions(t),s=e.modules.size,a=e.modules.data,l=s+r.margin*2,c=r.color.light.a?"<path "+Ce(r.color.light,"fill")+' d="M0 0h'+l+"v"+l+'H0z"/>':"",m="<path "+Ce(r.color.dark,"stroke")+' d="'+Rt(a,s,r.margin)+'"/>',u='viewBox="0 0 '+l+" "+l+'"',d='<svg xmlns="http://www.w3.org/2000/svg" '+(r.width?'width="'+r.width+'" height="'+r.width+'" ':"")+u+' shape-rendering="crispEdges">'+c+m+`</svg>
`;return typeof o=="function"&&o(null,d),d};const Ft=We,he=_e,ze=Ue,Lt=$e;function ye(n,e,t,o,r){const s=[].slice.call(arguments,1),a=s.length,l=typeof s[a-1]=="function";if(!l&&!Ft())throw new Error("Callback required as last argument");if(l){if(a<2)throw new Error("Too few arguments provided");a===2?(r=t,t=e,e=o=void 0):a===3&&(e.getContext&&typeof r>"u"?(r=o,o=void 0):(r=o,o=t,t=e,e=void 0))}else{if(a<1)throw new Error("Too few arguments provided");return a===1?(t=e,e=o=void 0):a===2&&!e.getContext&&(o=t,t=e,e=void 0),new Promise(function(c,m){try{const u=he.create(t,o);c(n(u,e,o))}catch(u){m(u)}})}try{const c=he.create(t,o);r(null,n(c,e,o))}catch(c){r(c)}}K.create=he.create;K.toCanvas=ye.bind(null,ze.render);K.toDataURL=ye.bind(null,ze.renderToDataURL);K.toString=ye.bind(null,function(n,e,t){return Lt.render(n,t)});const Dt=({isOpen:n,onClose:e,onPrint:t,receiptSize:o,onReceiptSizeChange:r})=>{const[s,a]=P.useState({printerType:"thermal",paperSize:o==="small"?"58mm":o==="medium"?"80mm":"A4",orientation:"portrait",quality:"normal",copies:1,margins:"minimum",colorMode:"blackwhite"}),l=()=>{t(s),e()},c=d=>{r(d),a(h=>({...h,paperSize:d==="small"?"58mm":d==="medium"?"80mm":"A4",printerType:d==="a4"?"laser":"thermal"}))},m=[{value:"thermal",label:"طابعة حرارية",icon:qe},{value:"laser",label:"طابعة ليزر",icon:Je},{value:"inkjet",label:"طابعة حبر",icon:be}],u=[{value:"58mm",label:"58mm (صغير)",description:"للطابعات الحرارية الصغيرة"},{value:"80mm",label:"80mm (متوسط)",description:"للطابعات الحرارية المتوسطة"},{value:"A4",label:"A4 (كبير)",description:"للطابعات العادية"}],E=[{value:"draft",label:"مسودة",description:"سريع وموفر للحبر"},{value:"normal",label:"عادي",description:"جودة متوازنة"},{value:"high",label:"عالي",description:"أفضل جودة"}];return i.jsx(Ge,{isOpen:n,onClose:e,title:"خصائص الطباعة",children:i.jsxs("div",{className:"space-y-6",children:[i.jsxs("div",{children:[i.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:[i.jsx(Ee,{className:"inline mr-2"}),"نوع الطابعة"]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:m.map(d=>{const h=d.icon;return i.jsxs("button",{onClick:()=>a(w=>({...w,printerType:d.value})),className:`p-4 border-2 rounded-lg text-center transition-all ${s.printerType===d.value?"border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300":"border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600"}`,children:[i.jsx(h,{className:"mx-auto mb-2 text-xl"}),i.jsx("div",{className:"text-sm font-medium",children:d.label})]},d.value)})})]}),i.jsxs("div",{children:[i.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:[i.jsx(be,{className:"inline mr-2"}),"حجم الورق"]}),i.jsx("div",{className:"space-y-2",children:u.map(d=>i.jsxs("label",{className:`flex items-center p-3 border rounded-lg cursor-pointer transition-all ${s.paperSize===d.value?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600"}`,children:[i.jsx("input",{type:"radio",name:"paperSize",value:d.value,checked:s.paperSize===d.value,onChange:h=>{a(N=>({...N,paperSize:h.target.value})),c({"58mm":"small","80mm":"medium",A4:"a4"}[h.target.value]||"medium")},className:"sr-only"}),i.jsxs("div",{className:"flex-1",children:[i.jsx("div",{className:"font-medium text-gray-900 dark:text-gray-100",children:d.label}),i.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:d.description})]}),s.paperSize===d.value&&i.jsx("div",{className:"w-4 h-4 bg-primary-500 rounded-full"})]},d.value))})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"جودة الطباعة"}),i.jsx("select",{value:s.quality,onChange:d=>a(h=>({...h,quality:d.target.value})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",children:E.map(d=>i.jsxs("option",{value:d.value,children:[d.label," - ",d.description]},d.value))})]}),i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"عدد النسخ"}),i.jsx("input",{type:"number",min:"1",max:"10",value:s.copies,onChange:d=>a(h=>({...h,copies:parseInt(d.target.value)||1})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"}),s.copies>1&&i.jsx("div",{className:"mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-md",children:i.jsxs("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:["💡 سيتم طباعة ",s.copies," نسخ في مستند واحد مع فاصل صفحات بين كل نسخة"]})})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"الهوامش"}),i.jsxs("select",{value:s.margins,onChange:d=>a(h=>({...h,margins:d.target.value})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",children:[i.jsx("option",{value:"none",children:"بدون هوامش"}),i.jsx("option",{value:"minimum",children:"هوامش صغيرة"}),i.jsx("option",{value:"normal",children:"هوامش عادية"})]})]})]}),i.jsxs("div",{children:[i.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"وضع الألوان"}),i.jsxs("select",{value:s.colorMode,onChange:d=>a(h=>({...h,colorMode:d.target.value})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",children:[i.jsx("option",{value:"blackwhite",children:"أبيض وأسود"}),i.jsx("option",{value:"grayscale",children:"رمادي"}),i.jsx("option",{value:"color",children:"ملون"})]})]}),i.jsxs("div",{className:"flex justify-end space-x-3 space-x-reverse pt-4 border-t border-gray-200 dark:border-gray-600",children:[i.jsxs("button",{onClick:e,className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:[i.jsx(Ye,{className:"inline mr-2"}),"إلغاء"]}),i.jsxs("button",{onClick:l,className:"px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors",children:[i.jsx(ve,{className:"inline mr-2"}),"طباعة"]})]})]})})},Ot=()=>{const{id:n}=He(),e=Ke(),[t,o]=P.useState(null),[r,s]=P.useState({name:"",address:"",phone:"",email:"",tax_rate:"0",receipt_header:"",receipt_footer:"",currency_symbol:"د.ل"}),[a,l]=P.useState(!0),[c,m]=P.useState(null),[u,E]=P.useState("medium"),[d,h]=P.useState(""),[w,N]=P.useState(!1);P.useEffect(()=>{(async()=>{l(!0),m(null);try{const p=parseInt(n||"0");if(isNaN(p)||p<=0)throw new Error("رقم الفاتورة غير صالح");const f=(await O.get(`/api/sales/${p}`)).data;if(!f||!f.items||!Array.isArray(f.items))throw new Error("بيانات الفاتورة غير صالحة");const C=await Promise.all(f.items.map(async B=>{try{const J=await O.get(`/api/products/${B.product_id}`);return{...B,product_name:J.data.name||`منتج #${B.product_id}`,price:B.unit_price}}catch{return{...B,product_name:`منتج #${B.product_id}`,price:B.unit_price}}}));let A="الكاشير";if(f.user_id)try{const B=await O.get(`/api/users/${f.user_id}`);A=B.data.full_name||B.data.username||"الكاشير"}catch{console.log("Could not fetch user data, using default cashier name"),f.user&&f.user.full_name?A=f.user.full_name:f.user&&f.user.username&&(A=f.user.username)}else f.user&&(A=f.user.full_name||f.user.username||"الكاشير");let k=null;if(f.customer_id&&f.customer_id!==null)try{k=(await O.get(`/api/customers/${f.customer_id}`)).data}catch{console.log("Could not fetch customer data"),f.customer&&(k=f.customer)}else f.customer&&(k=f.customer);const T=f.discount_amount||0,Oe=f.total_amount-T+(f.tax_amount||0),Ve={...f,items:C,payment_amount:f.amount_paid||Oe,change_amount:0,cashier_name:A,customer:k};o(Ve),s({name:"Smart POS",address:"ليبيا، طرابلس",phone:"+218 91-234-5678",email:"<EMAIL>",tax_rate:"0",receipt_header:"شكراً لاختيارك Smart POS",receipt_footer:"نتمنى لك يوماً سعيداً",currency_symbol:"د.ل"});try{const B=await O.get("/api/settings");if(B.data&&Array.isArray(B.data)){const J=B.data,M={};J.forEach(xe=>{M[xe.key]=xe.value}),Object.keys(M).length>0&&s({name:M.store_name||"Smart POS",address:M.store_address||"ليبيا، طرابلس",phone:M.store_phone||"+218 91-234-5678",email:M.store_email||"<EMAIL>",tax_rate:M.tax_rate||"0",receipt_header:M.receipt_header||"شكراً لاختيارك Smart POS",receipt_footer:M.receipt_footer||"نتمنى لك يوماً سعيداً",currency_symbol:M.currency_symbol||"د.ل"})}}catch{console.log("Settings API not available, using default store info")}l(!1)}catch(p){console.error("Error fetching receipt data:",p),m("فشل في تحميل بيانات الفاتورة"),l(!1)}})()},[n]),P.useEffect(()=>{(async()=>{if(t&&r.phone)try{const p=t.discount_amount||0,f=t.total_amount-p+(t.tax_amount||0);let C=`${r.name}
`;if(C+=`فاتورة: ${t.id}
`,C+=`المبلغ: ${f.toFixed(2)} ${r.currency_symbol}
`,C+=`التاريخ: ${y(t.created_at)}
`,t.customer&&t.customer.name!=="عميل مباشر"&&(C+=`العميل: ${t.customer.name}
`),t.amount_paid!==f){C+=`مدفوع: ${t.amount_paid.toFixed(2)} ${r.currency_symbol}
`;const k=f-t.amount_paid;C+=`متبقي: ${k.toFixed(2)} ${r.currency_symbol}
`}C+=`هاتف: ${r.phone}`;const A=await K.toDataURL(C,{width:200,margin:4,errorCorrectionLevel:"M",color:{dark:"#000000",light:"#FFFFFF"}});h(A)}catch(p){console.error("Error generating QR code:",p)}})()},[t,r]);const S=()=>{N(!0)},b=g=>{const p=document.createElement("style");p.textContent=`
      @media print {
        @page {
          size: ${g.paperSize==="A4"?"A4":g.paperSize} ${g.orientation};
          margin: ${g.margins==="none"?"0":g.margins==="minimum"?"5mm":"10mm"};
        }

        body {
          -webkit-print-color-adjust: ${g.colorMode==="color"?"exact":"economy"};
          print-color-adjust: ${g.colorMode==="color"?"exact":"economy"};
        }

        .receipt-wrapper {
          filter: ${g.colorMode==="grayscale"?"grayscale(100%)":"none"};
        }

        /* إضافة النسخ المتعددة في نفس المستند */
        .receipt-copies-container {
          display: block;
        }

        .receipt-copy {
          page-break-after: always;
        }

        .receipt-copy:last-child {
          page-break-after: auto;
        }
      }
    `,document.head.appendChild(p),g.copies>1&&j(g.copies),window.print(),setTimeout(()=>{x(),document.head.removeChild(p)},2e3)},j=g=>{const p=document.querySelector(".receipt-wrapper"),_=document.querySelector(".receipt-container");if(!p||!_)return;const f=document.createElement("div");f.className="receipt-copies-container print-only",f.style.display="none";for(let A=1;A<g;A++){const k=document.createElement("div");k.className="receipt-copy";const T=p.cloneNode(!0),re=T.querySelector(".receipt-footer p:last-child");re&&(re.textContent=`** نسخة ${A+1} **`),k.appendChild(T),f.appendChild(k)}_.appendChild(f);const C=document.createElement("style");C.textContent=`
      @media print {
        .receipt-copies-container {
          display: block !important;
        }
      }
    `,C.id="print-copies-style",document.head.appendChild(C)},x=()=>{const g=document.querySelector(".receipt-copies-container"),p=document.getElementById("print-copies-style");g&&g.remove(),p&&p.remove()},y=g=>{const p=new Date(g),_=String(p.getDate()).padStart(2,"0"),f=String(p.getMonth()+1).padStart(2,"0"),C=p.getFullYear();return`${_}/${f}/${C}`},v=g=>new Date(g).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!1});return a?i.jsx("div",{className:"flex justify-center items-center h-screen",children:i.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"})}):c||!t?i.jsxs("div",{className:"container mx-auto px-4 py-6 flex flex-col items-center justify-center min-h-[70vh]",children:[i.jsxs("div",{className:"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4 max-w-md w-full text-center",children:[i.jsx("h2",{className:"text-xl font-bold mb-2",children:"عذراً!"}),i.jsx("p",{children:c||"حدث خطأ في تحميل الفاتورة"}),i.jsx("p",{className:"mt-2 text-sm",children:"يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى."})]}),i.jsxs("button",{onClick:()=>e(-1),className:"mt-4 flex items-center bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors",children:[i.jsx(we,{className:"ml-2"})," العودة للصفحة السابقة"]})]}):i.jsxs("div",{className:"receipt-container",children:[i.jsxs("div",{className:"print-controls print:hidden container mx-auto px-4 py-6 mb-6",children:[i.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:i.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:i.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center p-6 gap-4",children:[i.jsxs("div",{className:"flex items-center",children:[i.jsx("button",{onClick:()=>e(-1),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md p-2 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm",children:i.jsx(we,{})}),i.jsxs("h1",{className:"text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100 mr-4 flex items-center",children:[i.jsx(ne,{className:"ml-3 text-primary-600 dark:text-primary-400"}),"فاتورة رقم #",t.id]})]}),i.jsxs("div",{className:"flex items-center gap-2 flex-wrap",children:[i.jsxs("button",{onClick:()=>window.print(),className:"bg-gray-600 text-white rounded-md py-2 px-3 sm:px-4 hover:bg-gray-700 flex items-center transition-all shadow-md hover:shadow-lg",children:[i.jsx(ve,{className:"ml-2"}),i.jsx("span",{className:"hidden sm:inline",children:"طباعة سريعة"}),i.jsx("span",{className:"sm:hidden",children:"سريعة"})]}),i.jsxs("button",{onClick:S,className:"bg-primary-600 text-white rounded-md py-2 px-3 sm:px-4 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg",children:[i.jsx(Ee,{className:"ml-2"}),i.jsx("span",{className:"hidden sm:inline",children:"خصائص الطباعة"}),i.jsx("span",{className:"sm:hidden",children:"خصائص"})]})]})]})})}),i.jsxs("div",{className:"receipt-size-selector",children:[i.jsx("h2",{className:"text-lg font-medium ml-4",children:"حجم الفاتورة:"}),i.jsxs("button",{onClick:()=>E("small"),className:`receipt-size-button ${u==="small"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[i.jsx(ne,{}),i.jsx("span",{children:"صغير (58mm)"})]}),i.jsxs("button",{onClick:()=>E("medium"),className:`receipt-size-button ${u==="medium"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[i.jsx(ne,{}),i.jsx("span",{children:"متوسط (80mm)"})]}),i.jsxs("button",{onClick:()=>E("a4"),className:`receipt-size-button ${u==="a4"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[i.jsx(Qe,{}),i.jsx("span",{children:"A4"})]})]})]}),i.jsx("div",{className:`receipt-wrapper receipt-size-${u}`,children:i.jsxs("div",{className:"receipt-content",children:[i.jsxs("div",{className:"receipt-header text-center receipt-section",children:[i.jsx("h1",{children:r.name}),r.address&&i.jsx("p",{className:"mb-1",children:r.address}),r.phone&&i.jsxs("p",{className:"mb-1",children:["هاتف: ",r.phone]}),r.email&&i.jsx("p",{className:"mb-1",children:r.email}),r.receipt_header&&i.jsx("p",{className:"mt-2 italic",children:r.receipt_header}),i.jsx("div",{className:"border-t border-gray-300 dark:border-gray-700 my-3"}),i.jsx("h2",{children:"فاتورة مبيعات"})]}),i.jsxs("div",{className:"receipt-info grid grid-cols-2 gap-2 receipt-section",children:[i.jsxs("div",{children:[i.jsxs("p",{children:[i.jsx("strong",{children:"رقم الفاتورة:"})," #",t.id]}),i.jsxs("p",{children:[i.jsx("strong",{children:"التاريخ:"})," ",y(t.created_at)]}),i.jsxs("p",{children:[i.jsx("strong",{children:"الوقت:"})," ",v(t.created_at)]})]}),i.jsxs("div",{className:"text-left",children:[i.jsxs("p",{children:[i.jsx("strong",{children:"الكاشير:"})," ",t.cashier_name||"-"]}),i.jsxs("p",{children:[i.jsx("strong",{children:"رقم الفاتورة الضريبي:"})," INV-",t.id,"-",new Date(t.created_at).getFullYear()]}),i.jsxs("p",{children:[i.jsx("strong",{children:"حالة الدفع:"})," ",t.payment_status==="paid"?"مدفوع":t.payment_status==="partial"?"جزئي":t.payment_status==="credit"?"آجل":"غير محدد"]})]})]}),t.customer&&t.customer.name!=="عميل مباشر"&&i.jsxs("div",{className:"receipt-customer receipt-section",children:[i.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"معلومات العميل"}),i.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[i.jsxs("div",{children:[i.jsxs("p",{children:[i.jsx("strong",{children:"الاسم:"})," ",t.customer.name]}),t.customer.phone&&i.jsxs("p",{children:[i.jsx("strong",{children:"الهاتف:"})," ",t.customer.phone]})]}),i.jsxs("div",{className:"text-left",children:[t.customer.email&&i.jsxs("p",{children:[i.jsx("strong",{children:"البريد الإلكتروني:"})," ",t.customer.email]}),t.customer.total_debt!==void 0&&t.customer.total_debt>0&&i.jsxs("p",{children:[i.jsx("strong",{children:"إجمالي المديونية:"})," ",t.customer.total_debt.toFixed(2)," ",r.currency_symbol]})]})]}),t.customer.address&&i.jsxs("p",{className:"mt-2",children:[i.jsx("strong",{children:"العنوان:"})," ",t.customer.address]})]}),i.jsxs("div",{className:"receipt-items py-2 receipt-section",children:[i.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"تفاصيل المنتجات"}),i.jsxs("table",{className:"receipt-items-table",children:[i.jsx("thead",{children:i.jsxs("tr",{children:[i.jsx("th",{className:"text-right",children:"المنتج"}),i.jsx("th",{className:"text-center",children:"الكمية"}),i.jsx("th",{className:"text-center",children:"السعر"}),i.jsx("th",{className:"text-left",children:"المجموع"})]})}),i.jsx("tbody",{children:t.items.map(g=>i.jsxs("tr",{children:[i.jsx("td",{className:"text-right",children:g.product_name||`منتج #${g.product_id}`}),i.jsx("td",{className:"text-center",children:g.quantity}),i.jsx("td",{className:"text-center",children:(g.price||g.unit_price).toFixed(2)}),i.jsx("td",{className:"text-left",children:g.subtotal.toFixed(2)})]},g.id))})]})]}),i.jsxs("div",{className:"receipt-totals receipt-section",children:[i.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"ملخص الفاتورة"}),(()=>{const g=t.discount_amount||0,p=t.total_amount,_=p-g,f=t.tax_amount||0,C=_+f;return i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"المجموع قبل الخصم:"}),i.jsxs("span",{children:[p.toFixed(2)," ",r.currency_symbol]})]}),g>0&&i.jsxs("div",{className:"total-row discount-row",children:[i.jsxs("span",{children:["الخصم (",t.discount_type==="percentage"?"نسبة مئوية":"مبلغ ثابت","):"]}),i.jsxs("span",{children:["-",g.toFixed(2)," ",r.currency_symbol,t.discount_type==="percentage"&&i.jsxs("span",{className:"text-xs mr-1",children:["(",(g/p*100).toFixed(1),"%)"]})]})]}),i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"المجموع بعد الخصم:"}),i.jsxs("span",{children:[_.toFixed(2)," ",r.currency_symbol]})]}),f>0&&i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"الضريبة:"}),i.jsxs("span",{children:[f.toFixed(2)," ",r.currency_symbol]})]}),i.jsxs("div",{className:"total-row grand-total",children:[i.jsx("span",{children:"المجموع النهائي:"}),i.jsxs("span",{children:[C.toFixed(2)," ",r.currency_symbol]})]})]})})()]}),i.jsxs("div",{className:"receipt-payment receipt-section",children:[i.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"معلومات الدفع"}),(()=>{const g=t.discount_amount||0,p=t.total_amount-g,_=t.tax_amount||0,f=p+_,C=t.amount_paid||0,A=f-C,k=t.change_amount||0,T=()=>{if(t.payment_status==="credit")return"آجل";if(t.payment_status==="partial")return"جزئي";switch(t.payment_method){case"cash":return"نقدي";case"card":return"بطاقة";case"credit":return"آجل";case"partial":return"جزئي";default:return t.payment_method}};return i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"طريقة الدفع:"}),i.jsx("span",{children:T()})]}),i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"المبلغ المدفوع:"}),i.jsxs("span",{children:[C.toFixed(2)," ",r.currency_symbol]})]}),A>0&&i.jsxs("div",{className:"total-row debt-row",style:{color:"#dc2626"},children:[i.jsx("span",{children:"المبلغ المتبقي (مديونية):"}),i.jsxs("span",{children:[A.toFixed(2)," ",r.currency_symbol]})]}),t.payment_method==="cash"&&k>0&&i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"المتبقي (الباقي):"}),i.jsxs("span",{children:[k.toFixed(2)," ",r.currency_symbol]})]}),i.jsxs("div",{className:"total-row",children:[i.jsx("span",{children:"حالة الفاتورة:"}),i.jsx("span",{style:{color:t.payment_status==="paid"?"#16a34a":t.payment_status==="partial"?"#ea580c":t.payment_status==="credit"?"#dc2626":"#6b7280"},children:t.payment_status==="paid"?"✓ مدفوعة بالكامل":t.payment_status==="partial"?"◐ مدفوعة جزئياً":t.payment_status==="credit"?"○ آجلة":"غير محدد"})]})]})})()]}),t.notes&&i.jsxs("div",{className:"receipt-notes mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md receipt-section",children:[i.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"ملاحظات"}),i.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:t.notes})]}),i.jsxs("div",{className:"receipt-footer receipt-section",children:[r.receipt_footer&&i.jsx("p",{className:"mb-3",children:r.receipt_footer}),i.jsx("p",{className:"mt-1",children:"** نسخة العميل **"})]}),d&&i.jsxs("div",{className:"receipt-qr",children:[i.jsx("div",{className:"qr-container",children:i.jsx("img",{src:d,alt:"QR Code",className:"qr-image"})}),i.jsxs("div",{className:"qr-info",children:[i.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 mb-1",children:"امسح الرمز للحصول على معلومات الفاتورة"}),i.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:["هاتف المتجر: ",r.phone]})]})]}),u==="a4"&&i.jsx("div",{className:"receipt-stamp",children:"مدفوع"})]})}),i.jsx(Dt,{isOpen:w,onClose:()=>N(!1),onPrint:b,receiptSize:u,onReceiptSizeChange:g=>E(g)})]})};export{Ot as default};
