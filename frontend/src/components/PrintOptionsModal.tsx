import React, { useState } from 'react';
import { FaPrint, FaTimes, FaCog, FaFileAlt, FaMobile, FaDesktop } from 'react-icons/fa';
import Modal from './Modal';

interface PrintOptionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPrint: (options: PrintOptions) => void;
  receiptSize: string;
  onReceiptSizeChange: (size: string) => void;
}

export interface PrintOptions {
  printerType: 'thermal' | 'laser' | 'inkjet';
  paperSize: string;
  orientation: 'portrait' | 'landscape';
  quality: 'draft' | 'normal' | 'high';
  copies: number;
  margins: 'none' | 'minimum' | 'normal';
  colorMode: 'color' | 'grayscale' | 'blackwhite';
}

const PrintOptionsModal: React.FC<PrintOptionsModalProps> = ({
  isOpen,
  onClose,
  onPrint,
  receiptSize,
  onReceiptSizeChange
}) => {
  const [printOptions, setPrintOptions] = useState<PrintOptions>({
    printerType: 'thermal',
    paperSize: receiptSize === 'small' ? '58mm' : receiptSize === 'medium' ? '80mm' : 'A4',
    orientation: 'portrait',
    quality: 'normal',
    copies: 1,
    margins: 'minimum',
    colorMode: 'blackwhite'
  });

  const handlePrint = () => {
    // إغلاق النافذة أولاً ثم تمرير خيارات الطباعة
    onClose();

    // انتظار قصير للتأكد من إغلاق النافذة قبل الطباعة
    setTimeout(() => {
      onPrint(printOptions);
    }, 150);
  };

  const handleReceiptSizeChange = (size: string) => {
    onReceiptSizeChange(size);
    setPrintOptions(prev => ({
      ...prev,
      paperSize: size === 'small' ? '58mm' : size === 'medium' ? '80mm' : 'A4',
      printerType: size === 'a4' ? 'laser' : 'thermal'
    }));
  };

  const printerTypes = [
    { value: 'thermal', label: 'طابعة حرارية', icon: FaMobile },
    { value: 'laser', label: 'طابعة ليزر', icon: FaDesktop },
    { value: 'inkjet', label: 'طابعة حبر', icon: FaFileAlt }
  ];

  const paperSizes = [
    { value: '58mm', label: '58mm (صغير)', description: 'للطابعات الحرارية الصغيرة' },
    { value: '80mm', label: '80mm (متوسط)', description: 'للطابعات الحرارية المتوسطة' },
    { value: 'A4', label: 'A4 (كبير)', description: 'للطابعات العادية' }
  ];

  const qualityOptions = [
    { value: 'draft', label: 'مسودة', description: 'سريع وموفر للحبر' },
    { value: 'normal', label: 'عادي', description: 'جودة متوازنة' },
    { value: 'high', label: 'عالي', description: 'أفضل جودة' }
  ];

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="خصائص الطباعة">
      <div className="space-y-6">
        {/* نوع الطابعة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            <FaCog className="inline mr-2" />
            نوع الطابعة
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {printerTypes.map((type) => {
              const IconComponent = type.icon;
              return (
                <button
                  key={type.value}
                  onClick={() => setPrintOptions(prev => ({ ...prev, printerType: type.value as any }))}
                  className={`p-4 border-2 rounded-lg text-center transition-all ${
                    printOptions.printerType === type.value
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                      : 'border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600'
                  }`}
                >
                  <IconComponent className="mx-auto mb-2 text-xl" />
                  <div className="text-sm font-medium">{type.label}</div>
                </button>
              );
            })}
          </div>
        </div>

        {/* حجم الورق */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            <FaFileAlt className="inline mr-2" />
            حجم الورق
          </label>
          <div className="space-y-2">
            {paperSizes.map((size) => (
              <label
                key={size.value}
                className={`flex items-center p-3 border rounded-lg cursor-pointer transition-all ${
                  printOptions.paperSize === size.value
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600'
                }`}
              >
                <input
                  type="radio"
                  name="paperSize"
                  value={size.value}
                  checked={printOptions.paperSize === size.value}
                  onChange={(e) => {
                    setPrintOptions(prev => ({ ...prev, paperSize: e.target.value }));
                    const receiptSizeMap: { [key: string]: string } = {
                      '58mm': 'small',
                      '80mm': 'medium',
                      'A4': 'a4'
                    };
                    handleReceiptSizeChange(receiptSizeMap[e.target.value] || 'medium');
                  }}
                  className="sr-only"
                />
                <div className="flex-1">
                  <div className="font-medium text-gray-900 dark:text-gray-100">{size.label}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">{size.description}</div>
                </div>
                {printOptions.paperSize === size.value && (
                  <div className="w-4 h-4 bg-primary-500 rounded-full"></div>
                )}
              </label>
            ))}
          </div>
        </div>

        {/* جودة الطباعة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            جودة الطباعة
          </label>
          <select
            value={printOptions.quality}
            onChange={(e) => setPrintOptions(prev => ({ ...prev, quality: e.target.value as any }))}
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            {qualityOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label} - {option.description}
              </option>
            ))}
          </select>
        </div>

        {/* خيارات إضافية */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* عدد النسخ */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              عدد النسخ
            </label>
            <input
              type="number"
              min="1"
              max="10"
              value={printOptions.copies}
              onChange={(e) => setPrintOptions(prev => ({ ...prev, copies: parseInt(e.target.value) || 1 }))}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
            {printOptions.copies > 1 && (
              <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-md">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  💡 سيتم طباعة {printOptions.copies} نسخ في مستند واحد مع فاصل صفحات بين كل نسخة
                </p>
              </div>
            )}
          </div>

          {/* الهوامش */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              الهوامش
            </label>
            <select
              value={printOptions.margins}
              onChange={(e) => setPrintOptions(prev => ({ ...prev, margins: e.target.value as any }))}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="none">بدون هوامش</option>
              <option value="minimum">هوامش صغيرة</option>
              <option value="normal">هوامش عادية</option>
            </select>
          </div>
        </div>

        {/* وضع الألوان */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            وضع الألوان
          </label>
          <select
            value={printOptions.colorMode}
            onChange={(e) => setPrintOptions(prev => ({ ...prev, colorMode: e.target.value as any }))}
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          >
            <option value="blackwhite">أبيض وأسود</option>
            <option value="grayscale">رمادي</option>
            <option value="color">ملون</option>
          </select>
        </div>

        {/* أزرار التحكم */}
        <div className="flex justify-end space-x-3 space-x-reverse pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            <FaTimes className="inline mr-2" />
            إلغاء
          </button>
          <button
            onClick={handlePrint}
            className="px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            <FaPrint className="inline mr-2" />
            طباعة
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default PrintOptionsModal;
