import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { FaPrint, FaArrowLeft, FaReceipt, FaFileInvoice } from 'react-icons/fa';
import QRCode from 'qrcode';
import api from '../lib/axios';
import './Receipt.css';

interface SaleDetail {
  id: number;
  total_amount: number; // Amount before tax and discount
  payment_method: string;
  tax_amount: number; // Tax amount in currency
  discount_amount: number; // Discount amount
  discount_type: string; // 'fixed' or 'percentage'
  customer_name: string | null;
  customer_id: number | null; // Customer ID
  notes: string | null;
  created_at: string;
  updated_at: string | null;
  user_id: number;
  items: SaleItem[];
  // Payment fields
  amount_paid: number; // Amount actually paid
  payment_status: string; // 'paid', 'partial', 'credit'
  // Additional fields for receipt
  payment_amount?: number;
  change_amount?: number;
  cashier_name?: string;
  // Customer information
  customer?: {
    id: number;
    name: string;
    phone?: string;
    email?: string;
    address?: string;
    total_debt?: number;
  };
}

interface SaleItem {
  id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  discount: number;
  subtotal: number;
  sale_id: number;
  created_at: string;
  // Campos adicionales para la factura
  product_name?: string;
  price?: number; // Alias para unit_price para compatibilidad
}

interface StoreInfo {
  name: string;
  address: string;
  phone: string;
  email: string;
  tax_rate: string;
  receipt_header: string;
  receipt_footer: string;
  currency_symbol: string;
}

const Receipt: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [sale, setSale] = useState<SaleDetail | null>(null);
  const [storeInfo, setStoreInfo] = useState<StoreInfo>({
    name: '',
    address: '',
    phone: '',
    email: '',
    tax_rate: '0',
    receipt_header: '',
    receipt_footer: '',
    currency_symbol: 'د.ل'
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [receiptSize, setReceiptSize] = useState<'small' | 'medium' | 'a4'>('medium');
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Check if the ID is a valid number
        const saleId = parseInt(id || '0');
        if (isNaN(saleId) || saleId <= 0) {
          throw new Error('رقم الفاتورة غير صالح');
        }

        // Fetch sale details
        const saleResponse = await api.get(`/api/sales/${saleId}`);

        // Adaptar los datos de la venta para que coincidan con nuestra interfaz
        const saleData = saleResponse.data;

        // Verificar que los datos de la venta son válidos
        if (!saleData || !saleData.items || !Array.isArray(saleData.items)) {
          throw new Error('بيانات الفاتورة غير صالحة');
        }

        // Obtener el nombre del producto para cada item
        const itemsWithProductNames = await Promise.all(
          saleData.items.map(async (item: any) => {
            try {
              const productResponse = await api.get(`/api/products/${item.product_id}`);
              return {
                ...item,
                product_name: productResponse.data.name || `منتج #${item.product_id}`,
                price: item.unit_price // Añadir campo price para compatibilidad
              };
            } catch (error) {
              return {
                ...item,
                product_name: `منتج #${item.product_id}`,
                price: item.unit_price // Añadir campo price para compatibilidad
              };
            }
          })
        );

        // Get cashier name from user data if available
        let cashierName = 'الكاشير'; // Default name
        if (saleData.user_id) {
          try {
            const userResponse = await api.get(`/api/users/${saleData.user_id}`);
            cashierName = userResponse.data.full_name || userResponse.data.username || 'الكاشير';
          } catch (userError) {
            console.log('Could not fetch user data, using default cashier name');
            // Check if there's a user object in the sale data
            if (saleData.user && saleData.user.full_name) {
              cashierName = saleData.user.full_name;
            } else if (saleData.user && saleData.user.username) {
              cashierName = saleData.user.username;
            }
          }
        } else if (saleData.user) {
          // If user object is included in sale data
          cashierName = saleData.user.full_name || saleData.user.username || 'الكاشير';
        }

        // Get customer information if available
        let customerInfo = null;
        if (saleData.customer_id && saleData.customer_id !== null) {
          try {
            const customerResponse = await api.get(`/api/customers/${saleData.customer_id}`);
            customerInfo = customerResponse.data;
          } catch (customerError) {
            console.log('Could not fetch customer data');
            // Check if there's a customer object in the sale data
            if (saleData.customer) {
              customerInfo = saleData.customer;
            }
          }
        } else if (saleData.customer) {
          // If customer object is included in sale data
          customerInfo = saleData.customer;
        }

        // حساب المبلغ المدفوع الصحيح (المبلغ النهائي بعد الخصم والضريبة)
        const discountAmount = saleData.discount_amount || 0;
        const totalAfterDiscount = saleData.total_amount - discountAmount;
        const finalAmount = totalAfterDiscount + (saleData.tax_amount || 0);

        // Añadir campos adicionales para la factura
        const enhancedSale = {
          ...saleData,
          items: itemsWithProductNames,
          payment_amount: saleData.amount_paid || finalAmount, // Use actual paid amount
          change_amount: 0, // Asumimos que no hay cambio
          cashier_name: cashierName,
          customer: customerInfo // Add customer information
        };

        setSale(enhancedSale);

        // Set default store info since the settings API is not available
        setStoreInfo({
          name: 'Smart POS',
          address: 'ليبيا، طرابلس',
          phone: '+218 91-234-5678',
          email: '<EMAIL>',
          tax_rate: '0',
          receipt_header: 'شكراً لاختيارك Smart POS',
          receipt_footer: 'نتمنى لك يوماً سعيداً',
          currency_symbol: 'د.ل'
        });

        // Try to fetch store settings if available
        try {
          const settingsResponse = await api.get('/api/settings');
          if (settingsResponse.data && Array.isArray(settingsResponse.data)) {
            const settings = settingsResponse.data;

            // Convert settings array to object
            const storeSettings: Record<string, string> = {};
            settings.forEach((setting: { key: string, value: string }) => {
              storeSettings[setting.key] = setting.value;
            });

            // Only update if we got valid settings
            if (Object.keys(storeSettings).length > 0) {
              setStoreInfo({
                name: storeSettings.store_name || 'Smart POS',
                address: storeSettings.store_address || 'ليبيا، طرابلس',
                phone: storeSettings.store_phone || '+218 91-234-5678',
                email: storeSettings.store_email || '<EMAIL>',
                tax_rate: storeSettings.tax_rate || '0',
                receipt_header: storeSettings.receipt_header || 'شكراً لاختيارك Smart POS',
                receipt_footer: storeSettings.receipt_footer || 'نتمنى لك يوماً سعيداً',
                currency_symbol: storeSettings.currency_symbol || 'د.ل'
              });
            }
          }
        } catch (settingsError) {
          console.log('Settings API not available, using default store info');
        }



        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching receipt data:', error);
        setError('فشل في تحميل بيانات الفاتورة');
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // Generate QR Code when sale and store info are available
  useEffect(() => {
    const generateQRCode = async () => {
      if (sale && storeInfo.phone) {
        try {
          // Calculate total with discount and tax
          const discountAmount = sale.discount_amount || 0;
          const totalAfterDiscount = sale.total_amount - discountAmount;
          const grandTotalWithTax = totalAfterDiscount + (sale.tax_amount || 0);

          // Build QR data with customer information
          let qrData = `هاتف المتجر: ${storeInfo.phone}\nفاتورة رقم: ${sale.id}\nالمبلغ النهائي: ${grandTotalWithTax.toFixed(2)} ${storeInfo.currency_symbol}`;

          if (sale.customer && sale.customer.name !== 'عميل مباشر') {
            qrData += `\nالعميل: ${sale.customer.name}`;
            if (sale.customer.phone) {
              qrData += `\nهاتف العميل: ${sale.customer.phone}`;
            }
          }

          if (sale.amount_paid !== grandTotalWithTax) {
            qrData += `\nالمبلغ المدفوع: ${sale.amount_paid.toFixed(2)} ${storeInfo.currency_symbol}`;
            const debtAmount = grandTotalWithTax - sale.amount_paid;
            qrData += `\nالمديونية: ${debtAmount.toFixed(2)} ${storeInfo.currency_symbol}`;
          }

          if (discountAmount > 0) {
            qrData += `\nالخصم: ${discountAmount.toFixed(2)} ${storeInfo.currency_symbol}`;
          }

          qrData += `\nالتاريخ: ${formatDate(sale.created_at)}\nالكاشير: ${sale.cashier_name || 'غير محدد'}`;
          const qrCodeDataUrl = await QRCode.toDataURL(qrData, {
            width: 120,
            margin: 2,
            color: {
              dark: '#000000',
              light: '#FFFFFF'
            }
          });
          setQrCodeUrl(qrCodeDataUrl);
        } catch (error) {
          console.error('Error generating QR code:', error);
        }
      }
    };

    generateQRCode();
  }, [sale, storeInfo]);

  const handlePrint = () => {
    window.print();
  };

  const formatDate = (dateString: string) => {
    // Now that the backend is storing dates in Tripoli time correctly,
    // we don't need to add 2 hours anymore
    const date = new Date(dateString);

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };

  const formatTime = (dateString: string) => {
    // Now that the backend is storing dates in Tripoli time correctly,
    // we don't need to add 2 hours anymore
    const date = new Date(dateString);

    return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error || !sale) {
    return (
      <div className="container mx-auto px-4 py-6 flex flex-col items-center justify-center min-h-[70vh]">
        <div className="bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4 max-w-md w-full text-center">
          <h2 className="text-xl font-bold mb-2">عذراً!</h2>
          <p>{error || 'حدث خطأ في تحميل الفاتورة'}</p>
          <p className="mt-2 text-sm">يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.</p>
        </div>
        <button
          onClick={() => navigate(-1)}
          className="mt-4 flex items-center bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors"
        >
          <FaArrowLeft className="ml-2" /> العودة للصفحة السابقة
        </button>
      </div>
    );
  }

  return (
    <div className="receipt-container">
      {/* Print Controls - hidden when printing */}
      <div className="print-controls print:hidden container mx-auto px-4 py-6 mb-6">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
          <div className="bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-6 gap-4">
              <div className="flex items-center">
                <button
                  onClick={() => navigate(-1)}
                  className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md p-2 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm"
                >
                  <FaArrowLeft />
                </button>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100 mr-4 flex items-center">
                  <FaReceipt className="ml-3 text-primary-600 dark:text-primary-400" />
                  فاتورة رقم #{sale.id}
                </h1>
              </div>
              <div className="flex items-center gap-2 flex-wrap">
                <button
                  onClick={handlePrint}
                  className="bg-primary-600 text-white rounded-md py-2 px-3 sm:px-4 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg"
                >
                  <FaPrint className="ml-2" />
                  <span className="hidden sm:inline">طباعة الفاتورة</span>
                  <span className="sm:hidden">طباعة</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Receipt Size Selector */}
        <div className="receipt-size-selector">
          <h2 className="text-lg font-medium ml-4">حجم الفاتورة:</h2>
          <button
            onClick={() => setReceiptSize('small')}
            className={`receipt-size-button ${receiptSize === 'small' ? 'active' : 'bg-gray-100 hover:bg-gray-200'}`}
          >
            <FaReceipt />
            <span>صغير (58mm)</span>
          </button>
          <button
            onClick={() => setReceiptSize('medium')}
            className={`receipt-size-button ${receiptSize === 'medium' ? 'active' : 'bg-gray-100 hover:bg-gray-200'}`}
          >
            <FaReceipt />
            <span>متوسط (80mm)</span>
          </button>
          <button
            onClick={() => setReceiptSize('a4')}
            className={`receipt-size-button ${receiptSize === 'a4' ? 'active' : 'bg-gray-100 hover:bg-gray-200'}`}
          >
            <FaFileInvoice />
            <span>A4</span>
          </button>
        </div>
      </div>

      {/* Receipt - styled for printing */}
      <div className={`receipt-wrapper receipt-size-${receiptSize}`}>
        <div className="receipt-content">
          {/* Header */}
          <div className="receipt-header text-center receipt-section">
            <h1>{storeInfo.name}</h1>
            {storeInfo.address && <p className="mb-1">{storeInfo.address}</p>}
            {storeInfo.phone && <p className="mb-1">هاتف: {storeInfo.phone}</p>}
            {storeInfo.email && <p className="mb-1">{storeInfo.email}</p>}
            {storeInfo.receipt_header && (
              <p className="mt-2 italic">{storeInfo.receipt_header}</p>
            )}
            <div className="border-t border-gray-300 dark:border-gray-700 my-3"></div>
            <h2>فاتورة مبيعات</h2>
          </div>

          {/* Receipt Info */}
          <div className="receipt-info grid grid-cols-2 gap-2 receipt-section">
            <div>
              <p><strong>رقم الفاتورة:</strong> #{sale.id}</p>
              <p><strong>التاريخ:</strong> {formatDate(sale.created_at)}</p>
              <p><strong>الوقت:</strong> {formatTime(sale.created_at)}</p>
            </div>
            <div className="text-left">
              <p><strong>الكاشير:</strong> {sale.cashier_name || '-'}</p>
              <p><strong>رقم الفاتورة الضريبي:</strong> INV-{sale.id}-{new Date(sale.created_at).getFullYear()}</p>
              <p><strong>حالة الدفع:</strong> {
                sale.payment_status === 'paid' ? 'مدفوع' :
                sale.payment_status === 'partial' ? 'جزئي' :
                sale.payment_status === 'credit' ? 'آجل' : 'غير محدد'
              }</p>
            </div>
          </div>

          {/* Customer Info - if customer exists and is not direct customer */}
          {sale.customer && sale.customer.name !== 'عميل مباشر' && (
            <div className="receipt-customer receipt-section">
              <h3 className="font-bold text-secondary-700 dark:text-secondary-300 mb-2">معلومات العميل</h3>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <p><strong>الاسم:</strong> {sale.customer.name}</p>
                  {sale.customer.phone && (
                    <p><strong>الهاتف:</strong> {sale.customer.phone}</p>
                  )}
                </div>
                <div className="text-left">
                  {sale.customer.email && (
                    <p><strong>البريد الإلكتروني:</strong> {sale.customer.email}</p>
                  )}
                  {sale.customer.total_debt !== undefined && sale.customer.total_debt > 0 && (
                    <p><strong>إجمالي المديونية:</strong> {sale.customer.total_debt.toFixed(2)} {storeInfo.currency_symbol}</p>
                  )}
                </div>
              </div>
              {sale.customer.address && (
                <p className="mt-2"><strong>العنوان:</strong> {sale.customer.address}</p>
              )}
            </div>
          )}

          {/* Items */}
          <div className="receipt-items py-2 receipt-section">
            <h3 className="font-bold text-secondary-700 dark:text-secondary-300 mb-2">تفاصيل المنتجات</h3>
            <table className="receipt-items-table">
              <thead>
                <tr>
                  <th className="text-right">المنتج</th>
                  <th className="text-center">الكمية</th>
                  <th className="text-center">السعر</th>
                  <th className="text-left">المجموع</th>
                </tr>
              </thead>
              <tbody>
                {sale.items.map(item => (
                  <tr key={item.id}>
                    <td className="text-right">{item.product_name || `منتج #${item.product_id}`}</td>
                    <td className="text-center">{item.quantity}</td>
                    <td className="text-center">{(item.price || item.unit_price).toFixed(2)}</td>
                    <td className="text-left">{item.subtotal.toFixed(2)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Totals */}
          <div className="receipt-totals receipt-section">
            <h3 className="font-bold text-secondary-700 dark:text-secondary-300 mb-2">ملخص الفاتورة</h3>
            {/* Calculate totals correctly using new discount structure */}
            {(() => {
              const discountAmount = sale.discount_amount || 0; // Use stored discount amount
              const totalBeforeDiscount = sale.total_amount; // This is now the amount before discount
              const totalAfterDiscount = totalBeforeDiscount - discountAmount;
              const taxAmount = sale.tax_amount || 0; // Use stored tax amount
              const grandTotalWithTax = totalAfterDiscount + taxAmount;

              return (
                <>
                  <div className="total-row">
                    <span>المجموع قبل الخصم:</span>
                    <span>{totalBeforeDiscount.toFixed(2)} {storeInfo.currency_symbol}</span>
                  </div>

                  {/* Invoice-level discount */}
                  {discountAmount > 0 && (
                    <div className="total-row discount-row">
                      <span>الخصم ({sale.discount_type === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}):</span>
                      <span>
                        -{discountAmount.toFixed(2)} {storeInfo.currency_symbol}
                        {sale.discount_type === 'percentage' && (
                          <span className="text-xs mr-1">
                            ({((discountAmount / totalBeforeDiscount) * 100).toFixed(1)}%)
                          </span>
                        )}
                      </span>
                    </div>
                  )}

                  {/* Subtotal after discount (before tax) */}
                  <div className="total-row">
                    <span>المجموع بعد الخصم:</span>
                    <span>{totalAfterDiscount.toFixed(2)} {storeInfo.currency_symbol}</span>
                  </div>

                  {/* Tax */}
                  {taxAmount > 0 && (
                    <div className="total-row">
                      <span>الضريبة:</span>
                      <span>
                        {taxAmount.toFixed(2)} {storeInfo.currency_symbol}
                      </span>
                    </div>
                  )}

                  {/* Grand total with tax */}
                  <div className="total-row grand-total">
                    <span>المجموع النهائي:</span>
                    <span>{grandTotalWithTax.toFixed(2)} {storeInfo.currency_symbol}</span>
                  </div>
                </>
              );
            })()}
          </div>

          {/* Payment Info */}
          <div className="receipt-payment receipt-section">
            <h3 className="font-bold text-secondary-700 dark:text-secondary-300 mb-2">معلومات الدفع</h3>
            {(() => {
              const discountAmount = sale.discount_amount || 0;
              const totalAfterDiscount = sale.total_amount - discountAmount;
              const taxAmount = sale.tax_amount || 0;
              const grandTotalWithTax = totalAfterDiscount + taxAmount;
              const paidAmount = sale.amount_paid || 0;
              const debtAmount = grandTotalWithTax - paidAmount;
              const changeAmount = sale.change_amount || 0;

              // Determine payment method display
              const getPaymentMethodText = () => {
                if (sale.payment_status === 'credit') return 'آجل';
                if (sale.payment_status === 'partial') return 'جزئي';

                switch (sale.payment_method) {
                  case 'cash': return 'نقدي';
                  case 'card': return 'بطاقة';
                  case 'credit': return 'آجل';
                  case 'partial': return 'جزئي';
                  default: return sale.payment_method;
                }
              };

              return (
                <>
                  <div className="total-row">
                    <span>طريقة الدفع:</span>
                    <span>{getPaymentMethodText()}</span>
                  </div>

                  <div className="total-row">
                    <span>المبلغ المدفوع:</span>
                    <span>{paidAmount.toFixed(2)} {storeInfo.currency_symbol}</span>
                  </div>

                  {/* Show debt amount if there's unpaid balance */}
                  {debtAmount > 0 && (
                    <div className="total-row debt-row" style={{ color: '#dc2626' }}>
                      <span>المبلغ المتبقي (مديونية):</span>
                      <span>{debtAmount.toFixed(2)} {storeInfo.currency_symbol}</span>
                    </div>
                  )}

                  {/* Show change if cash payment and there's change */}
                  {sale.payment_method === 'cash' && changeAmount > 0 && (
                    <div className="total-row">
                      <span>المتبقي (الباقي):</span>
                      <span>{changeAmount.toFixed(2)} {storeInfo.currency_symbol}</span>
                    </div>
                  )}

                  {/* Payment status indicator */}
                  <div className="total-row">
                    <span>حالة الفاتورة:</span>
                    <span style={{
                      color: sale.payment_status === 'paid' ? '#16a34a' :
                             sale.payment_status === 'partial' ? '#ea580c' :
                             sale.payment_status === 'credit' ? '#dc2626' : '#6b7280'
                    }}>
                      {sale.payment_status === 'paid' ? '✓ مدفوعة بالكامل' :
                       sale.payment_status === 'partial' ? '◐ مدفوعة جزئياً' :
                       sale.payment_status === 'credit' ? '○ آجلة' : 'غير محدد'}
                    </span>
                  </div>
                </>
              );
            })()}
          </div>

          {/* Notes */}
          {sale.notes && (
            <div className="receipt-notes mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md receipt-section">
              <h3 className="font-bold text-secondary-700 dark:text-secondary-300 mb-2">ملاحظات</h3>
              <p className="text-gray-600 dark:text-gray-400">{sale.notes}</p>
            </div>
          )}

          {/* Footer */}
          <div className="receipt-footer receipt-section">
            {storeInfo.receipt_footer && (
              <p className="mb-3">{storeInfo.receipt_footer}</p>
            )}
            <p className="mt-1">** نسخة العميل **</p>
          </div>

          {/* QR Code */}
          {(receiptSize === 'a4' || receiptSize === 'medium') && qrCodeUrl && (
            <div className="receipt-qr">
              <div className="qr-container">
                <img src={qrCodeUrl} alt="QR Code" className="qr-image" />
              </div>
              <div className="qr-info">
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">
                  امسح الرمز للحصول على معلومات الفاتورة
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  هاتف المتجر: {storeInfo.phone}
                </p>
              </div>
            </div>
          )}

          {/* Stamp for A4 size */}
          {receiptSize === 'a4' && (
            <div className="receipt-stamp">
              مدفوع
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Receipt;