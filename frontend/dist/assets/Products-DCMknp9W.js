import{c as ce,b as I,u as de,d as se,r as x,j as e,a as ge,e as me}from"./index-DmuezwQ-.js";import{Y as z,Z as xe,l as Z,_ as J,R as Q,T as oe,$ as pe,C as ne,B as le,O as ue,o as X,s as ee,a0 as he,a1 as ye,a2 as be}from"./index-Cn5nbJXI.js";import{T as re,B as fe,a as ke,N as W,S as ae}from"./BarcodeInput-npsHH5Ko.js";import{T as Y}from"./ToggleSwitch-CtfuXeD6.js";import{S as te}from"./SelectBox-DlAJEOvE.js";import{M as je}from"./Modal-CFBGiLJ5.js";import{D as ve,S as Ne}from"./SuccessModal-DZBIHrb5.js";const ie=ce((o,b)=>({products:[],categories:[],loading:!1,error:null,filters:{},selectedProduct:null,pagination:{total:0,page:1,limit:10,pages:1},fetchProducts:async r=>{var l,n;try{o({loading:!0,error:null});const a={};r!=null&&r.search&&r.search.trim()!==""&&(a.search=r.search.trim(),console.log("Adding search filter:",r.search.trim())),r!=null&&r.category&&r.category.trim()!==""&&(a.category=r.category.trim(),console.log("Adding category filter:",r.category.trim())),(r==null?void 0:r.lowStock)===!0&&(a.low_stock="true",console.log("Adding low_stock filter: true")),(r==null?void 0:r.zeroStock)===!0&&(a.zero_stock="true",console.log("Adding zero_stock filter: true")),(r==null?void 0:r.isActive)!==void 0&&(a.is_active=r.isActive?"true":"false",console.log(`Adding is_active filter: ${r.isActive}`));const h=(r==null?void 0:r.page)||1,i=(r==null?void 0:r.limit)||10;a.page=h.toString(),a.limit=i.toString(),console.log(`Adding pagination: page=${h}, limit=${i}`),a._t=new Date().getTime().toString(),console.log("Fetching products with params:",a);const j=de.getState().token;console.log("Using auth token for fetching products:",j?"Token exists":"No token");try{console.log("Making API request to /api/products with params:",a);const y=`/api/products/?${Object.entries(a).map(([v,N])=>`${encodeURIComponent(v)}=${encodeURIComponent(N)}`).join("&")}`;console.log("Full request URL:",y);const u=await I.get(y,{headers:{Authorization:`Bearer ${j}`,"Content-Type":"application/json"}});console.log("Products fetched successfully:",u.data.length,"with filters:",r),u.data.length>0?console.log("First few products:",u.data.slice(0,Math.min(3,u.data.length)).map(v=>({id:v.id,name:v.name,category:v.category}))):console.log("No products returned from API");const p=parseInt(u.headers["x-total-count"]||"0",10),F=parseInt(u.headers["x-page"]||"1",10),q=parseInt(u.headers["x-limit"]||"10",10),E=parseInt(u.headers["x-pages"]||"1",10);return console.log("Pagination info from headers:",{total:p,page:F,limit:q,pages:E}),o({products:u.data,loading:!1,pagination:{total:p,page:F,limit:q,pages:E}}),u.data}catch(g){console.error("API Error:",g),console.error("API Error details:",(l=g.response)==null?void 0:l.status,(n=g.response)==null?void 0:n.data),console.log("Trying direct request to backend...");const u=`http://localhost:8003/api/products/?${Object.entries(a).map(([N,R])=>`${encodeURIComponent(N)}=${encodeURIComponent(R)}`).join("&")}`;console.log("Full direct request URL:",u);const p=await I.get(u,{headers:{Authorization:`Bearer ${j}`,"Content-Type":"application/json"}});console.log("Direct request successful:",p.data.length),p.data.length>0?console.log("First few products from direct request:",p.data.slice(0,Math.min(3,p.data.length)).map(N=>({id:N.id,name:N.name,category:N.category}))):console.log("No products returned from direct API request");const F=parseInt(p.headers["x-total-count"]||"0",10),q=parseInt(p.headers["x-page"]||"1",10),E=parseInt(p.headers["x-limit"]||"10",10),v=parseInt(p.headers["x-pages"]||"1",10);return console.log("Pagination info from direct headers:",{total:F,page:q,limit:E,pages:v}),o({products:p.data,loading:!1,pagination:{total:F,page:q,limit:E,pages:v}}),p.data}}catch(a){throw console.error("Error fetching products:",a),o({error:a instanceof Error?a.message:"فشل في جلب المنتجات",loading:!1}),a}},fetchCategories:async()=>{try{const r=await I.get("/api/products/categories/list");o({categories:r.data})}catch(r){o({error:r instanceof Error?r.message:"Failed to fetch categories"})}},addCategory:async r=>{try{o({loading:!0,error:null});const l=await I.post("/api/products/categories",{category:r});return o({categories:l.data,loading:!1}),l.data}catch(l){throw o({error:l instanceof Error?l.message:"Failed to add category",loading:!1}),l}},deleteCategory:async r=>{var l,n,a,h;try{o({loading:!0,error:null});const i=await I.delete(`/api/products/categories/${encodeURIComponent(r)}`);return o({categories:i.data,loading:!1}),i.data}catch(i){throw((l=i==null?void 0:i.response)==null?void 0:l.status)===400&&((h=(a=(n=i==null?void 0:i.response)==null?void 0:n.data)==null?void 0:a.detail)!=null&&h.includes("Cannot delete category that has products"))?o({error:"لا يمكن حذف الفئة لأنها تحتوي على منتجات. قم بنقل أو حذف المنتجات أولاً.",loading:!1}):o({error:i instanceof Error?i.message:"فشل في حذف الفئة",loading:!1}),i}},createProduct:async r=>{try{o({loading:!0,error:null}),console.log("Creating product with data:",r);const l=await I.post("/api/products/no-auth",r,{headers:{"Content-Type":"application/json"}});return console.log("Product created successfully:",l.data),o(n=>({products:[...n.products,l.data],loading:!1})),l.data}catch(l){throw console.error("Error creating product:",l),o({error:l instanceof Error?l.message:"فشل في إنشاء المنتج",loading:!1}),l}},updateProduct:async(r,l)=>{try{o({loading:!0,error:null});const n=await I.put(`/api/products/${r}`,l);return o(a=>({products:a.products.map(h=>h.id===r?n.data:h),loading:!1})),n.data}catch(n){throw o({error:n instanceof Error?n.message:"Failed to update product",loading:!1}),n}},deleteProduct:async r=>{var l,n,a,h,i,j;try{o({loading:!0,error:null}),await I.delete(`/api/products/${r}`),o(g=>({products:g.products.filter(y=>y.id!==r),loading:!1}))}catch(g){console.error("Error deleting product:",g);let y="فشل في حذف المنتج";throw((l=g==null?void 0:g.response)==null?void 0:l.status)===400?y=((n=g.response.data)==null?void 0:n.detail)||"لا يمكن حذف هذا المنتج":((a=g==null?void 0:g.response)==null?void 0:a.status)===404?y="المنتج غير موجود":((h=g==null?void 0:g.response)==null?void 0:h.status)===403?y="ليس لديك صلاحية لحذف هذا المنتج":((i=g==null?void 0:g.response)==null?void 0:i.status)>=500?y="خطأ في الخادم. حاول مرة أخرى لاحقاً":((g==null?void 0:g.code)==="NETWORK_ERROR"||(j=g==null?void 0:g.message)!=null&&j.includes("Network Error"))&&(y="خطأ في الاتصال بالخادم. تحقق من اتصال الإنترنت"),o({error:y,loading:!1}),new Error(y)}},updateStock:async(r,l)=>{try{o({loading:!0,error:null});const n=await I.put(`/api/products/${r}/stock`,{quantity:l});return o(a=>({products:a.products.map(h=>h.id===r?n.data:h),loading:!1})),n.data}catch(n){throw o({error:n instanceof Error?n.message:"Failed to update stock",loading:!1}),n}},setFilters:r=>{const l={...r};l.search===""&&delete l.search,l.category===""&&delete l.category,o({filters:l}),console.log("Setting filters in store:",l),console.log("Filter details - search:",l.search||"none","category:",l.category||"none","lowStock:",l.lowStock||!1),o(n=>({...n,loading:!0})),console.log("Calling fetchProducts with filters:",l),setTimeout(()=>{b().fetchProducts(l).then(n=>{console.log("Products fetched with filters successfully:",n?n.length:0),n&&n.length>0?console.log("Sample of filtered products:",n.slice(0,Math.min(3,n.length)).map(a=>({id:a.id,name:a.name,category:a.category,quantity:a.quantity}))):console.log("No products returned after applying filters"),o(a=>({...a,loading:!1}))}).catch(n=>{console.error("Error fetching products with filters:",n),o(a=>({...a,loading:!1,error:"فشل في تطبيق الفلاتر"}))})},100)},setSelectedProduct:r=>{o({selectedProduct:r})},clearError:()=>{o({error:null})},nextPage:()=>{const{pagination:r,filters:l}=b();if(r.page<r.pages){const n=r.page+1;console.log(`Moving to next page: ${r.page} -> ${n}`);const a={...l,page:n};o({filters:a}),b().fetchProducts(a)}else console.log(`Already at last page (${r.page} of ${r.pages})`)},prevPage:()=>{const{pagination:r,filters:l}=b();if(r.page>1){const n=r.page-1;console.log(`Moving to previous page: ${r.page} -> ${n}`);const a={...l,page:n};o({filters:a}),b().fetchProducts(a)}else console.log(`Already at first page (${r.page} of ${r.pages})`)},setPage:r=>{const{filters:l,pagination:n}=b();if(r>=1&&r<=n.pages){console.log(`Setting page to ${r} (current: ${n.page}, total: ${n.pages})`);const a={...l,page:r};o({filters:a}),b().fetchProducts(a)}else console.log(`Invalid page number: ${r} (current: ${n.page}, total: ${n.pages})`)},setLimit:r=>{const{filters:l}=b();console.log(`Setting limit to ${r} and resetting to page 1`);const n={...l,limit:r,page:1};o({filters:n}),b().fetchProducts(n)}})),we=({product:o,onClose:b})=>{const{createProduct:r,updateProduct:l,categories:n,deleteCategory:a}=ie();se();const[h,i]=x.useState(!1),[j,g]=x.useState(null),[y,u]=x.useState(!1),[p,F]=x.useState(""),[q,E]=x.useState(null),[v,N]=x.useState(null),[R,G]=x.useState(!1),[f,w]=x.useState({}),[M,T]=x.useState(null),[D]=x.useState(!1),L=x.useRef(null),[d,k]=x.useState({name:"",barcode:"",description:"",price:"",cost_price:"",quantity:"0",min_quantity:"0",category:"",unit:"",is_active:!0}),O=()=>{const s=Date.now().toString(),c=Math.floor(Math.random()*1e3).toString().padStart(3,"0");return`${s}${c}`},K=async s=>{if(!s.trim()){N(null);return}G(!0),N(null);try{const c=await I.get(`/api/products/check-barcode/${s}`);c.data.exists?N(`هذا الباركود مستخدم بالفعل في المنتج "${c.data.product.name}". يرجى توليد باركود جديد أو استخدام باركود مختلف.`):(T("الباركود متاح ويمكن استخدامه"),setTimeout(()=>T(null),2e3))}catch(c){console.error("Error checking barcode:",c),N("حدث خطأ أثناء التحقق من الباركود. يرجى المحاولة مرة أخرى.")}finally{G(!1)}};x.useEffect(()=>{if(!o){const s=O();k(c=>({...c,barcode:s}))}},[o]),x.useEffect(()=>{o&&k({name:o.name,barcode:o.barcode||"",description:o.description||"",price:o.price.toString(),cost_price:o.cost_price.toString(),quantity:o.quantity.toString(),min_quantity:o.min_quantity.toString(),category:o.category||"",unit:o.unit,is_active:o.is_active})},[o]);const B=async s=>{var c,P,_,A;if(s)try{window.confirm(`هل أنت متأكد من حذف الفئة "${s}"؟`)&&(await a(s),d.category===s&&k(S=>({...S,category:""})))}catch(S){((c=S==null?void 0:S.response)==null?void 0:c.status)===400&&((A=(_=(P=S==null?void 0:S.response)==null?void 0:P.data)==null?void 0:_.detail)!=null&&A.includes("Cannot delete category that has products"))?g("لا يمكن حذف هذه الفئة لأنها تحتوي على منتجات. يرجى نقل المنتجات إلى فئة أخرى أو حذفها أولاً."):g("فشل في حذف الفئة. يرجى المحاولة مرة أخرى.")}},U=()=>{const s={};return d.name.trim()?d.name.trim().length<2&&(s.name="اسم المنتج يجب أن يكون أكثر من حرفين"):s.name="يرجى إدخال اسم المنتج",d.barcode.trim()?v&&(s.barcode=v):s.barcode="يرجى إدخال الباركود أو توليد باركود جديد",!d.price||d.price.trim()===""?s.price="يرجى إدخال سعر البيع":(isNaN(Number(d.price))||Number(d.price)<=0)&&(s.price="سعر البيع يجب أن يكون رقماً أكبر من الصفر"),!d.cost_price||d.cost_price.trim()===""?s.cost_price="يرجى إدخال تكلفة الشراء":(isNaN(Number(d.cost_price))||Number(d.cost_price)<0)&&(s.cost_price="تكلفة الشراء يجب أن تكون رقماً صحيحاً"),d.price&&d.cost_price&&Number(d.price)<Number(d.cost_price)&&(s.price="سعر البيع لا يمكن أن يكون أقل من تكلفة الشراء"),!d.category&&!y?s.category="يرجى اختيار فئة المنتج":y&&!p.trim()&&(s.category="يرجى إدخال اسم الفئة الجديدة"),d.unit||(s.unit="يرجى اختيار وحدة القياس"),w(s),Object.keys(s).length>0?(g("يرجى تصحيح البيانات المطلوبة أدناه لإتمام عملية الحفظ"),!1):!0},H=async s=>{var c,P,_,A,S,V;if(s.preventDefault(),g(null),!!U())try{i(!0);let $=d.category;y&&p.trim()&&($=p.trim());const t={...d,category:$,price:Number(d.price),cost_price:Number(d.cost_price),quantity:Number(d.quantity),min_quantity:Number(d.min_quantity)};if(o)await l(o.id,t);else try{await r(t)}catch(m){console.error("Error creating product:",m),((c=m==null?void 0:m.response)==null?void 0:c.status)===400&&((A=(_=(P=m==null?void 0:m.response)==null?void 0:P.data)==null?void 0:_.detail)!=null&&A.includes("barcode already exists"))?g("الباركود المدخل موجود بالفعل. يرجى توليد باركود جديد أو استخدام باركود مختلف."):g("حدث خطأ أثناء حفظ المنتج. يرجى التحقق من البيانات والمحاولة مرة أخرى."),i(!1);return}y&&(F(""),u(!1),E(null)),b()}catch($){console.error("Error in form submission:",$),((S=$==null?void 0:$.response)==null?void 0:S.status)===400?g("البيانات المدخلة غير صحيحة. يرجى مراجعة جميع الحقول والمحاولة مرة أخرى."):((V=$==null?void 0:$.response)==null?void 0:V.status)===500?g("حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً."):g("حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.")}finally{i(!1)}};return e.jsxs("form",{onSubmit:H,className:"space-y-6",children:[j&&e.jsx("div",{className:"bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-2",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(z,{className:"h-5 w-5 text-red-500 mt-0.5"})}),e.jsxs("div",{className:"mr-3",children:[e.jsx("h3",{className:"text-sm font-medium text-red-800 dark:text-red-300",children:"تنبيه: يوجد أخطاء في النموذج"}),e.jsx("p",{className:"mt-1 text-sm text-red-700 dark:text-red-400",children:j})]})]})}),M&&e.jsx("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-2",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(xe,{className:"h-5 w-5 text-green-500 mt-0.5"})}),e.jsxs("div",{className:"mr-3",children:[e.jsx("h3",{className:"text-sm font-medium text-green-800 dark:text-green-300",children:"تم بنجاح"}),e.jsx("p",{className:"mt-1 text-sm text-green-700 dark:text-green-400",children:M})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsx(re,{label:"اسم المنتج",name:"name",value:d.name,onChange:s=>{k(c=>({...c,name:s})),f.name&&w(c=>({...c,name:""}))},placeholder:"أدخل اسم المنتج",required:!0,error:f.name,icon:e.jsx(Z,{}),maxLength:100}),e.jsx(fe,{label:"الباركود",name:"barcode",value:d.barcode,onChange:s=>{k(c=>({...c,barcode:s})),f.barcode&&w(c=>({...c,barcode:""})),N(null),s.trim()&&setTimeout(()=>{K(s)},500)},onGenerate:O,placeholder:"أدخل الباركود أو اضغط توليد",required:!0,error:f.barcode||v||void 0,success:!v&&d.barcode&&D?"الباركود متاح للاستخدام":void 0,validating:R,showGenerateButton:!0}),e.jsx("div",{className:"md:col-span-2",children:e.jsx(ke,{label:"وصف المنتج",name:"description",value:d.description,onChange:s=>k(c=>({...c,description:s})),placeholder:"أدخل وصف المنتج (اختياري)",rows:3,maxLength:500})}),e.jsx(W,{label:"سعر البيع",name:"price",value:d.price,onChange:s=>{k(c=>({...c,price:s})),f.price&&w(c=>({...c,price:""}))},placeholder:"0.00",required:!0,error:f.price,currency:"د.ل",min:0,step:"0.01",precision:2,icon:e.jsx(J,{})}),e.jsx(W,{label:"تكلفة الشراء",name:"cost_price",value:d.cost_price,onChange:s=>{k(c=>({...c,cost_price:s})),f.cost_price&&w(c=>({...c,cost_price:""}))},placeholder:"0.00",required:!0,error:f.cost_price,currency:"د.ل",min:0,step:"0.01",precision:2,icon:e.jsx(J,{})}),e.jsx(W,{label:"المخزون الحالي",name:"quantity",value:d.quantity,onChange:s=>k(c=>({...c,quantity:s})),placeholder:"0",min:0,step:"1",precision:0,icon:e.jsx(Z,{}),showControls:!0}),e.jsx(W,{label:"الحد الأدنى للمخزون",name:"min_quantity",value:d.min_quantity,onChange:s=>k(c=>({...c,min_quantity:s})),placeholder:"0",min:0,step:"1",precision:0,icon:e.jsx(z,{}),showControls:!0}),e.jsx("div",{children:y?e.jsxs("div",{className:"space-y-3",children:[e.jsx(re,{label:"الفئة الجديدة",name:"newCategory",value:p,onChange:s=>{F(s),f.category&&w(c=>({...c,category:""}))},placeholder:"أدخل اسم الفئة الجديدة",required:!0,error:f.category||q||void 0,icon:e.jsx(Q,{}),autoFocus:!0}),e.jsx("div",{className:"flex gap-2",children:e.jsx("button",{type:"button",onClick:()=>{u(!1),F(""),E(null)},className:"btn-secondary px-4 py-2 text-sm",children:"إلغاء"})}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg",children:"💡 سيتم إضافة الفئة الجديدة تلقائيًا عند حفظ المنتج"})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(ae,{label:"الفئة",name:"category",value:d.category,onChange:s=>{if(s==="new"){u(!0),setTimeout(()=>{L.current&&L.current.focus()},0);return}k(c=>({...c,category:s})),f.category&&w(c=>({...c,category:""}))},options:[...n.map(s=>({value:s,label:s})),{value:"new",label:"+ إضافة فئة جديدة"}],placeholder:"اختر الفئة",required:!0,error:f.category,icon:e.jsx(Q,{})}),d.category&&d.category!=="new"&&e.jsx("div",{className:"flex justify-end",children:e.jsxs("button",{type:"button",onClick:()=>B(d.category),className:"inline-flex items-center gap-2 px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-700 dark:hover:text-red-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",title:"حذف الفئة المحددة",children:[e.jsx(oe,{className:"h-3 w-3"}),e.jsx("span",{children:"حذف الفئة"})]})})]}),n.length>0&&e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 bg-orange-50 dark:bg-orange-900/20 p-2 rounded-lg",children:"⚠️ يمكنك حذف الفئة فقط إذا لم تكن تحتوي على منتجات"})]})}),e.jsx(ae,{label:"وحدة القياس",name:"unit",value:d.unit,onChange:s=>{k(c=>({...c,unit:s})),f.unit&&w(c=>({...c,unit:""}))},options:[{value:"piece",label:"قطعة"},{value:"kg",label:"كيلوجرام"},{value:"g",label:"جرام"},{value:"l",label:"لتر"},{value:"ml",label:"مليلتر"},{value:"box",label:"صندوق"},{value:"pack",label:"عبوة"}],placeholder:"اختر وحدة القياس",required:!0,error:f.unit,icon:e.jsx(pe,{})}),e.jsx("div",{className:"md:col-span-2",children:e.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700",children:[e.jsx(Y,{id:"is_active",checked:d.is_active,onChange:s=>k(c=>({...c,is_active:s})),label:"المنتج نشط ومتاح للبيع",className:"justify-between"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:"💡 المنتجات النشطة فقط تظهر في نقطة البيع ويمكن بيعها"})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-3 justify-center pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsx("button",{type:"button",onClick:b,disabled:h,className:"btn-secondary flex items-center justify-center min-w-[120px]",children:e.jsx("span",{children:"إلغاء"})}),e.jsx("button",{type:"submit",disabled:h,className:"btn-primary flex items-center justify-center min-w-[140px]",children:h?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),e.jsx("span",{children:"جاري الحفظ..."})]}):e.jsxs(e.Fragment,{children:[o?e.jsx(ne,{className:"ml-2"}):e.jsx(le,{className:"ml-2"}),e.jsx("span",{children:o?"تحديث المنتج":"إضافة المنتج"})]})})]})]})},Ee=({isNew:o=!1})=>{const{products:b,categories:r,loading:l,error:n,filters:a,selectedProduct:h,pagination:i,fetchProducts:j,fetchCategories:g,deleteProduct:y,setFilters:u,setSelectedProduct:p,nextPage:F,prevPage:q,setPage:E,setLimit:v}=ie();se();const N=ge(),R=me(),[G,f]=x.useState(o),[w,M]=x.useState(a.search||""),[T,D]=x.useState(a.category||""),[L,d]=x.useState(a.lowStock||!1),[k,O]=x.useState(a.zeroStock||!1),[K,B]=x.useState(a.isActive),[U,H]=x.useState(!1),[s,c]=x.useState(a.limit||10),[P,_]=x.useState({isOpen:!1,productId:null,productName:"",isLoading:!1}),[A,S]=x.useState({isOpen:!1,title:"",message:""});x.useEffect(()=>{console.log("Component mounted, applying filters:",a),console.log("Filter details on mount - search:",a.search||"none","category:",a.category||"none","lowStock:",a.lowStock||!1);const t=R.state;if(t){let m={...a},C=!1;t.zeroStock&&(console.log("Applying zero stock filter from navigation state"),O(!0),m={...m,zeroStock:!0,page:1,limit:10},C=!0),t.search&&(console.log("Applying search filter from navigation state:",t.search),M(t.search),m={...m,search:t.search,page:1},C=!0),C?u(m):j(a)}else j(a);return g(),o&&(p(null),f(!0)),()=>{}},[o,R.state]),x.useEffect(()=>{console.log("Filters changed in store:",a),M(a.search||""),D(a.category||""),d(a.lowStock||!1),O(a.zeroStock||!1),B(a.isActive),c(a.limit||10)},[a]);const V=(t,m)=>{_({isOpen:!0,productId:t,productName:m,isLoading:!1})},$=async()=>{if(P.productId){_(t=>({...t,isLoading:!0}));try{await y(P.productId),_({isOpen:!1,productId:null,productName:"",isLoading:!1}),S({isOpen:!0,title:"تم الحذف بنجاح",message:`تم حذف المنتج "${P.productName}" بنجاح`}),j(a)}catch(t){_({isOpen:!1,productId:null,productName:"",isLoading:!1}),console.error("Error deleting product:",t)}}};return e.jsxs("div",{className:"container mx-auto px-4 py-6 bg-gray-50 dark:bg-gray-900 min-h-screen",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx("button",{onClick:()=>N("/"),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2.5 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm flex-shrink-0",title:"العودة للرئيسية",children:e.jsx(ue,{className:"text-sm"})}),e.jsxs("div",{className:"mr-3 sm:mr-4 min-w-0 flex-1",children:[e.jsxs("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center",children:[e.jsx(Z,{className:"ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:"إدارة المنتجات"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block",children:"إدارة وتتبع المخزون والمنتجات"})]})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap",children:[e.jsx("button",{onClick:()=>j(),className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"تحديث",children:e.jsx(X,{className:`text-sm ${l?"animate-spin":""}`})}),e.jsxs("button",{onClick:()=>{p(null),f(!0)},className:"bg-primary-600 text-white rounded-lg py-2.5 px-3 sm:px-4 lg:px-5 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg font-medium",children:[e.jsx(le,{className:"ml-2 text-sm"}),e.jsx("span",{className:"hidden sm:inline lg:inline",children:"إضافة منتج جديد"}),e.jsx("span",{className:"sm:hidden lg:hidden",children:"إضافة"})]})]})]})})}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(Z,{className:"text-primary-600 dark:text-primary-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-primary-700 dark:text-primary-300 mb-1",children:"إجمالي المنتجات"}),e.jsx("div",{className:"text-xl font-bold text-primary-600 dark:text-primary-400",children:b.length})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30",children:[e.jsx("div",{className:"bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(Z,{className:"text-success-600 dark:text-success-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-success-700 dark:text-success-300 mb-1",children:"المنتجات النشطة"}),e.jsx("div",{className:"text-xl font-bold text-success-600 dark:text-success-400",children:b.filter(t=>t.is_active).length})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-warning-50 dark:bg-warning-900/20 rounded-lg border border-warning-100 dark:border-warning-800/30",children:[e.jsx("div",{className:"bg-warning-100 dark:bg-warning-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(z,{className:"text-warning-600 dark:text-warning-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-warning-700 dark:text-warning-300 mb-1",children:"مخزون منخفض"}),e.jsx("div",{className:"text-xl font-bold text-warning-600 dark:text-warning-400",children:b.filter(t=>t.quantity<=t.min_quantity&&t.quantity>0).length})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-danger-50 dark:bg-danger-900/20 rounded-lg border border-danger-100 dark:border-danger-800/30",children:[e.jsx("div",{className:"bg-danger-100 dark:bg-danger-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(z,{className:"text-danger-600 dark:text-danger-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-danger-700 dark:text-danger-300 mb-1",children:"نفدت الكمية"}),e.jsx("div",{className:"text-xl font-bold text-danger-600 dark:text-danger-400",children:b.filter(t=>t.quantity===0).length})]})]})]})}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ee,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"}),e.jsx("input",{type:"text",placeholder:"البحث عن منتج...",value:w,onChange:t=>M(t.target.value),onKeyUp:t=>{if(t.key==="Enter"){console.log("Applying search filter on Enter key:",w);const m={search:w,category:T,lowStock:L};console.log("New filters to apply on Enter:",m),u(m)}},className:"w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"})]})}),e.jsxs("div",{className:"flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>H(!U),className:`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${U?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(he,{className:"ml-2"}),"فلاتر"]}),e.jsxs("button",{onClick:()=>j(a),className:"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center",title:"تحديث",children:[e.jsx(X,{className:`ml-2 ${l?"animate-spin":""}`}),"تحديث"]}),e.jsxs("button",{onClick:()=>{console.log("Applying search filter on button click:",w),u({search:w,category:T,lowStock:L})},className:"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 hover:bg-primary-200 dark:hover:bg-primary-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center",children:[e.jsx(ee,{className:"ml-2"}),"بحث"]})]})]}),U&&e.jsx("div",{className:"mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4",children:[e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(te,{label:"الفئة",name:"category",value:T,onChange:t=>{D(t)},options:[{value:"",label:"جميع الفئات"},...r.map(t=>({value:t,label:t}))]})}),e.jsx("div",{className:"sm:col-span-1 lg:col-span-1",children:e.jsx(te,{label:"عدد العناصر",name:"itemsPerPage",value:s.toString(),onChange:t=>{const m=parseInt(t,10);c(m),v(m)},options:[{value:"10",label:"10"},{value:"20",label:"20"},{value:"30",label:"30"},{value:"50",label:"50"}]})}),e.jsxs("div",{className:"sm:col-span-1 lg:col-span-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"مخزون منخفض"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center",children:e.jsx(Y,{id:"lowStock",checked:L,onChange:t=>d(t),label:"منخفض فقط"})})]}),e.jsxs("div",{className:"sm:col-span-1 lg:col-span-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"نفدت الكمية"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center",children:e.jsx(Y,{id:"zeroStock",checked:k,onChange:t=>O(t),label:"صفر فقط"})})]}),e.jsxs("div",{className:"sm:col-span-1 lg:col-span-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"المنتجات النشطة"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center",children:e.jsx(Y,{id:"activeStatus",checked:K===!0,onChange:t=>B(t?!0:void 0),label:"النشطة فقط"})})]}),e.jsxs("div",{className:"sm:col-span-2 lg:col-span-1 flex flex-col gap-2",children:[e.jsx("button",{type:"button",onClick:()=>{M(""),D(""),d(!1),O(!1),B(void 0),c(10),u({limit:10}),console.log("Filters reset to empty")},className:"bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm",children:"إعادة تعيين"}),e.jsx("button",{type:"button",onClick:()=>{console.log("Applying all filters");const t={search:w,category:T,lowStock:L,zeroStock:k,isActive:K,limit:s,page:1};console.log("New filters to apply:",t),u(t),console.log("Current pagination state:",i)},className:"bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-lg transition-colors h-[42px] flex items-center justify-center text-sm",children:"تطبيق الفلاتر"})]})]})})]}),n&&e.jsx("div",{className:"bg-danger-100 dark:bg-danger-900/30 border border-danger-200 dark:border-danger-800 text-danger-700 dark:text-danger-300 px-4 py-3 rounded-xl mb-6",children:n}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden",children:[e.jsx("div",{className:"overflow-x-auto custom-scrollbar",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-700",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"اسم المنتج"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الفئة"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"السعر"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المخزون"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الحالة"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الإجراءات"})]})}),e.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:l?e.jsx("tr",{children:e.jsx("td",{colSpan:6,className:"px-6 py-4 text-center text-gray-500 dark:text-gray-400",children:"جاري التحميل..."})}):b.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:6,className:"px-6 py-4 text-center text-gray-500 dark:text-gray-400",children:"لم يتم العثور على منتجات"})}):b.map(t=>e.jsxs("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:t.name}),e.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.barcode||"بدون باركود"})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:t.category||"-"}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsxs("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:[t.price.toFixed(2)," د.ل"]}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["هامش الربح: ",t.profit_margin.toFixed(1),"%"]})]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsxs("div",{className:`text-sm ${t.quantity===0?"text-danger-600 dark:text-danger-400 font-medium":t.quantity<=t.min_quantity?"text-warning-600 dark:text-warning-400 font-medium":"text-gray-900 dark:text-gray-100"}`,children:[t.quantity," ",t.unit]}),t.quantity===0?e.jsxs("div",{className:"text-xs text-danger-500 dark:text-danger-400 flex items-center",children:[e.jsx(z,{className:"mr-1"})," نفدت الكمية"]}):t.quantity<=t.min_quantity?e.jsxs("div",{className:"text-xs text-warning-500 dark:text-warning-400 flex items-center",children:[e.jsx(z,{className:"mr-1"})," مخزون منخفض"]}):null]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${t.is_active?"bg-success-100 dark:bg-success-900/30 text-success-800 dark:text-success-300":"bg-danger-100 dark:bg-danger-900/30 text-danger-800 dark:text-danger-300"}`,children:t.is_active?"نشط":"غير نشط"})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-left text-sm font-medium",children:[e.jsx("button",{onClick:()=>{p(t),f(!0)},className:"text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 ml-3",title:"تعديل",children:e.jsx(ne,{className:"inline text-lg"})}),e.jsx("button",{onClick:()=>V(t.id,t.name),className:"text-danger-600 dark:text-danger-400 hover:text-danger-800 dark:hover:text-danger-300",title:"حذف",children:e.jsx(oe,{className:"inline text-lg"})})]})]},t.id))})]})}),!l&&b.length>0&&e.jsxs("div",{className:"px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex-1 flex justify-between items-center sm:hidden",children:[e.jsx("button",{onClick:()=>{console.log(`Moving to previous page from ${i.page}`),q()},disabled:i.page<=1,className:`relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${i.page<=1?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:"السابق"}),e.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.jsx("span",{className:"font-medium",children:i.page})," / ",e.jsx("span",{className:"font-medium",children:i.pages})]}),e.jsx("button",{onClick:()=>{console.log(`Moving to next page from ${i.page}`),F()},disabled:i.page>=i.pages,className:`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md ${i.page>=i.pages?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:"التالي"})]}),e.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:[e.jsx("span",{className:"font-medium",children:i.page})," / ",e.jsx("span",{className:"font-medium",children:i.pages})," ",e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["(الإجمالي: ",i.total,")"]})]})}),e.jsx("div",{children:e.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px rtl:space-x-reverse","aria-label":"Pagination",children:[e.jsxs("button",{onClick:()=>{console.log(`Moving to previous page from ${i.page}`),q()},disabled:i.page<=1,className:`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${i.page<=1?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"sr-only",children:"السابق"}),e.jsx(ye,{className:"h-5 w-5"})]}),e.jsxs("div",{className:"hidden text-xs text-gray-500 dark:text-gray-400 px-2 py-1 border-r border-gray-300 dark:border-gray-600",children:["صفحة ",i.page," من ",i.pages]}),Array.from({length:Math.min(5,i.pages)},(t,m)=>{let C;return i.pages<=5||i.page<=3?C=m+1:i.page>=i.pages-2?C=i.pages-4+m:C=i.page-2+m,e.jsx("button",{onClick:()=>{console.log(`Setting page to ${C}`),E(C)},className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${i.page===C?"z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:C},C)}),e.jsxs("button",{onClick:()=>{console.log(`Moving to next page from ${i.page}`),F()},disabled:i.page>=i.pages,className:`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 text-sm font-medium ${i.page>=i.pages?"text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 cursor-not-allowed":"text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:[e.jsx("span",{className:"sr-only",children:"التالي"}),e.jsx(be,{className:"h-5 w-5"})]})]})})]})]})]}),e.jsx(je,{isOpen:G,onClose:()=>{p(null),f(!1)},title:h?"تعديل المنتج":"إضافة منتج جديد",size:"lg",children:e.jsx(we,{product:h||void 0,onClose:()=>{p(null),f(!1)}})}),e.jsx(ve,{isOpen:P.isOpen,onClose:()=>_({isOpen:!1,productId:null,productName:"",isLoading:!1}),onConfirm:$,title:"تأكيد حذف المنتج",message:"هل أنت متأكد من رغبتك في حذف هذا المنتج؟",itemName:P.productName,isLoading:P.isLoading}),e.jsx(Ne,{isOpen:A.isOpen,onClose:()=>S({isOpen:!1,title:"",message:""}),title:A.title,message:A.message})]})};export{Ee as default};
