import{r as I,j as a,f as Oe,a as Ve,b as z}from"./index-4-jyR6ws.js";import{w as Ce,an as He,am as Ke,ao as xe,I as Je,D as Ee,O as be,e as re,ap as Ye}from"./index-yc4-8FIA.js";import{M as qe}from"./Modal-Df3FNVJO.js";var H={},Qe=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},_e={},B={};let he;const Ge=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];B.getSymbolSize=function(e){if(!e)throw new Error('"version" cannot be null or undefined');if(e<1||e>40)throw new Error('"version" should be in range from 1 to 40');return e*4+17};B.getSymbolTotalCodewords=function(e){return Ge[e]};B.getBCHDigit=function(n){let e=0;for(;n!==0;)e++,n>>>=1;return e};B.setToSJISFunction=function(e){if(typeof e!="function")throw new Error('"toSJISFunc" is not a valid function.');he=e};B.isKanjiModeEnabled=function(){return typeof he<"u"};B.toSJIS=function(e){return he(e)};var Z={};(function(n){n.L={bit:1},n.M={bit:0},n.Q={bit:3},n.H={bit:2};function e(t){if(typeof t!="string")throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return n.L;case"m":case"medium":return n.M;case"q":case"quartile":return n.Q;case"h":case"high":return n.H;default:throw new Error("Unknown EC Level: "+t)}}n.isValid=function(o){return o&&typeof o.bit<"u"&&o.bit>=0&&o.bit<4},n.from=function(o,r){if(n.isValid(o))return o;try{return e(o)}catch{return r}}})(Z);function ve(){this.buffer=[],this.length=0}ve.prototype={get:function(n){const e=Math.floor(n/8);return(this.buffer[e]>>>7-n%8&1)===1},put:function(n,e){for(let t=0;t<e;t++)this.putBit((n>>>e-t-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(n){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),n&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var We=ve;function K(n){if(!n||n<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=n,this.data=new Uint8Array(n*n),this.reservedBit=new Uint8Array(n*n)}K.prototype.set=function(n,e,t,o){const r=n*this.size+e;this.data[r]=t,o&&(this.reservedBit[r]=!0)};K.prototype.get=function(n,e){return this.data[n*this.size+e]};K.prototype.xor=function(n,e,t){this.data[n*this.size+e]^=t};K.prototype.isReserved=function(n,e){return this.reservedBit[n*this.size+e]};var Ze=K,Ae={};(function(n){const e=B.getSymbolSize;n.getRowColCoords=function(o){if(o===1)return[];const r=Math.floor(o/7)+2,s=e(o),i=s===145?26:Math.ceil((s-13)/(2*r-2))*2,l=[s-7];for(let c=1;c<r-1;c++)l[c]=l[c-1]-i;return l.push(6),l.reverse()},n.getPositions=function(o){const r=[],s=n.getRowColCoords(o),i=s.length;for(let l=0;l<i;l++)for(let c=0;c<i;c++)l===0&&c===0||l===0&&c===i-1||l===i-1&&c===0||r.push([s[l],s[c]]);return r}})(Ae);var Se={};const Xe=B.getSymbolSize,we=7;Se.getPositions=function(e){const t=Xe(e);return[[0,0],[t-we,0],[0,t-we]]};var Be={};(function(n){n.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e={N1:3,N2:3,N3:40,N4:10};n.isValid=function(r){return r!=null&&r!==""&&!isNaN(r)&&r>=0&&r<=7},n.from=function(r){return n.isValid(r)?parseInt(r,10):void 0},n.getPenaltyN1=function(r){const s=r.size;let i=0,l=0,c=0,g=null,h=null;for(let C=0;C<s;C++){l=c=0,g=h=null;for(let u=0;u<s;u++){let m=r.get(C,u);m===g?l++:(l>=5&&(i+=e.N1+(l-5)),g=m,l=1),m=r.get(u,C),m===h?c++:(c>=5&&(i+=e.N1+(c-5)),h=m,c=1)}l>=5&&(i+=e.N1+(l-5)),c>=5&&(i+=e.N1+(c-5))}return i},n.getPenaltyN2=function(r){const s=r.size;let i=0;for(let l=0;l<s-1;l++)for(let c=0;c<s-1;c++){const g=r.get(l,c)+r.get(l,c+1)+r.get(l+1,c)+r.get(l+1,c+1);(g===4||g===0)&&i++}return i*e.N2},n.getPenaltyN3=function(r){const s=r.size;let i=0,l=0,c=0;for(let g=0;g<s;g++){l=c=0;for(let h=0;h<s;h++)l=l<<1&2047|r.get(g,h),h>=10&&(l===1488||l===93)&&i++,c=c<<1&2047|r.get(h,g),h>=10&&(c===1488||c===93)&&i++}return i*e.N3},n.getPenaltyN4=function(r){let s=0;const i=r.data.length;for(let c=0;c<i;c++)s+=r.data[c];return Math.abs(Math.ceil(s*100/i/5)-10)*e.N4};function t(o,r,s){switch(o){case n.Patterns.PATTERN000:return(r+s)%2===0;case n.Patterns.PATTERN001:return r%2===0;case n.Patterns.PATTERN010:return s%3===0;case n.Patterns.PATTERN011:return(r+s)%3===0;case n.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(s/3))%2===0;case n.Patterns.PATTERN101:return r*s%2+r*s%3===0;case n.Patterns.PATTERN110:return(r*s%2+r*s%3)%2===0;case n.Patterns.PATTERN111:return(r*s%3+(r+s)%2)%2===0;default:throw new Error("bad maskPattern:"+o)}}n.applyMask=function(r,s){const i=s.size;for(let l=0;l<i;l++)for(let c=0;c<i;c++)s.isReserved(c,l)||s.xor(c,l,t(r,c,l))},n.getBestMask=function(r,s){const i=Object.keys(n.Patterns).length;let l=0,c=1/0;for(let g=0;g<i;g++){s(g),n.applyMask(g,r);const h=n.getPenaltyN1(r)+n.getPenaltyN2(r)+n.getPenaltyN3(r)+n.getPenaltyN4(r);n.applyMask(g,r),h<c&&(c=h,l=g)}return l}})(Be);var X={};const R=Z,q=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],Q=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];X.getBlocksCount=function(e,t){switch(t){case R.L:return q[(e-1)*4+0];case R.M:return q[(e-1)*4+1];case R.Q:return q[(e-1)*4+2];case R.H:return q[(e-1)*4+3];default:return}};X.getTotalCodewordsCount=function(e,t){switch(t){case R.L:return Q[(e-1)*4+0];case R.M:return Q[(e-1)*4+1];case R.Q:return Q[(e-1)*4+2];case R.H:return Q[(e-1)*4+3];default:return}};var Te={},ee={};const O=new Uint8Array(512),G=new Uint8Array(256);(function(){let e=1;for(let t=0;t<255;t++)O[t]=e,G[e]=t,e<<=1,e&256&&(e^=285);for(let t=255;t<512;t++)O[t]=O[t-255]})();ee.log=function(e){if(e<1)throw new Error("log("+e+")");return G[e]};ee.exp=function(e){return O[e]};ee.mul=function(e,t){return e===0||t===0?0:O[G[e]+G[t]]};(function(n){const e=ee;n.mul=function(o,r){const s=new Uint8Array(o.length+r.length-1);for(let i=0;i<o.length;i++)for(let l=0;l<r.length;l++)s[i+l]^=e.mul(o[i],r[l]);return s},n.mod=function(o,r){let s=new Uint8Array(o);for(;s.length-r.length>=0;){const i=s[0];for(let c=0;c<r.length;c++)s[c]^=e.mul(r[c],i);let l=0;for(;l<s.length&&s[l]===0;)l++;s=s.slice(l)}return s},n.generateECPolynomial=function(o){let r=new Uint8Array([1]);for(let s=0;s<o;s++)r=n.mul(r,new Uint8Array([1,e.exp(s)]));return r}})(Te);const ke=Te;function me(n){this.genPoly=void 0,this.degree=n,this.degree&&this.initialize(this.degree)}me.prototype.initialize=function(e){this.degree=e,this.genPoly=ke.generateECPolynomial(this.degree)};me.prototype.encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");const t=new Uint8Array(e.length+this.degree);t.set(e);const o=ke.mod(t,this.genPoly),r=this.degree-o.length;if(r>0){const s=new Uint8Array(this.degree);return s.set(o,r),s}return o};var et=me,Ie={},F={},ge={};ge.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40};var M={};const Me="[0-9]+",tt="[A-Z $%*+\\-./:]+";let V="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";V=V.replace(/u/g,"\\u");const rt="(?:(?![A-Z0-9 $%*+\\-./:]|"+V+`)(?:.|[\r
]))+`;M.KANJI=new RegExp(V,"g");M.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");M.BYTE=new RegExp(rt,"g");M.NUMERIC=new RegExp(Me,"g");M.ALPHANUMERIC=new RegExp(tt,"g");const nt=new RegExp("^"+V+"$"),st=new RegExp("^"+Me+"$"),ot=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");M.testKanji=function(e){return nt.test(e)};M.testNumeric=function(e){return st.test(e)};M.testAlphanumeric=function(e){return ot.test(e)};(function(n){const e=ge,t=M;n.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},n.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},n.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},n.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},n.MIXED={bit:-1},n.getCharCountIndicator=function(s,i){if(!s.ccBits)throw new Error("Invalid mode: "+s);if(!e.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?s.ccBits[0]:i<27?s.ccBits[1]:s.ccBits[2]},n.getBestModeForData=function(s){return t.testNumeric(s)?n.NUMERIC:t.testAlphanumeric(s)?n.ALPHANUMERIC:t.testKanji(s)?n.KANJI:n.BYTE},n.toString=function(s){if(s&&s.id)return s.id;throw new Error("Invalid mode")},n.isValid=function(s){return s&&s.bit&&s.ccBits};function o(r){if(typeof r!="string")throw new Error("Param is not a string");switch(r.toLowerCase()){case"numeric":return n.NUMERIC;case"alphanumeric":return n.ALPHANUMERIC;case"kanji":return n.KANJI;case"byte":return n.BYTE;default:throw new Error("Unknown mode: "+r)}}n.from=function(s,i){if(n.isValid(s))return s;try{return o(s)}catch{return i}}})(F);(function(n){const e=B,t=X,o=Z,r=F,s=ge,i=7973,l=e.getBCHDigit(i);function c(u,m,b){for(let w=1;w<=40;w++)if(m<=n.getCapacity(w,b,u))return w}function g(u,m){return r.getCharCountIndicator(u,m)+4}function h(u,m){let b=0;return u.forEach(function(w){const v=g(w.mode,m);b+=v+w.getBitsLength()}),b}function C(u,m){for(let b=1;b<=40;b++)if(h(u,b)<=n.getCapacity(b,m,r.MIXED))return b}n.from=function(m,b){return s.isValid(m)?parseInt(m,10):b},n.getCapacity=function(m,b,w){if(!s.isValid(m))throw new Error("Invalid QR Code version");typeof w>"u"&&(w=r.BYTE);const v=e.getSymbolTotalCodewords(m),x=t.getTotalCodewordsCount(m,b),N=(v-x)*8;if(w===r.MIXED)return N;const y=N-g(w,m);switch(w){case r.NUMERIC:return Math.floor(y/10*3);case r.ALPHANUMERIC:return Math.floor(y/11*2);case r.KANJI:return Math.floor(y/13);case r.BYTE:default:return Math.floor(y/8)}},n.getBestVersionForData=function(m,b){let w;const v=o.from(b,o.M);if(Array.isArray(m)){if(m.length>1)return C(m,v);if(m.length===0)return 1;w=m[0]}else w=m;return c(w.mode,w.getLength(),v)},n.getEncodedBits=function(m){if(!s.isValid(m)||m<7)throw new Error("Invalid QR Code version");let b=m<<12;for(;e.getBCHDigit(b)-l>=0;)b^=i<<e.getBCHDigit(b)-l;return m<<12|b}})(Ie);var Pe={};const ce=B,Re=1335,at=21522,Ne=ce.getBCHDigit(Re);Pe.getEncodedBits=function(e,t){const o=e.bit<<3|t;let r=o<<10;for(;ce.getBCHDigit(r)-Ne>=0;)r^=Re<<ce.getBCHDigit(r)-Ne;return(o<<10|r)^at};var Fe={};const it=F;function L(n){this.mode=it.NUMERIC,this.data=n.toString()}L.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)};L.prototype.getLength=function(){return this.data.length};L.prototype.getBitsLength=function(){return L.getBitsLength(this.data.length)};L.prototype.write=function(e){let t,o,r;for(t=0;t+3<=this.data.length;t+=3)o=this.data.substr(t,3),r=parseInt(o,10),e.put(r,10);const s=this.data.length-t;s>0&&(o=this.data.substr(t),r=parseInt(o,10),e.put(r,s*3+1))};var ct=L;const lt=F,ne=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function D(n){this.mode=lt.ALPHANUMERIC,this.data=n}D.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)};D.prototype.getLength=function(){return this.data.length};D.prototype.getBitsLength=function(){return D.getBitsLength(this.data.length)};D.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let o=ne.indexOf(this.data[t])*45;o+=ne.indexOf(this.data[t+1]),e.put(o,11)}this.data.length%2&&e.put(ne.indexOf(this.data[t]),6)};var dt=D;const ut=F;function U(n){this.mode=ut.BYTE,typeof n=="string"?this.data=new TextEncoder().encode(n):this.data=new Uint8Array(n)}U.getBitsLength=function(e){return e*8};U.prototype.getLength=function(){return this.data.length};U.prototype.getBitsLength=function(){return U.getBitsLength(this.data.length)};U.prototype.write=function(n){for(let e=0,t=this.data.length;e<t;e++)n.put(this.data[e],8)};var ht=U;const mt=F,gt=B;function $(n){this.mode=mt.KANJI,this.data=n}$.getBitsLength=function(e){return e*13};$.prototype.getLength=function(){return this.data.length};$.prototype.getBitsLength=function(){return $.getBitsLength(this.data.length)};$.prototype.write=function(n){let e;for(e=0;e<this.data.length;e++){let t=gt.toSJIS(this.data[e]);if(t>=33088&&t<=40956)t-=33088;else if(t>=57408&&t<=60351)t-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);t=(t>>>8&255)*192+(t&255),n.put(t,13)}};var ft=$,Le={exports:{}};(function(n){var e={single_source_shortest_paths:function(t,o,r){var s={},i={};i[o]=0;var l=e.PriorityQueue.make();l.push(o,0);for(var c,g,h,C,u,m,b,w,v;!l.empty();){c=l.pop(),g=c.value,C=c.cost,u=t[g]||{};for(h in u)u.hasOwnProperty(h)&&(m=u[h],b=C+m,w=i[h],v=typeof i[h]>"u",(v||w>b)&&(i[h]=b,l.push(h,b),s[h]=g))}if(typeof r<"u"&&typeof i[r]>"u"){var x=["Could not find a path from ",o," to ",r,"."].join("");throw new Error(x)}return s},extract_shortest_path_from_predecessor_list:function(t,o){for(var r=[],s=o;s;)r.push(s),t[s],s=t[s];return r.reverse(),r},find_path:function(t,o,r){var s=e.single_source_shortest_paths(t,o,r);return e.extract_shortest_path_from_predecessor_list(s,r)},PriorityQueue:{make:function(t){var o=e.PriorityQueue,r={},s;t=t||{};for(s in o)o.hasOwnProperty(s)&&(r[s]=o[s]);return r.queue=[],r.sorter=t.sorter||o.default_sorter,r},default_sorter:function(t,o){return t.cost-o.cost},push:function(t,o){var r={value:t,cost:o};this.queue.push(r),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};n.exports=e})(Le);var pt=Le.exports;(function(n){const e=F,t=ct,o=dt,r=ht,s=ft,i=M,l=B,c=pt;function g(x){return unescape(encodeURIComponent(x)).length}function h(x,N,y){const d=[];let p;for(;(p=x.exec(y))!==null;)d.push({data:p[0],index:p.index,mode:N,length:p[0].length});return d}function C(x){const N=h(i.NUMERIC,e.NUMERIC,x),y=h(i.ALPHANUMERIC,e.ALPHANUMERIC,x);let d,p;return l.isKanjiModeEnabled()?(d=h(i.BYTE,e.BYTE,x),p=h(i.KANJI,e.KANJI,x)):(d=h(i.BYTE_KANJI,e.BYTE,x),p=[]),N.concat(y,d,p).sort(function(f,E){return f.index-E.index}).map(function(f){return{data:f.data,mode:f.mode,length:f.length}})}function u(x,N){switch(N){case e.NUMERIC:return t.getBitsLength(x);case e.ALPHANUMERIC:return o.getBitsLength(x);case e.KANJI:return s.getBitsLength(x);case e.BYTE:return r.getBitsLength(x)}}function m(x){return x.reduce(function(N,y){const d=N.length-1>=0?N[N.length-1]:null;return d&&d.mode===y.mode?(N[N.length-1].data+=y.data,N):(N.push(y),N)},[])}function b(x){const N=[];for(let y=0;y<x.length;y++){const d=x[y];switch(d.mode){case e.NUMERIC:N.push([d,{data:d.data,mode:e.ALPHANUMERIC,length:d.length},{data:d.data,mode:e.BYTE,length:d.length}]);break;case e.ALPHANUMERIC:N.push([d,{data:d.data,mode:e.BYTE,length:d.length}]);break;case e.KANJI:N.push([d,{data:d.data,mode:e.BYTE,length:g(d.data)}]);break;case e.BYTE:N.push([{data:d.data,mode:e.BYTE,length:g(d.data)}])}}return N}function w(x,N){const y={},d={start:{}};let p=["start"];for(let j=0;j<x.length;j++){const f=x[j],E=[];for(let A=0;A<f.length;A++){const _=f[A],P=""+j+A;E.push(P),y[P]={node:_,lastCount:0},d[P]={};for(let J=0;J<p.length;J++){const T=p[J];y[T]&&y[T].node.mode===_.mode?(d[T][P]=u(y[T].lastCount+_.length,_.mode)-u(y[T].lastCount,_.mode),y[T].lastCount+=_.length):(y[T]&&(y[T].lastCount=_.length),d[T][P]=u(_.length,_.mode)+4+e.getCharCountIndicator(_.mode,N))}}p=E}for(let j=0;j<p.length;j++)d[p[j]].end=0;return{map:d,table:y}}function v(x,N){let y;const d=e.getBestModeForData(x);if(y=e.from(N,d),y!==e.BYTE&&y.bit<d.bit)throw new Error('"'+x+'" cannot be encoded with mode '+e.toString(y)+`.
 Suggested mode is: `+e.toString(d));switch(y===e.KANJI&&!l.isKanjiModeEnabled()&&(y=e.BYTE),y){case e.NUMERIC:return new t(x);case e.ALPHANUMERIC:return new o(x);case e.KANJI:return new s(x);case e.BYTE:return new r(x)}}n.fromArray=function(N){return N.reduce(function(y,d){return typeof d=="string"?y.push(v(d,null)):d.data&&y.push(v(d.data,d.mode)),y},[])},n.fromString=function(N,y){const d=C(N,l.isKanjiModeEnabled()),p=b(d),j=w(p,y),f=c.find_path(j.map,"start","end"),E=[];for(let A=1;A<f.length-1;A++)E.push(j.table[f[A]].node);return n.fromArray(m(E))},n.rawSplit=function(N){return n.fromArray(C(N,l.isKanjiModeEnabled()))}})(Fe);const te=B,se=Z,yt=We,xt=Ze,bt=Ae,wt=Se,le=Be,de=X,Nt=et,W=Ie,jt=Pe,Ct=F,oe=Fe;function Et(n,e){const t=n.size,o=wt.getPositions(e);for(let r=0;r<o.length;r++){const s=o[r][0],i=o[r][1];for(let l=-1;l<=7;l++)if(!(s+l<=-1||t<=s+l))for(let c=-1;c<=7;c++)i+c<=-1||t<=i+c||(l>=0&&l<=6&&(c===0||c===6)||c>=0&&c<=6&&(l===0||l===6)||l>=2&&l<=4&&c>=2&&c<=4?n.set(s+l,i+c,!0,!0):n.set(s+l,i+c,!1,!0))}}function _t(n){const e=n.size;for(let t=8;t<e-8;t++){const o=t%2===0;n.set(t,6,o,!0),n.set(6,t,o,!0)}}function vt(n,e){const t=bt.getPositions(e);for(let o=0;o<t.length;o++){const r=t[o][0],s=t[o][1];for(let i=-2;i<=2;i++)for(let l=-2;l<=2;l++)i===-2||i===2||l===-2||l===2||i===0&&l===0?n.set(r+i,s+l,!0,!0):n.set(r+i,s+l,!1,!0)}}function At(n,e){const t=n.size,o=W.getEncodedBits(e);let r,s,i;for(let l=0;l<18;l++)r=Math.floor(l/3),s=l%3+t-8-3,i=(o>>l&1)===1,n.set(r,s,i,!0),n.set(s,r,i,!0)}function ae(n,e,t){const o=n.size,r=jt.getEncodedBits(e,t);let s,i;for(s=0;s<15;s++)i=(r>>s&1)===1,s<6?n.set(s,8,i,!0):s<8?n.set(s+1,8,i,!0):n.set(o-15+s,8,i,!0),s<8?n.set(8,o-s-1,i,!0):s<9?n.set(8,15-s-1+1,i,!0):n.set(8,15-s-1,i,!0);n.set(o-8,8,1,!0)}function St(n,e){const t=n.size;let o=-1,r=t-1,s=7,i=0;for(let l=t-1;l>0;l-=2)for(l===6&&l--;;){for(let c=0;c<2;c++)if(!n.isReserved(r,l-c)){let g=!1;i<e.length&&(g=(e[i]>>>s&1)===1),n.set(r,l-c,g),s--,s===-1&&(i++,s=7)}if(r+=o,r<0||t<=r){r-=o,o=-o;break}}}function Bt(n,e,t){const o=new yt;t.forEach(function(c){o.put(c.mode.bit,4),o.put(c.getLength(),Ct.getCharCountIndicator(c.mode,n)),c.write(o)});const r=te.getSymbolTotalCodewords(n),s=de.getTotalCodewordsCount(n,e),i=(r-s)*8;for(o.getLengthInBits()+4<=i&&o.put(0,4);o.getLengthInBits()%8!==0;)o.putBit(0);const l=(i-o.getLengthInBits())/8;for(let c=0;c<l;c++)o.put(c%2?17:236,8);return Tt(o,n,e)}function Tt(n,e,t){const o=te.getSymbolTotalCodewords(e),r=de.getTotalCodewordsCount(e,t),s=o-r,i=de.getBlocksCount(e,t),l=o%i,c=i-l,g=Math.floor(o/i),h=Math.floor(s/i),C=h+1,u=g-h,m=new Nt(u);let b=0;const w=new Array(i),v=new Array(i);let x=0;const N=new Uint8Array(n.buffer);for(let f=0;f<i;f++){const E=f<c?h:C;w[f]=N.slice(b,b+E),v[f]=m.encode(w[f]),b+=E,x=Math.max(x,E)}const y=new Uint8Array(o);let d=0,p,j;for(p=0;p<x;p++)for(j=0;j<i;j++)p<w[j].length&&(y[d++]=w[j][p]);for(p=0;p<u;p++)for(j=0;j<i;j++)y[d++]=v[j][p];return y}function kt(n,e,t,o){let r;if(Array.isArray(n))r=oe.fromArray(n);else if(typeof n=="string"){let g=e;if(!g){const h=oe.rawSplit(n);g=W.getBestVersionForData(h,t)}r=oe.fromString(n,g||40)}else throw new Error("Invalid data");const s=W.getBestVersionForData(r,t);if(!s)throw new Error("The amount of data is too big to be stored in a QR Code");if(!e)e=s;else if(e<s)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+s+`.
`);const i=Bt(e,t,r),l=te.getSymbolSize(e),c=new xt(l);return Et(c,e),_t(c),vt(c,e),ae(c,t,0),e>=7&&At(c,e),St(c,i),isNaN(o)&&(o=le.getBestMask(c,ae.bind(null,c,t))),le.applyMask(o,c),ae(c,t,o),{modules:c,version:e,errorCorrectionLevel:t,maskPattern:o,segments:r}}_e.create=function(e,t){if(typeof e>"u"||e==="")throw new Error("No input text");let o=se.M,r,s;return typeof t<"u"&&(o=se.from(t.errorCorrectionLevel,se.M),r=W.from(t.version),s=le.from(t.maskPattern),t.toSJISFunc&&te.setToSJISFunction(t.toSJISFunc)),kt(e,r,o,s)};var De={},fe={};(function(n){function e(t){if(typeof t=="number"&&(t=t.toString()),typeof t!="string")throw new Error("Color should be defined as hex string");let o=t.slice().replace("#","").split("");if(o.length<3||o.length===5||o.length>8)throw new Error("Invalid hex color: "+t);(o.length===3||o.length===4)&&(o=Array.prototype.concat.apply([],o.map(function(s){return[s,s]}))),o.length===6&&o.push("F","F");const r=parseInt(o.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:r&255,hex:"#"+o.slice(0,6).join("")}}n.getOptions=function(o){o||(o={}),o.color||(o.color={});const r=typeof o.margin>"u"||o.margin===null||o.margin<0?4:o.margin,s=o.width&&o.width>=21?o.width:void 0,i=o.scale||4;return{width:s,scale:s?4:i,margin:r,color:{dark:e(o.color.dark||"#000000ff"),light:e(o.color.light||"#ffffffff")},type:o.type,rendererOpts:o.rendererOpts||{}}},n.getScale=function(o,r){return r.width&&r.width>=o+r.margin*2?r.width/(o+r.margin*2):r.scale},n.getImageWidth=function(o,r){const s=n.getScale(o,r);return Math.floor((o+r.margin*2)*s)},n.qrToImageData=function(o,r,s){const i=r.modules.size,l=r.modules.data,c=n.getScale(i,s),g=Math.floor((i+s.margin*2)*c),h=s.margin*c,C=[s.color.light,s.color.dark];for(let u=0;u<g;u++)for(let m=0;m<g;m++){let b=(u*g+m)*4,w=s.color.light;if(u>=h&&m>=h&&u<g-h&&m<g-h){const v=Math.floor((u-h)/c),x=Math.floor((m-h)/c);w=C[l[v*i+x]?1:0]}o[b++]=w.r,o[b++]=w.g,o[b++]=w.b,o[b]=w.a}}})(fe);(function(n){const e=fe;function t(r,s,i){r.clearRect(0,0,s.width,s.height),s.style||(s.style={}),s.height=i,s.width=i,s.style.height=i+"px",s.style.width=i+"px"}function o(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}n.render=function(s,i,l){let c=l,g=i;typeof c>"u"&&(!i||!i.getContext)&&(c=i,i=void 0),i||(g=o()),c=e.getOptions(c);const h=e.getImageWidth(s.modules.size,c),C=g.getContext("2d"),u=C.createImageData(h,h);return e.qrToImageData(u.data,s,c),t(C,g,h),C.putImageData(u,0,0),g},n.renderToDataURL=function(s,i,l){let c=l;typeof c>"u"&&(!i||!i.getContext)&&(c=i,i=void 0),c||(c={});const g=n.render(s,i,c),h=c.type||"image/png",C=c.rendererOpts||{};return g.toDataURL(h,C.quality)}})(De);var Ue={};const It=fe;function je(n,e){const t=n.a/255,o=e+'="'+n.hex+'"';return t<1?o+" "+e+'-opacity="'+t.toFixed(2).slice(1)+'"':o}function ie(n,e,t){let o=n+e;return typeof t<"u"&&(o+=" "+t),o}function Mt(n,e,t){let o="",r=0,s=!1,i=0;for(let l=0;l<n.length;l++){const c=Math.floor(l%e),g=Math.floor(l/e);!c&&!s&&(s=!0),n[l]?(i++,l>0&&c>0&&n[l-1]||(o+=s?ie("M",c+t,.5+g+t):ie("m",r,0),r=0,s=!1),c+1<e&&n[l+1]||(o+=ie("h",i),i=0)):r++}return o}Ue.render=function(e,t,o){const r=It.getOptions(t),s=e.modules.size,i=e.modules.data,l=s+r.margin*2,c=r.color.light.a?"<path "+je(r.color.light,"fill")+' d="M0 0h'+l+"v"+l+'H0z"/>':"",g="<path "+je(r.color.dark,"stroke")+' d="'+Mt(i,s,r.margin)+'"/>',h='viewBox="0 0 '+l+" "+l+'"',u='<svg xmlns="http://www.w3.org/2000/svg" '+(r.width?'width="'+r.width+'" height="'+r.width+'" ':"")+h+' shape-rendering="crispEdges">'+c+g+`</svg>
`;return typeof o=="function"&&o(null,u),u};const Pt=Qe,ue=_e,$e=De,Rt=Ue;function pe(n,e,t,o,r){const s=[].slice.call(arguments,1),i=s.length,l=typeof s[i-1]=="function";if(!l&&!Pt())throw new Error("Callback required as last argument");if(l){if(i<2)throw new Error("Too few arguments provided");i===2?(r=t,t=e,e=o=void 0):i===3&&(e.getContext&&typeof r>"u"?(r=o,o=void 0):(r=o,o=t,t=e,e=void 0))}else{if(i<1)throw new Error("Too few arguments provided");return i===1?(t=e,e=o=void 0):i===2&&!e.getContext&&(o=t,t=e,e=void 0),new Promise(function(c,g){try{const h=ue.create(t,o);c(n(h,e,o))}catch(h){g(h)}})}try{const c=ue.create(t,o);r(null,n(c,e,o))}catch(c){r(c)}}H.create=ue.create;H.toCanvas=pe.bind(null,$e.render);H.toDataURL=pe.bind(null,$e.renderToDataURL);H.toString=pe.bind(null,function(n,e,t){return Rt.render(n,t)});const Ft=({isOpen:n,onClose:e,onPrint:t,receiptSize:o,onReceiptSizeChange:r})=>{const[s,i]=I.useState({printerType:"thermal",paperSize:o==="small"?"58mm":o==="medium"?"80mm":"A4",orientation:"portrait",quality:"normal",copies:1,margins:"minimum",colorMode:"blackwhite"}),l=()=>{t(s),e()},c=u=>{r(u),i(m=>({...m,paperSize:u==="small"?"58mm":u==="medium"?"80mm":"A4",printerType:u==="a4"?"laser":"thermal"}))},g=[{value:"thermal",label:"طابعة حرارية",icon:He},{value:"laser",label:"طابعة ليزر",icon:Ke},{value:"inkjet",label:"طابعة حبر",icon:xe}],h=[{value:"58mm",label:"58mm (صغير)",description:"للطابعات الحرارية الصغيرة"},{value:"80mm",label:"80mm (متوسط)",description:"للطابعات الحرارية المتوسطة"},{value:"A4",label:"A4 (كبير)",description:"للطابعات العادية"}],C=[{value:"draft",label:"مسودة",description:"سريع وموفر للحبر"},{value:"normal",label:"عادي",description:"جودة متوازنة"},{value:"high",label:"عالي",description:"أفضل جودة"}];return a.jsx(qe,{isOpen:n,onClose:e,title:"خصائص الطباعة",children:a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{children:[a.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:[a.jsx(Ce,{className:"inline mr-2"}),"نوع الطابعة"]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:g.map(u=>{const m=u.icon;return a.jsxs("button",{onClick:()=>i(b=>({...b,printerType:u.value})),className:`p-4 border-2 rounded-lg text-center transition-all ${s.printerType===u.value?"border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300":"border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600"}`,children:[a.jsx(m,{className:"mx-auto mb-2 text-xl"}),a.jsx("div",{className:"text-sm font-medium",children:u.label})]},u.value)})})]}),a.jsxs("div",{children:[a.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:[a.jsx(xe,{className:"inline mr-2"}),"حجم الورق"]}),a.jsx("div",{className:"space-y-2",children:h.map(u=>a.jsxs("label",{className:`flex items-center p-3 border rounded-lg cursor-pointer transition-all ${s.paperSize===u.value?"border-primary-500 bg-primary-50 dark:bg-primary-900/20":"border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600"}`,children:[a.jsx("input",{type:"radio",name:"paperSize",value:u.value,checked:s.paperSize===u.value,onChange:m=>{i(w=>({...w,paperSize:m.target.value})),c({"58mm":"small","80mm":"medium",A4:"a4"}[m.target.value]||"medium")},className:"sr-only"}),a.jsxs("div",{className:"flex-1",children:[a.jsx("div",{className:"font-medium text-gray-900 dark:text-gray-100",children:u.label}),a.jsx("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:u.description})]}),s.paperSize===u.value&&a.jsx("div",{className:"w-4 h-4 bg-primary-500 rounded-full"})]},u.value))})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"جودة الطباعة"}),a.jsx("select",{value:s.quality,onChange:u=>i(m=>({...m,quality:u.target.value})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",children:C.map(u=>a.jsxs("option",{value:u.value,children:[u.label," - ",u.description]},u.value))})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"عدد النسخ"}),a.jsx("input",{type:"number",min:"1",max:"10",value:s.copies,onChange:u=>i(m=>({...m,copies:parseInt(u.target.value)||1})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"الهوامش"}),a.jsxs("select",{value:s.margins,onChange:u=>i(m=>({...m,margins:u.target.value})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",children:[a.jsx("option",{value:"none",children:"بدون هوامش"}),a.jsx("option",{value:"minimum",children:"هوامش صغيرة"}),a.jsx("option",{value:"normal",children:"هوامش عادية"})]})]})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"وضع الألوان"}),a.jsxs("select",{value:s.colorMode,onChange:u=>i(m=>({...m,colorMode:u.target.value})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",children:[a.jsx("option",{value:"blackwhite",children:"أبيض وأسود"}),a.jsx("option",{value:"grayscale",children:"رمادي"}),a.jsx("option",{value:"color",children:"ملون"})]})]}),a.jsxs("div",{className:"flex justify-end space-x-3 space-x-reverse pt-4 border-t border-gray-200 dark:border-gray-600",children:[a.jsxs("button",{onClick:e,className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:[a.jsx(Je,{className:"inline mr-2"}),"إلغاء"]}),a.jsxs("button",{onClick:l,className:"px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors",children:[a.jsx(Ee,{className:"inline mr-2"}),"طباعة"]})]})]})})},$t=()=>{const{id:n}=Oe(),e=Ve(),[t,o]=I.useState(null),[r,s]=I.useState({name:"",address:"",phone:"",email:"",tax_rate:"0",receipt_header:"",receipt_footer:"",currency_symbol:"د.ل"}),[i,l]=I.useState(!0),[c,g]=I.useState(null),[h,C]=I.useState("medium"),[u,m]=I.useState(""),[b,w]=I.useState(!1);I.useEffect(()=>{(async()=>{l(!0),g(null);try{const p=parseInt(n||"0");if(isNaN(p)||p<=0)throw new Error("رقم الفاتورة غير صالح");const f=(await z.get(`/api/sales/${p}`)).data;if(!f||!f.items||!Array.isArray(f.items))throw new Error("بيانات الفاتورة غير صالحة");const E=await Promise.all(f.items.map(async S=>{try{const Y=await z.get(`/api/products/${S.product_id}`);return{...S,product_name:Y.data.name||`منتج #${S.product_id}`,price:S.unit_price}}catch{return{...S,product_name:`منتج #${S.product_id}`,price:S.unit_price}}}));let A="الكاشير";if(f.user_id)try{const S=await z.get(`/api/users/${f.user_id}`);A=S.data.full_name||S.data.username||"الكاشير"}catch{console.log("Could not fetch user data, using default cashier name"),f.user&&f.user.full_name?A=f.user.full_name:f.user&&f.user.username&&(A=f.user.username)}else f.user&&(A=f.user.full_name||f.user.username||"الكاشير");let _=null;if(f.customer_id&&f.customer_id!==null)try{_=(await z.get(`/api/customers/${f.customer_id}`)).data}catch{console.log("Could not fetch customer data"),f.customer&&(_=f.customer)}else f.customer&&(_=f.customer);const P=f.discount_amount||0,T=f.total_amount-P+(f.tax_amount||0),ze={...f,items:E,payment_amount:f.amount_paid||T,change_amount:0,cashier_name:A,customer:_};o(ze),s({name:"Smart POS",address:"ليبيا، طرابلس",phone:"+218 91-234-5678",email:"<EMAIL>",tax_rate:"0",receipt_header:"شكراً لاختيارك Smart POS",receipt_footer:"نتمنى لك يوماً سعيداً",currency_symbol:"د.ل"});try{const S=await z.get("/api/settings");if(S.data&&Array.isArray(S.data)){const Y=S.data,k={};Y.forEach(ye=>{k[ye.key]=ye.value}),Object.keys(k).length>0&&s({name:k.store_name||"Smart POS",address:k.store_address||"ليبيا، طرابلس",phone:k.store_phone||"+218 91-234-5678",email:k.store_email||"<EMAIL>",tax_rate:k.tax_rate||"0",receipt_header:k.receipt_header||"شكراً لاختيارك Smart POS",receipt_footer:k.receipt_footer||"نتمنى لك يوماً سعيداً",currency_symbol:k.currency_symbol||"د.ل"})}}catch{console.log("Settings API not available, using default store info")}l(!1)}catch(p){console.error("Error fetching receipt data:",p),g("فشل في تحميل بيانات الفاتورة"),l(!1)}})()},[n]),I.useEffect(()=>{(async()=>{if(t&&r.phone)try{const p=t.discount_amount||0,f=t.total_amount-p+(t.tax_amount||0);let E=`${r.name}
`;if(E+=`فاتورة: ${t.id}
`,E+=`المبلغ: ${f.toFixed(2)} ${r.currency_symbol}
`,E+=`التاريخ: ${N(t.created_at)}
`,t.customer&&t.customer.name!=="عميل مباشر"&&(E+=`العميل: ${t.customer.name}
`),t.amount_paid!==f){E+=`مدفوع: ${t.amount_paid.toFixed(2)} ${r.currency_symbol}
`;const _=f-t.amount_paid;E+=`متبقي: ${_.toFixed(2)} ${r.currency_symbol}
`}E+=`هاتف: ${r.phone}`;const A=await H.toDataURL(E,{width:200,margin:4,errorCorrectionLevel:"M",color:{dark:"#000000",light:"#FFFFFF"}});m(A)}catch(p){console.error("Error generating QR code:",p)}})()},[t,r]);const v=()=>{w(!0)},x=d=>{const p=document.createElement("style");p.textContent=`
      @media print {
        @page {
          size: ${d.paperSize==="A4"?"A4":d.paperSize} ${d.orientation};
          margin: ${d.margins==="none"?"0":d.margins==="minimum"?"5mm":"10mm"};
        }

        body {
          -webkit-print-color-adjust: ${d.colorMode==="color"?"exact":"economy"};
          print-color-adjust: ${d.colorMode==="color"?"exact":"economy"};
        }

        .receipt-wrapper {
          filter: ${d.colorMode==="grayscale"?"grayscale(100%)":"none"};
        }
      }
    `,document.head.appendChild(p);for(let j=0;j<d.copies;j++)setTimeout(()=>{window.print()},j*1e3);setTimeout(()=>{document.head.removeChild(p)},2e3)},N=d=>{const p=new Date(d),j=String(p.getDate()).padStart(2,"0"),f=String(p.getMonth()+1).padStart(2,"0"),E=p.getFullYear();return`${j}/${f}/${E}`},y=d=>new Date(d).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!1});return i?a.jsx("div",{className:"flex justify-center items-center h-screen",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"})}):c||!t?a.jsxs("div",{className:"container mx-auto px-4 py-6 flex flex-col items-center justify-center min-h-[70vh]",children:[a.jsxs("div",{className:"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4 max-w-md w-full text-center",children:[a.jsx("h2",{className:"text-xl font-bold mb-2",children:"عذراً!"}),a.jsx("p",{children:c||"حدث خطأ في تحميل الفاتورة"}),a.jsx("p",{className:"mt-2 text-sm",children:"يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى."})]}),a.jsxs("button",{onClick:()=>e(-1),className:"mt-4 flex items-center bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors",children:[a.jsx(be,{className:"ml-2"})," العودة للصفحة السابقة"]})]}):a.jsxs("div",{className:"receipt-container",children:[a.jsxs("div",{className:"print-controls print:hidden container mx-auto px-4 py-6 mb-6",children:[a.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:a.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:a.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center p-6 gap-4",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("button",{onClick:()=>e(-1),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md p-2 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm",children:a.jsx(be,{})}),a.jsxs("h1",{className:"text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100 mr-4 flex items-center",children:[a.jsx(re,{className:"ml-3 text-primary-600 dark:text-primary-400"}),"فاتورة رقم #",t.id]})]}),a.jsxs("div",{className:"flex items-center gap-2 flex-wrap",children:[a.jsxs("button",{onClick:()=>window.print(),className:"bg-gray-600 text-white rounded-md py-2 px-3 sm:px-4 hover:bg-gray-700 flex items-center transition-all shadow-md hover:shadow-lg",children:[a.jsx(Ee,{className:"ml-2"}),a.jsx("span",{className:"hidden sm:inline",children:"طباعة سريعة"}),a.jsx("span",{className:"sm:hidden",children:"سريعة"})]}),a.jsxs("button",{onClick:v,className:"bg-primary-600 text-white rounded-md py-2 px-3 sm:px-4 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg",children:[a.jsx(Ce,{className:"ml-2"}),a.jsx("span",{className:"hidden sm:inline",children:"خصائص الطباعة"}),a.jsx("span",{className:"sm:hidden",children:"خصائص"})]})]})]})})}),a.jsxs("div",{className:"receipt-size-selector",children:[a.jsx("h2",{className:"text-lg font-medium ml-4",children:"حجم الفاتورة:"}),a.jsxs("button",{onClick:()=>C("small"),className:`receipt-size-button ${h==="small"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[a.jsx(re,{}),a.jsx("span",{children:"صغير (58mm)"})]}),a.jsxs("button",{onClick:()=>C("medium"),className:`receipt-size-button ${h==="medium"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[a.jsx(re,{}),a.jsx("span",{children:"متوسط (80mm)"})]}),a.jsxs("button",{onClick:()=>C("a4"),className:`receipt-size-button ${h==="a4"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[a.jsx(Ye,{}),a.jsx("span",{children:"A4"})]})]})]}),a.jsx("div",{className:`receipt-wrapper receipt-size-${h}`,children:a.jsxs("div",{className:"receipt-content",children:[a.jsxs("div",{className:"receipt-header text-center receipt-section",children:[a.jsx("h1",{children:r.name}),r.address&&a.jsx("p",{className:"mb-1",children:r.address}),r.phone&&a.jsxs("p",{className:"mb-1",children:["هاتف: ",r.phone]}),r.email&&a.jsx("p",{className:"mb-1",children:r.email}),r.receipt_header&&a.jsx("p",{className:"mt-2 italic",children:r.receipt_header}),a.jsx("div",{className:"border-t border-gray-300 dark:border-gray-700 my-3"}),a.jsx("h2",{children:"فاتورة مبيعات"})]}),a.jsxs("div",{className:"receipt-info grid grid-cols-2 gap-2 receipt-section",children:[a.jsxs("div",{children:[a.jsxs("p",{children:[a.jsx("strong",{children:"رقم الفاتورة:"})," #",t.id]}),a.jsxs("p",{children:[a.jsx("strong",{children:"التاريخ:"})," ",N(t.created_at)]}),a.jsxs("p",{children:[a.jsx("strong",{children:"الوقت:"})," ",y(t.created_at)]})]}),a.jsxs("div",{className:"text-left",children:[a.jsxs("p",{children:[a.jsx("strong",{children:"الكاشير:"})," ",t.cashier_name||"-"]}),a.jsxs("p",{children:[a.jsx("strong",{children:"رقم الفاتورة الضريبي:"})," INV-",t.id,"-",new Date(t.created_at).getFullYear()]}),a.jsxs("p",{children:[a.jsx("strong",{children:"حالة الدفع:"})," ",t.payment_status==="paid"?"مدفوع":t.payment_status==="partial"?"جزئي":t.payment_status==="credit"?"آجل":"غير محدد"]})]})]}),t.customer&&t.customer.name!=="عميل مباشر"&&a.jsxs("div",{className:"receipt-customer receipt-section",children:[a.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"معلومات العميل"}),a.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[a.jsxs("div",{children:[a.jsxs("p",{children:[a.jsx("strong",{children:"الاسم:"})," ",t.customer.name]}),t.customer.phone&&a.jsxs("p",{children:[a.jsx("strong",{children:"الهاتف:"})," ",t.customer.phone]})]}),a.jsxs("div",{className:"text-left",children:[t.customer.email&&a.jsxs("p",{children:[a.jsx("strong",{children:"البريد الإلكتروني:"})," ",t.customer.email]}),t.customer.total_debt!==void 0&&t.customer.total_debt>0&&a.jsxs("p",{children:[a.jsx("strong",{children:"إجمالي المديونية:"})," ",t.customer.total_debt.toFixed(2)," ",r.currency_symbol]})]})]}),t.customer.address&&a.jsxs("p",{className:"mt-2",children:[a.jsx("strong",{children:"العنوان:"})," ",t.customer.address]})]}),a.jsxs("div",{className:"receipt-items py-2 receipt-section",children:[a.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"تفاصيل المنتجات"}),a.jsxs("table",{className:"receipt-items-table",children:[a.jsx("thead",{children:a.jsxs("tr",{children:[a.jsx("th",{className:"text-right",children:"المنتج"}),a.jsx("th",{className:"text-center",children:"الكمية"}),a.jsx("th",{className:"text-center",children:"السعر"}),a.jsx("th",{className:"text-left",children:"المجموع"})]})}),a.jsx("tbody",{children:t.items.map(d=>a.jsxs("tr",{children:[a.jsx("td",{className:"text-right",children:d.product_name||`منتج #${d.product_id}`}),a.jsx("td",{className:"text-center",children:d.quantity}),a.jsx("td",{className:"text-center",children:(d.price||d.unit_price).toFixed(2)}),a.jsx("td",{className:"text-left",children:d.subtotal.toFixed(2)})]},d.id))})]})]}),a.jsxs("div",{className:"receipt-totals receipt-section",children:[a.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"ملخص الفاتورة"}),(()=>{const d=t.discount_amount||0,p=t.total_amount,j=p-d,f=t.tax_amount||0,E=j+f;return a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"total-row",children:[a.jsx("span",{children:"المجموع قبل الخصم:"}),a.jsxs("span",{children:[p.toFixed(2)," ",r.currency_symbol]})]}),d>0&&a.jsxs("div",{className:"total-row discount-row",children:[a.jsxs("span",{children:["الخصم (",t.discount_type==="percentage"?"نسبة مئوية":"مبلغ ثابت","):"]}),a.jsxs("span",{children:["-",d.toFixed(2)," ",r.currency_symbol,t.discount_type==="percentage"&&a.jsxs("span",{className:"text-xs mr-1",children:["(",(d/p*100).toFixed(1),"%)"]})]})]}),a.jsxs("div",{className:"total-row",children:[a.jsx("span",{children:"المجموع بعد الخصم:"}),a.jsxs("span",{children:[j.toFixed(2)," ",r.currency_symbol]})]}),f>0&&a.jsxs("div",{className:"total-row",children:[a.jsx("span",{children:"الضريبة:"}),a.jsxs("span",{children:[f.toFixed(2)," ",r.currency_symbol]})]}),a.jsxs("div",{className:"total-row grand-total",children:[a.jsx("span",{children:"المجموع النهائي:"}),a.jsxs("span",{children:[E.toFixed(2)," ",r.currency_symbol]})]})]})})()]}),a.jsxs("div",{className:"receipt-payment receipt-section",children:[a.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"معلومات الدفع"}),(()=>{const d=t.discount_amount||0,p=t.total_amount-d,j=t.tax_amount||0,f=p+j,E=t.amount_paid||0,A=f-E,_=t.change_amount||0,P=()=>{if(t.payment_status==="credit")return"آجل";if(t.payment_status==="partial")return"جزئي";switch(t.payment_method){case"cash":return"نقدي";case"card":return"بطاقة";case"credit":return"آجل";case"partial":return"جزئي";default:return t.payment_method}};return a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"total-row",children:[a.jsx("span",{children:"طريقة الدفع:"}),a.jsx("span",{children:P()})]}),a.jsxs("div",{className:"total-row",children:[a.jsx("span",{children:"المبلغ المدفوع:"}),a.jsxs("span",{children:[E.toFixed(2)," ",r.currency_symbol]})]}),A>0&&a.jsxs("div",{className:"total-row debt-row",style:{color:"#dc2626"},children:[a.jsx("span",{children:"المبلغ المتبقي (مديونية):"}),a.jsxs("span",{children:[A.toFixed(2)," ",r.currency_symbol]})]}),t.payment_method==="cash"&&_>0&&a.jsxs("div",{className:"total-row",children:[a.jsx("span",{children:"المتبقي (الباقي):"}),a.jsxs("span",{children:[_.toFixed(2)," ",r.currency_symbol]})]}),a.jsxs("div",{className:"total-row",children:[a.jsx("span",{children:"حالة الفاتورة:"}),a.jsx("span",{style:{color:t.payment_status==="paid"?"#16a34a":t.payment_status==="partial"?"#ea580c":t.payment_status==="credit"?"#dc2626":"#6b7280"},children:t.payment_status==="paid"?"✓ مدفوعة بالكامل":t.payment_status==="partial"?"◐ مدفوعة جزئياً":t.payment_status==="credit"?"○ آجلة":"غير محدد"})]})]})})()]}),t.notes&&a.jsxs("div",{className:"receipt-notes mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md receipt-section",children:[a.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"ملاحظات"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:t.notes})]}),a.jsxs("div",{className:"receipt-footer receipt-section",children:[r.receipt_footer&&a.jsx("p",{className:"mb-3",children:r.receipt_footer}),a.jsx("p",{className:"mt-1",children:"** نسخة العميل **"})]}),u&&a.jsxs("div",{className:"receipt-qr",children:[a.jsx("div",{className:"qr-container",children:a.jsx("img",{src:u,alt:"QR Code",className:"qr-image"})}),a.jsxs("div",{className:"qr-info",children:[a.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 mb-1",children:"امسح الرمز للحصول على معلومات الفاتورة"}),a.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:["هاتف المتجر: ",r.phone]})]})]}),h==="a4"&&a.jsx("div",{className:"receipt-stamp",children:"مدفوع"})]})}),a.jsx(Ft,{isOpen:b,onClose:()=>w(!1),onPrint:x,receiptSize:h,onReceiptSizeChange:d=>C(d)})]})};export{$t as default};
