import{r as n,j as e}from"./index-DmuezwQ-.js";import{Y as N,Z as T,ay as Y,az as U,s as Z,m as G,aA as K,N as J,o as L}from"./index-Cn5nbJXI.js";const Q=n.forwardRef(({label:j,name:V,value:r,onChange:f,placeholder:C="",required:h=!1,disabled:v=!1,error:a,success:o,icon:l,type:d="text",dir:B="rtl",className:I="",maxLength:p,minLength:z,pattern:x,autoComplete:s,autoFocus:c=!1},S)=>{const[g,w]=n.useState(!1),[m,$]=n.useState(!1),k=A=>{f(A.target.value),m||$(!0)},u=()=>{w(!0)},E=()=>{w(!1),$(!0)},i=a&&m,y=o&&m&&!a,b=!r||r.trim()==="",D=h&&b&&m&&!a;return e.jsxs("div",{className:`relative ${I}`,children:[j&&e.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[j,h&&e.jsx("span",{className:"text-red-500 mr-1",children:"*"})]}),e.jsxs("div",{className:"relative",children:[l&&e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10",children:e.jsx("div",{className:`transition-colors duration-200 ${i?"text-red-500":y?"text-green-500":g?"text-primary-500":"text-gray-400 dark:text-gray-500"}`,children:l})}),e.jsx("input",{ref:S,type:d,name:V,value:r,onChange:k,onFocus:u,onBlur:E,placeholder:C,required:h,disabled:v,dir:B,maxLength:p,minLength:z,pattern:x,autoComplete:s,autoFocus:c,className:`
            w-full rounded-xl border-2 py-3 px-4 transition-all duration-200 ease-in-out
            ${l?"pr-12":""}
            ${v?"bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-200 dark:border-gray-700":"bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"}
            ${i?"border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20":y?"border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20":g?"border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}
            focus:outline-none
            placeholder:text-gray-400 dark:placeholder:text-gray-500
          `}),(i||y)&&e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3",children:i?e.jsx(N,{className:"h-5 w-5 text-red-500"}):e.jsx(T,{className:"h-5 w-5 text-green-500"})})]}),i&&e.jsx("div",{className:"mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:e.jsxs("p",{className:"text-red-700 dark:text-red-300 text-sm flex items-center",children:[e.jsx(N,{className:"ml-2 flex-shrink-0 text-red-500"}),a]})}),y&&e.jsx("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:e.jsxs("p",{className:"text-green-700 dark:text-green-300 text-sm flex items-center",children:[e.jsx(T,{className:"ml-2 flex-shrink-0 text-green-500"}),o]})}),D&&e.jsx("div",{className:"mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg",children:e.jsxs("p",{className:"text-orange-700 dark:text-orange-300 text-sm flex items-center",children:[e.jsx(N,{className:"ml-2 flex-shrink-0 text-orange-500"}),"هذا الحقل مطلوب، يرجى تعبئته"]})})]})});Q.displayName="TextInput";const W=n.forwardRef(({label:j,name:V,value:r,onChange:f,placeholder:C="0",required:h=!1,disabled:v=!1,error:a,success:o,icon:l,min:d,max:B,step:I="0.01",dir:p="ltr",className:z="",currency:x,showControls:s=!1,precision:c=2},S)=>{const[g,w]=n.useState(!1),[m,$]=n.useState(!1),k=R=>{let F=R.target.value;if(F===""){f("");return}/^-?\d*\.?\d*$/.test(F)&&f(F),m||$(!0)},u=()=>{w(!0)},E=()=>{if(w(!1),$(!0),r&&!isNaN(Number(r))){const F=Number(r).toFixed(c);f(F)}},i=()=>{const R=Number(r)||0,F=Number(I),M=R+F;(B===void 0||M<=B)&&f(M.toFixed(c))},y=()=>{const R=Number(r)||0,F=Number(I),M=R-F;(d===void 0||M>=d)&&f(M.toFixed(c))},b=a&&m,D=o&&m&&!a,A=!r||r.trim()==="",O=h&&A&&m&&!a;return e.jsxs("div",{className:`relative ${z}`,children:[j&&e.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[j,h&&e.jsx("span",{className:"text-red-500 mr-1",children:"*"})]}),e.jsxs("div",{className:"relative",children:[x&&e.jsx("div",{className:`absolute inset-y-0 flex items-center pointer-events-none z-10 ${s?"left-10 pl-3":"left-0 pl-4"}`,children:e.jsx("span",{className:"text-gray-500 dark:text-gray-400 text-sm font-medium",children:x})}),l&&!x&&e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10",children:e.jsx("div",{className:`transition-colors duration-200 ${b?"text-red-500":D?"text-green-500":g?"text-primary-500":"text-gray-400 dark:text-gray-500"}`,children:l})}),e.jsx("input",{ref:S,type:"number",name:V,value:r,onChange:k,onFocus:u,onBlur:E,placeholder:C,required:h,disabled:v,dir:p,min:d,max:B,step:I,className:`
            number-input-custom w-full rounded-xl border-2 py-3 px-4 transition-all duration-200 ease-in-out
            ${x&&s?"pl-20":x?"pl-16":s?"pl-10":""}
            ${l&&!x?"pr-12":""}
            ${v?"bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-200 dark:border-gray-700":"bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"}
            ${b?"border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20":D?"border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20":g?"border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}
            focus:outline-none
            placeholder:text-gray-400 dark:placeholder:text-gray-500
          `}),s&&!v&&e.jsxs("div",{className:"absolute left-2 top-1/2 -translate-y-1/2 flex flex-col",children:[e.jsx("button",{type:"button",onClick:i,className:"w-6 h-4 flex items-center justify-center bg-transparent hover:bg-primary-50 dark:hover:bg-primary-900/30 text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-200 rounded-t border border-gray-300 dark:border-gray-600 border-b-0 group",children:e.jsx(Y,{className:"h-2 w-2 group-hover:scale-110 transition-transform"})}),e.jsx("button",{type:"button",onClick:y,className:"w-6 h-4 flex items-center justify-center bg-transparent hover:bg-primary-50 dark:hover:bg-primary-900/30 text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-all duration-200 rounded-b border border-gray-300 dark:border-gray-600 group",children:e.jsx(U,{className:"h-2 w-2 group-hover:scale-110 transition-transform"})})]}),(b||D)&&e.jsx("div",{className:`absolute inset-y-0 flex items-center ${x&&s?"left-20":x?"left-16":s?"left-10":"left-3"}`,children:b?e.jsx(N,{className:"h-5 w-5 text-red-500"}):e.jsx(T,{className:"h-5 w-5 text-green-500"})})]}),b&&e.jsx("div",{className:"mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:e.jsxs("p",{className:"text-red-700 dark:text-red-300 text-sm flex items-center",children:[e.jsx(N,{className:"ml-2 flex-shrink-0 text-red-500"}),a]})}),D&&e.jsx("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:e.jsxs("p",{className:"text-green-700 dark:text-green-300 text-sm flex items-center",children:[e.jsx(T,{className:"ml-2 flex-shrink-0 text-green-500"}),o]})}),O&&e.jsx("div",{className:"mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg",children:e.jsxs("p",{className:"text-orange-700 dark:text-orange-300 text-sm flex items-center",children:[e.jsx(N,{className:"ml-2 flex-shrink-0 text-orange-500"}),"هذا الحقل مطلوب، يرجى تعبئته"]})})]})});W.displayName="NumberInput";const X=n.forwardRef(({label:j,name:V,value:r,onChange:f,placeholder:C="",required:h=!1,disabled:v=!1,error:a,success:o,rows:l=3,maxLength:d,minLength:B,dir:I="rtl",className:p="",autoFocus:z=!1,resize:x="vertical"},s)=>{const[c,S]=n.useState(!1),[g,w]=n.useState(!1),m=A=>{f(A.target.value),g||w(!0)},$=()=>{S(!0)},k=()=>{S(!1),w(!0)},u=a&&g,E=o&&g&&!a,i=!r||r.trim()==="",y=h&&i&&g&&!a,b=r?r.length:0,D=d&&d>0;return e.jsxs("div",{className:`relative ${p}`,children:[j&&e.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[j,h&&e.jsx("span",{className:"text-red-500 mr-1",children:"*"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("textarea",{ref:s,name:V,value:r,onChange:m,onFocus:$,onBlur:k,placeholder:C,required:h,disabled:v,dir:I,rows:l,maxLength:d,minLength:B,autoFocus:z,style:{resize:x},className:`
            w-full rounded-xl border-2 py-3 px-4 transition-all duration-200 ease-in-out
            ${v?"bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-200 dark:border-gray-700":"bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"}
            ${u?"border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20":E?"border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20":c?"border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}
            focus:outline-none
            placeholder:text-gray-400 dark:placeholder:text-gray-500
          `}),(u||E)&&e.jsx("div",{className:"absolute top-3 left-3",children:u?e.jsx(N,{className:"h-5 w-5 text-red-500"}):e.jsx(T,{className:"h-5 w-5 text-green-500"})}),D&&e.jsx("div",{className:"absolute bottom-2 left-3",children:e.jsxs("span",{className:`text-xs ${b>d*.9?"text-red-500":b>d*.7?"text-orange-500":"text-gray-400 dark:text-gray-500"}`,children:[b,"/",d]})})]}),u&&e.jsx("div",{className:"mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:e.jsxs("p",{className:"text-red-700 dark:text-red-300 text-sm flex items-center",children:[e.jsx(N,{className:"ml-2 flex-shrink-0 text-red-500"}),a]})}),E&&e.jsx("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:e.jsxs("p",{className:"text-green-700 dark:text-green-300 text-sm flex items-center",children:[e.jsx(T,{className:"ml-2 flex-shrink-0 text-green-500"}),o]})}),y&&e.jsx("div",{className:"mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg",children:e.jsxs("p",{className:"text-orange-700 dark:text-orange-300 text-sm flex items-center",children:[e.jsx(N,{className:"ml-2 flex-shrink-0 text-orange-500"}),"هذا الحقل مطلوب، يرجى تعبئته"]})})]})});X.displayName="TextArea";const _=n.forwardRef(({label:j,name:V,value:r,onChange:f,options:C,placeholder:h="اختر خياراً...",required:v=!1,disabled:a=!1,error:o,success:l,icon:d,className:B="",searchable:I=!1,clearable:p=!1,maxHeight:z="200px"},x)=>{const[s,c]=n.useState(!1),[S,g]=n.useState(""),[w,m]=n.useState(!1),[$,k]=n.useState(-1),u=n.useRef(null),E=n.useRef(null);n.useEffect(()=>{const t=H=>{u.current&&!u.current.contains(H.target)&&(c(!1),g(""))};return document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}},[]),n.useEffect(()=>{s&&I&&E.current&&E.current.focus()},[s,I]);const i=C.filter(t=>t.label.toLowerCase().includes(S.toLowerCase())),y=C.find(t=>t.value===r),b=()=>{a||(c(!s),m(!0),k(-1))},D=t=>{f(t),c(!1),g(""),m(!0)},A=t=>{t.stopPropagation(),f(""),m(!0)},O=t=>{if(!s){(t.key==="Enter"||t.key===" ")&&(t.preventDefault(),c(!0));return}switch(t.key){case"Escape":c(!1),g("");break;case"ArrowDown":t.preventDefault(),k(H=>H<i.length-1?H+1:0);break;case"ArrowUp":t.preventDefault(),k(H=>H>0?H-1:i.length-1);break;case"Enter":t.preventDefault(),$>=0&&i[$]&&D(i[$].value);break}},R=o&&w,F=l&&w&&!o,P=v&&!r&&w&&!o;return e.jsxs("div",{className:`relative ${B}`,ref:x||u,children:[j&&e.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[j,v&&e.jsx("span",{className:"text-red-500 mr-1",children:"*"})]}),e.jsxs("div",{className:"relative",children:[d&&e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10",children:e.jsx("div",{className:`transition-colors duration-200 ${R?"text-red-500":F?"text-green-500":s?"text-primary-500":"text-gray-400 dark:text-gray-500"}`,children:d})}),e.jsx("button",{type:"button",onClick:b,onKeyDown:O,disabled:a,className:`
            w-full rounded-xl border-2 py-3 px-4 text-right transition-all duration-200 ease-in-out
            ${d?"pr-12":""}
            ${p&&r?"pl-12":"pl-10"}
            ${a?"bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-200 dark:border-gray-700":"bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 cursor-pointer"}
            ${R?"border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20":F?"border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20":s?"border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}
            focus:outline-none
          `,children:e.jsx("span",{className:`block truncate ${y?"":"text-gray-400 dark:text-gray-500"}`,children:y?y.label:h})}),e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx(U,{className:`h-4 w-4 text-gray-400 dark:text-gray-500 transition-transform duration-200 ${s?"transform rotate-180":""}`})}),p&&r&&!a&&e.jsx("button",{type:"button",onClick:A,className:"absolute inset-y-0 left-8 flex items-center pl-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:e.jsx("span",{className:"text-lg",children:"×"})}),(R||F)&&e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3",children:R?e.jsx(N,{className:"h-5 w-5 text-red-500"}):e.jsx(T,{className:"h-5 w-5 text-green-500"})}),e.jsx("input",{type:"hidden",name:V,value:r})]}),s&&e.jsxs("div",{className:"absolute z-50 mt-1 w-full bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden",children:[I&&e.jsx("div",{className:"p-3 border-b border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"relative",children:[e.jsx(Z,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-4 w-4"}),e.jsx("input",{ref:E,type:"text",value:S,onChange:t=>g(t.target.value),placeholder:"بحث...",className:"w-full pr-10 pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"})]})}),e.jsx("div",{className:"max-h-60 overflow-auto dropdown-scrollbar",style:{maxHeight:z},children:i.length===0?e.jsx("div",{className:"px-3 py-2 text-gray-500 dark:text-gray-400 text-center",children:"لا توجد خيارات متاحة"}):i.map((t,H)=>e.jsxs("button",{type:"button",onClick:()=>!t.disabled&&D(t.value),disabled:t.disabled,className:`w-full px-3 py-2 text-right flex items-center justify-between transition-colors ${t.disabled?"text-gray-400 dark:text-gray-600 cursor-not-allowed":H===$?"bg-primary-50 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300":t.value===r?"bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-200":"text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[e.jsxs("div",{className:"flex items-center",children:[t.icon&&e.jsx("span",{className:"ml-2",children:t.icon}),e.jsx("span",{className:"truncate",children:t.label})]}),t.value===r&&e.jsx(G,{className:"h-4 w-4 text-primary-600 dark:text-primary-400"})]},t.value))})]}),R&&e.jsx("div",{className:"mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:e.jsxs("p",{className:"text-red-700 dark:text-red-300 text-sm flex items-center",children:[e.jsx(N,{className:"ml-2 flex-shrink-0 text-red-500"}),o]})}),F&&e.jsx("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:e.jsxs("p",{className:"text-green-700 dark:text-green-300 text-sm flex items-center",children:[e.jsx(T,{className:"ml-2 flex-shrink-0 text-green-500"}),l]})}),P&&e.jsx("div",{className:"mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg",children:e.jsxs("p",{className:"text-orange-700 dark:text-orange-300 text-sm flex items-center",children:[e.jsx(N,{className:"ml-2 flex-shrink-0 text-orange-500"}),"هذا الحقل مطلوب، يرجى اختيار قيمة"]})})]})});_.displayName="SelectInput";const q=n.forwardRef(({label:j,name:V,value:r,onChange:f,onGenerate:C,onValidate:h,placeholder:v="أدخل الباركود أو اضغط توليد",required:a=!1,disabled:o=!1,error:l,success:d,className:B="",showGenerateButton:I=!0,validating:p=!1},z)=>{const[x,s]=n.useState(!1),[c,S]=n.useState(!1),g=y=>{const b=y.target.value;f(b),c||S(!0),h&&b.trim()&&h(b)},w=()=>{s(!0)},m=()=>{s(!1),S(!0)},$=()=>{if(C){const y=C();f(y),S(!0)}},k=l&&c,u=d&&c&&!l&&!p,E=!r||r.trim()==="",i=a&&E&&c&&!l;return e.jsxs("div",{className:`relative ${B}`,children:[j&&e.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[j,a&&e.jsx("span",{className:"text-red-500 mr-1",children:"*"})]}),e.jsxs("div",{className:"relative flex gap-2",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none z-10",children:e.jsx("div",{className:`transition-colors duration-200 ${p?"text-blue-500":k?"text-red-500":u?"text-green-500":x?"text-primary-500":"text-gray-400 dark:text-gray-500"}`,children:p?e.jsx(K,{className:"animate-spin"}):e.jsx(J,{})})}),e.jsx("input",{ref:z,type:"text",name:V,value:r,onChange:g,onFocus:w,onBlur:m,placeholder:v,required:a,disabled:o,dir:"ltr",className:`
              w-full rounded-xl border-2 py-3 px-4 pr-12 transition-all duration-200 ease-in-out
              ${o?"bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed border-gray-200 dark:border-gray-700":"bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"}
              ${k?"border-red-500 focus:border-red-500 focus:ring-4 focus:ring-red-500/20":u?"border-green-500 focus:border-green-500 focus:ring-4 focus:ring-green-500/20":p?"border-blue-500 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20":x?"border-primary-500 focus:border-primary-500 focus:ring-4 focus:ring-primary-500/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}
              focus:outline-none
              placeholder:text-gray-400 dark:placeholder:text-gray-500
              font-mono
            `}),(k||u)&&e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3",children:k?e.jsx(N,{className:"h-5 w-5 text-red-500"}):e.jsx(T,{className:"h-5 w-5 text-green-500"})})]}),I&&e.jsxs("button",{type:"button",onClick:$,disabled:o,className:`
              px-4 py-3 rounded-xl border-2 transition-all duration-200 ease-in-out
              flex items-center gap-2 whitespace-nowrap
              ${o?"bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-600 border-gray-200 dark:border-gray-700 cursor-not-allowed":"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:border-primary-500 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20"}
              focus:outline-none focus:ring-4 focus:ring-primary-500/20
            `,title:"توليد باركود جديد",children:[e.jsx(L,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"توليد"})]})]}),p&&e.jsx("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:e.jsxs("p",{className:"text-blue-700 dark:text-blue-300 text-sm flex items-center",children:[e.jsx(K,{className:"animate-spin ml-2 flex-shrink-0 text-blue-500"}),"جاري التحقق من الباركود..."]})}),k&&e.jsx("div",{className:"mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:e.jsxs("p",{className:"text-red-700 dark:text-red-300 text-sm flex items-center",children:[e.jsx(N,{className:"ml-2 flex-shrink-0 text-red-500"}),l]})}),u&&e.jsx("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:e.jsxs("p",{className:"text-green-700 dark:text-green-300 text-sm flex items-center",children:[e.jsx(T,{className:"ml-2 flex-shrink-0 text-green-500"}),d]})}),i&&e.jsx("div",{className:"mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg",children:e.jsxs("p",{className:"text-orange-700 dark:text-orange-300 text-sm flex items-center",children:[e.jsx(N,{className:"ml-2 flex-shrink-0 text-orange-500"}),"هذا الحقل مطلوب، يرجى إدخال الباركود أو توليد باركود جديد"]})}),r&&!l&&!p&&e.jsxs("div",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400 font-mono",children:["الباركود: ",r]})]})});q.displayName="BarcodeInput";export{q as B,W as N,_ as S,Q as T,X as a};
