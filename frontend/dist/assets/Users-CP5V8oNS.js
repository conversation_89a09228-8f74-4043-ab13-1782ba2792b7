import{j as e,r as t,u as he,d as Ge,a as He,b as U}from"./index-B6Ptcst5.js";import{Y as W,O as pe,v as Q,o as Je,B as Qe,m as be,g as E,a7 as L,a8 as ue,s as ye,a0 as Ve,a9 as We,I as Xe,C as Ze,aa as k,T as er,a1 as rr,a2 as sr,ab as ar,ac as tr,ad as lr,ae as dr,af as ir,ag as fe,A as nr}from"./index-CD3RUED3.js";import{S as je}from"./SelectBox-DNGiHK_-.js";import{T as V}from"./ToggleSwitch-B_CYwPI5.js";import{M as X}from"./Modal-Cbe9soZb.js";import{T as w,S as or}from"./BarcodeInput-DJiRmhaz.js";const cr=({isOpen:x,onClose:N,onConfirm:p,title:T,message:$,confirmText:v="تأكيد",cancelText:z="إلغاء",isLoading:b=!1})=>e.jsx(X,{isOpen:x,onClose:N,title:T,size:"sm",zIndex:"high",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30 mb-6",children:e.jsx(W,{className:"h-8 w-8 text-red-600 dark:text-red-400"})}),e.jsx("div",{className:"mb-6",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:$})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx("button",{onClick:N,disabled:b,className:"btn-secondary flex items-center justify-center min-w-[100px]",children:e.jsx("span",{children:z})}),e.jsx("button",{onClick:p,disabled:b,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[100px]",children:b?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"}),e.jsx("span",{children:"جاري التنفيذ..."})]}):e.jsx("span",{children:v})})]})]})}),ke={username:"",full_name:"",email:"",password:"",confirm_password:"",role:"cashier",is_active:!0},ur=()=>{const[x,N]=t.useState([]),[p,T]=t.useState([]),[$,v]=t.useState(!1),[z,b]=t.useState(!1),[C,Z]=t.useState(!1),[B,K]=t.useState(null),[a,g]=t.useState(ke),[h,ee]=t.useState(!1),[we,D]=t.useState(!1),[S,re]=t.useState(""),[se,ae]=t.useState(""),[d,m]=t.useState({}),[u,ve]=t.useState(""),[te,y]=t.useState(""),[le,c]=t.useState(""),[Ne,I]=t.useState(!1),[f,Y]=t.useState(null),[G,Ce]=t.useState(!1),[M,de]=t.useState(!1),[O,ie]=t.useState(!1),[A,Se]=t.useState(""),[_,_e]=t.useState(10),[i,H]=t.useState(1),[ne,Fe]=t.useState([]),[R,Pe]=t.useState({totalUsers:0,activeUsers:0,adminUsers:0,cashierUsers:0}),{user:n}=he();Ge();const oe=He(),Ue=(r,s)=>{if(r===0)return{icon:e.jsx(ar,{}),bgColor:"bg-gray-100 dark:bg-gray-700",textColor:"text-gray-600 dark:text-gray-400",label:"لا توجد مبيعات"};switch(s){case 1:return{icon:e.jsx(nr,{}),bgColor:"bg-yellow-100 dark:bg-yellow-900/30",textColor:"text-yellow-800 dark:text-yellow-300",label:"الأول"};case 2:return{icon:e.jsx(fe,{}),bgColor:"bg-gray-100 dark:bg-gray-700",textColor:"text-gray-700 dark:text-gray-300",label:"الثاني"};case 3:return{icon:e.jsx(fe,{}),bgColor:"bg-orange-100 dark:bg-orange-900/30",textColor:"text-orange-700 dark:text-orange-300",label:"الثالث"};default:return r>=20?{icon:e.jsx(tr,{}),bgColor:"bg-purple-100 dark:bg-purple-900/30",textColor:"text-purple-700 dark:text-purple-300",label:"ممتاز"}:r>=10?{icon:e.jsx(lr,{}),bgColor:"bg-green-100 dark:bg-green-900/30",textColor:"text-green-700 dark:text-green-300",label:"جيد جداً"}:r>=5?{icon:e.jsx(dr,{}),bgColor:"bg-blue-100 dark:bg-blue-900/30",textColor:"text-blue-700 dark:text-blue-300",label:"جيد"}:{icon:e.jsx(ir,{}),bgColor:"bg-yellow-50 dark:bg-yellow-900/20",textColor:"text-yellow-600 dark:text-yellow-400",label:"مقبول"}}},Ee=()=>{const r=[...x].sort((l,o)=>(o.sales_count||0)-(l.sales_count||0)),s={};return r.forEach((l,o)=>{l.sales_count&&l.sales_count>0&&(s[l.id]=o+1)}),s};t.useEffect(()=>{J(),(n==null?void 0:n.role)!=="admin"&&c("لا تملك صلاحيات كافية للوصول إلى هذه الصفحة")},[n]),t.useEffect(()=>{ce()},[x]),t.useEffect(()=>{xe()},[u,x,M,O,A]),t.useEffect(()=>{Te()},[p,i,_]);const ce=()=>{Pe({totalUsers:x.length,activeUsers:x.filter(r=>r.is_active).length,adminUsers:x.filter(r=>r.role==="admin").length,cashierUsers:x.filter(r=>r.role==="cashier").length})},xe=()=>{let r=[...x];u.trim()&&(r=r.filter(s=>s.username.toLowerCase().includes(u.toLowerCase())||s.full_name.toLowerCase().includes(u.toLowerCase())||s.email&&s.email.toLowerCase().includes(u.toLowerCase()))),M&&!O?r=r.filter(s=>s.is_active):O&&!M&&(r=r.filter(s=>!s.is_active)),A&&(r=r.filter(s=>s.role===A)),r.sort((s,l)=>(l.sales_count||0)-(s.sales_count||0)),T(r),H(1)},Te=()=>{const r=(i-1)*_,s=r+_,l=p.slice(r,s);Fe(l)},F=Math.ceil(p.length/_),P=r=>{H(r)},$e=r=>{_e(r),H(1)},me=()=>{xe()},J=async()=>{if((n==null?void 0:n.role)!=="admin"){console.log("User is not admin:",n==null?void 0:n.role),c("لا تملك صلاحيات كافية للوصول إلى هذه الصفحة"),v(!1);return}v(!0);try{console.log("Fetching users..."),console.log("Current auth token:",he.getState().token);const r=await U.get("/api/users/",{params:{_t:new Date().getTime()}});console.log("Users fetched successfully:",r.data),N(r.data),T(r.data),v(!1)}catch(r){console.error("Error fetching users:",r),r.response?(console.error("Error response:",r.response.status,r.response.data),r.response.status===401?(console.log("Authentication error - token may be invalid"),c("خطأ في المصادقة. يرجى تسجيل الخروج وإعادة تسجيل الدخول.")):r.response.status===403?(console.log("Authorization error - user may not have admin role"),c("لا تملك صلاحيات كافية للوصول إلى هذه الصفحة")):(console.log("Other API error:",r.response.status),c(`فشل في تحميل المستخدمين. يرجى المحاولة مرة أخرى. (${r.response.status})`))):r.request?(console.error("Error request - no response received:",r.request),c("فشل في الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.")):(console.error("Error setting up request:",r.message),c("فشل في تحميل المستخدمين. يرجى المحاولة مرة أخرى.")),v(!1)}},De=()=>{const r={};return a.username.trim()||(r.username="اسم المستخدم مطلوب"),a.full_name.trim()||(r.full_name="الاسم الكامل مطلوب"),a.email&&!/\S+@\S+\.\S+/.test(a.email)&&(r.email="البريد الإلكتروني غير صالح"),C||(a.password?a.password.length<6&&(r.password="كلمة المرور يجب أن تكون 6 أحرف على الأقل"):r.password="كلمة المرور مطلوبة",a.password!==a.confirm_password&&(r.confirm_password="كلمات المرور غير متطابقة")),m(r),Object.keys(r).length===0},Ie=()=>{const r={};return S?S.length<6&&(r.newPassword="كلمة المرور يجب أن تكون 6 أحرف على الأقل"):r.newPassword="كلمة المرور الجديدة مطلوبة",S!==se&&(r.confirmNewPassword="كلمات المرور غير متطابقة"),m(r),Object.keys(r).length===0},q=()=>{g(ke),m({}),Z(!1),K(null),b(!1)},Me=()=>{q(),b(!0)},Oe=r=>{Z(!0),K(r.id),g({username:r.username,full_name:r.full_name,email:r.email||"",password:"",confirm_password:"",role:r.role,is_active:r.is_active}),b(!0)},Ae=r=>{K(r),re(""),ae(""),D(!0)},Re=r=>{const s=new Date(r),l=String(s.getDate()).padStart(2,"0"),o=String(s.getMonth()+1).padStart(2,"0"),j=s.getFullYear();return`${l}/${o}/${j}`},qe=r=>{Y(r),I(!0)},Le=async r=>{if(r.preventDefault(),!!De())try{if(C&&B){const s={username:a.username,full_name:a.full_name,email:a.email||null,role:a.role,is_active:a.is_active};await U.post(`/api/users/${B}/update`,s),y("تم تحديث المستخدم بنجاح")}else{const s={username:a.username,full_name:a.full_name,email:a.email||null,password:a.password,role:a.role,is_active:a.is_active};await U.post("/api/users",s),y("تم إنشاء المستخدم بنجاح")}J(),q(),setTimeout(()=>y(""),3e3)}catch(s){console.error("Error saving user:",s),s.response&&s.response.data&&s.response.data.detail?s.response.data.detail.includes("already exists")?m({...d,username:"اسم المستخدم موجود بالفعل"}):c(`فشل في حفظ المستخدم: ${s.response.data.detail}`):c("فشل في حفظ المستخدم. يرجى المحاولة مرة أخرى."),setTimeout(()=>c(""),3e3)}},ze=async r=>{if(r.preventDefault(),!!Ie())try{await U.post(`/api/users/${B}/reset-password`,{password:S}),D(!1),y("تم تغيير كلمة المرور بنجاح"),setTimeout(()=>y(""),3e3)}catch(s){console.error("Error resetting password:",s),c("فشل في تغيير كلمة المرور. يرجى المحاولة مرة أخرى."),setTimeout(()=>c(""),3e3)}},Be=async()=>{var r,s;if(f)try{await U.delete(`/api/users/${f.id}`),N(x.filter(l=>l.id!==f.id)),y("تم حذف المستخدم بنجاح"),setTimeout(()=>y(""),3e3),I(!1),Y(null)}catch(l){console.error("Error deleting user:",l);let o="حدث خطأ أثناء حذف المستخدم";if((s=(r=l.response)==null?void 0:r.data)!=null&&s.detail){const j=l.response.data.detail;j.includes("sales records")?o=`لا يمكن حذف المستخدم "${f.full_name}". هذا المستخدم لديه سجلات مبيعات في النظام. يرجى نقل أو إزالة هذه السجلات قبل حذف المستخدم.`:j.includes("Cannot delete your own account")?o="لا يمكنك حذف حسابك الشخصي":o=j}c(o),setTimeout(()=>c(""),8e3),I(!1),Y(null)}};return(n==null?void 0:n.role)!=="admin"?e.jsxs("div",{className:"touch-container",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("button",{onClick:()=>oe("/"),className:"btn-icon mr-3",children:e.jsx(pe,{})}),e.jsx("h1",{className:"text-2xl font-bold text-secondary-900",children:"إدارة المستخدمين"})]})}),e.jsxs("div",{className:"bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 p-4 rounded-xl mb-4 shadow-soft flex items-center",children:[e.jsx(W,{className:"ml-2"}),"لا تملك صلاحيات كافية للوصول إلى هذه الصفحة. يرجى تسجيل الدخول كمدير."]})]}):e.jsxs("div",{className:"touch-container",children:[e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:e.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4",children:[e.jsxs("div",{className:"flex items-center min-w-0 flex-1",children:[e.jsx("button",{onClick:()=>oe("/"),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg p-2.5 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm flex-shrink-0",title:"العودة للرئيسية",children:e.jsx(pe,{className:"text-sm"})}),e.jsxs("div",{className:"mr-3 sm:mr-4 min-w-0 flex-1",children:[e.jsxs("h1",{className:"text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center",children:[e.jsx(Q,{className:"ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:"إدارة المستخدمين"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block",children:"إدارة حسابات المستخدمين والصلاحيات"})]})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3 flex-wrap lg:flex-nowrap",children:[e.jsx("button",{onClick:()=>{J(),ce()},className:"text-gray-600 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 p-2.5 rounded-lg hover:bg-white/50 dark:hover:bg-gray-700/50 transition-colors backdrop-blur-sm border border-gray-200 dark:border-gray-600",title:"تحديث",children:e.jsx(Je,{className:`text-sm ${$?"animate-spin":""}`})}),e.jsxs("button",{onClick:Me,className:"bg-primary-600 text-white rounded-lg py-2.5 px-3 sm:px-4 lg:px-5 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg font-medium",children:[e.jsx(Qe,{className:"ml-2 text-sm"}),e.jsx("span",{className:"hidden sm:inline lg:inline",children:"إضافة مستخدم"}),e.jsx("span",{className:"sm:hidden lg:hidden",children:"إضافة"})]})]})]})})}),te&&e.jsxs("div",{className:"bg-success-100 dark:bg-success-900/30 text-success-700 dark:text-success-300 p-4 rounded-xl mb-4 shadow-soft flex items-center",children:[e.jsx(be,{className:"ml-2"}),te]}),le&&e.jsxs("div",{className:"bg-danger-100 dark:bg-danger-900/30 text-danger-700 dark:text-danger-300 p-4 rounded-xl mb-4 shadow-soft flex items-center",children:[e.jsx(W,{className:"ml-2"}),le]}),e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-4 mb-6 border border-gray-200 dark:border-gray-700",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800/30",children:[e.jsx("div",{className:"bg-primary-100 dark:bg-primary-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(Q,{className:"text-primary-600 dark:text-primary-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-primary-700 dark:text-primary-300 mb-1",children:"إجمالي المستخدمين"}),e.jsx("div",{className:"text-xl font-bold text-primary-600 dark:text-primary-400",children:R.totalUsers})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-success-50 dark:bg-success-900/20 rounded-lg border border-success-100 dark:border-success-800/30",children:[e.jsx("div",{className:"bg-success-100 dark:bg-success-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(E,{className:"text-success-600 dark:text-success-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-success-700 dark:text-success-300 mb-1",children:"المستخدمين النشطين"}),e.jsx("div",{className:"text-xl font-bold text-success-600 dark:text-success-400",children:R.activeUsers})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-100 dark:border-purple-800/30",children:[e.jsx("div",{className:"bg-purple-100 dark:bg-purple-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(L,{className:"text-purple-600 dark:text-purple-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-purple-700 dark:text-purple-300 mb-1",children:"المديرين"}),e.jsx("div",{className:"text-xl font-bold text-purple-600 dark:text-purple-400",children:R.adminUsers})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30",children:[e.jsx("div",{className:"bg-blue-100 dark:bg-blue-900/40 p-2.5 rounded-lg flex-shrink-0",children:e.jsx(ue,{className:"text-blue-600 dark:text-blue-400 text-lg"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-xs font-medium text-blue-700 dark:text-blue-300 mb-1",children:"الكاشيرين"}),e.jsx("div",{className:"text-xl font-bold text-blue-600 dark:text-blue-400",children:R.cashierUsers})]})]})]})}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft p-6 mb-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"relative",children:[e.jsx(ye,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"}),e.jsx("input",{type:"text",placeholder:"البحث بالاسم أو اسم المستخدم أو البريد الإلكتروني...",value:u,onChange:r=>ve(r.target.value),onKeyDown:r=>r.key==="Enter"&&me(),className:"w-full pr-10 pl-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"})]})}),e.jsxs("div",{className:"flex gap-2 flex-wrap",children:[e.jsxs("button",{onClick:()=>Ce(!G),className:`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center ${G?"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:[e.jsx(Ve,{className:"ml-2"}),"فلاتر"]}),e.jsxs("button",{onClick:me,className:"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 border border-primary-200 dark:border-primary-800 hover:bg-primary-200 dark:hover:bg-primary-900/50 px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center",children:[e.jsx(ye,{className:"ml-2"}),"بحث"]})]})]}),G&&e.jsx("div",{className:"mt-4 p-6 bg-gray-50 dark:bg-gray-700 rounded-xl border border-gray-200 dark:border-gray-600",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx("div",{className:"w-full",children:e.jsx(je,{label:"عدد العناصر",name:"itemsPerPage",value:_.toString(),onChange:r=>$e(parseInt(r,10)),options:[{value:"10",label:"10"},{value:"20",label:"20"},{value:"30",label:"30"},{value:"50",label:"50"}]})}),e.jsx("div",{className:"w-full",children:e.jsx(je,{label:"الدور",name:"role",value:A,onChange:r=>Se(r),options:[{value:"",label:"جميع الأدوار"},{value:"admin",label:"مدير"},{value:"cashier",label:"كاشير"}]})}),e.jsxs("div",{className:"w-full",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"المستخدمين النشطين"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center",children:e.jsx(V,{id:"activeOnly",checked:M,onChange:r=>{de(r),r&&ie(!1)},label:"النشطين فقط"})})]}),e.jsxs("div",{className:"w-full",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"المستخدمين غير النشطين"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 h-[42px] flex items-center",children:e.jsx(V,{id:"inactiveOnly",checked:O,onChange:r=>{ie(r),r&&de(!1)},label:"غير النشطين فقط"})})]})]})})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-soft overflow-hidden border border-gray-200 dark:border-gray-700",children:[$?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-14 w-14 border-b-3 border-primary-600 dark:border-primary-400"})}):ne.length>0?e.jsx("div",{className:"overflow-x-auto custom-scrollbar",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-700",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"المستخدم"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"البريد الإلكتروني"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الدور"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الحالة"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e.jsxs("div",{className:"flex items-center justify-end gap-1",children:[e.jsx("span",{children:"عدد المبيعات"}),e.jsx(We,{className:"text-primary-500 dark:text-primary-400",title:"مرتب من الأعلى إلى الأقل"})]})}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"تاريخ الإنشاء"}),e.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"الإجراءات"})]})}),e.jsx("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:ne.map(r=>e.jsxs("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.jsx("div",{className:"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center",children:e.jsx(E,{className:"text-primary-600 dark:text-primary-400"})})}),e.jsxs("div",{className:"mr-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:r.full_name}),e.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["@",r.username]})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:r.email||"-"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.role==="admin"?e.jsxs("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300",children:[e.jsx(L,{className:"ml-1"}),"مدير"]}):e.jsxs("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",children:[e.jsx(ue,{className:"ml-1"}),"كاشير"]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${r.is_active?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"}`,children:r.is_active?e.jsxs(e.Fragment,{children:[e.jsx(be,{className:"ml-1"}),"نشط"]}):e.jsxs(e.Fragment,{children:[e.jsx(Xe,{className:"ml-1"}),"غير نشط"]})})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"flex items-center",children:(()=>{const l=Ee()[r.id]||0,o=r.sales_count||0,{icon:j,bgColor:Ke,textColor:Ye,label:ge}=Ue(o,l);return e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${Ke} ${Ye} cursor-help`,title:`${ge} - ${o} ${o===1?"مبيعة":"مبيعات"}${l>0?` (المركز ${l})`:""}`,children:[e.jsx("span",{className:"ml-1",children:j}),o]}),l>0&&l<=3&&e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400 font-medium",children:ge})]})})()})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",children:Re(r.created_at)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>Oe(r),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300",title:"تعديل",children:e.jsx(Ze,{})}),e.jsx("button",{onClick:()=>Ae(r.id),className:"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300",title:"تغيير كلمة المرور",children:e.jsx(k,{})}),(n==null?void 0:n.id)!==r.id&&e.jsx("button",{onClick:()=>qe(r),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300",title:"حذف",children:e.jsx(er,{})})]})})]},r.id))})]})}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx(Q,{className:"mx-auto text-4xl text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:u?"لم يتم العثور على مستخدمين مطابقين":"لا يوجد مستخدمين"})]}),p.length>0&&e.jsx("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[e.jsx("button",{onClick:()=>P(i-1),disabled:i===1,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"السابق"}),e.jsx("button",{onClick:()=>P(i+1),disabled:i===F,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"التالي"})]}),e.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:[e.jsx("span",{className:"font-medium",children:i})," / ",e.jsx("span",{className:"font-medium",children:F})," ",e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["(الإجمالي: ",p.length,")"]})]})}),e.jsx("div",{children:e.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[e.jsxs("button",{onClick:()=>P(i-1),disabled:i===1,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx("span",{className:"sr-only",children:"السابق"}),e.jsx(rr,{className:"h-5 w-5","aria-hidden":"true"})]}),Array.from({length:F},(r,s)=>s+1).map(r=>r===1||r===F||r>=i-1&&r<=i+1?e.jsx("button",{onClick:()=>P(r),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${r===i?"z-10 bg-primary-50 dark:bg-primary-900/30 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-300":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:r},r):r===i-2||r===i+2?e.jsx("span",{className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300",children:"..."},r):null),e.jsxs("button",{onClick:()=>P(i+1),disabled:i===F,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx("span",{className:"sr-only",children:"التالي"}),e.jsx(sr,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]})]})})]}),e.jsx(X,{isOpen:z,onClose:q,title:C?"تعديل مستخدم":"إضافة مستخدم جديد",size:"md",children:e.jsxs("form",{onSubmit:Le,className:"space-y-5",children:[e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[e.jsx(E,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"المعلومات الأساسية"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsx(w,{label:"اسم المستخدم",name:"username",value:a.username,onChange:r=>{g({...a,username:r}),d.username&&m({...d,username:""})},placeholder:"أدخل اسم المستخدم",required:!0,error:d.username,icon:e.jsx(E,{})}),e.jsx(w,{label:"الاسم الكامل",name:"full_name",value:a.full_name,onChange:r=>{g({...a,full_name:r}),d.full_name&&m({...d,full_name:""})},placeholder:"أدخل الاسم الكامل",required:!0,error:d.full_name,icon:e.jsx(E,{})})]}),e.jsx("div",{className:"mt-4",children:e.jsx(w,{label:"البريد الإلكتروني",name:"email",type:"email",value:a.email,onChange:r=>{g({...a,email:r}),d.email&&m({...d,email:""})},placeholder:"أدخل البريد الإلكتروني",error:d.email,dir:"ltr"})})]}),!C&&e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[e.jsx(k,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"كلمة المرور"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsx(w,{label:"كلمة المرور",name:"password",type:h?"text":"password",value:a.password,onChange:r=>{g({...a,password:r}),d.password&&m({...d,password:""})},placeholder:"أدخل كلمة المرور",required:!0,error:d.password,dir:"ltr",icon:e.jsx(k,{})}),e.jsx(w,{label:"تأكيد كلمة المرور",name:"confirm_password",type:h?"text":"password",value:a.confirm_password,onChange:r=>{g({...a,confirm_password:r}),d.confirm_password&&m({...d,confirm_password:""})},placeholder:"أعد إدخال كلمة المرور",required:!0,error:d.confirm_password,dir:"ltr",icon:e.jsx(k,{})})]}),e.jsx("div",{className:"mt-4 flex items-center",children:e.jsx("button",{type:"button",onClick:()=>ee(!h),className:"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 flex items-center",children:h?"إخفاء كلمة المرور":"عرض كلمة المرور"})})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[e.jsx(L,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"الدور والحالة"]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsx(or,{label:"الدور",name:"role",value:a.role,onChange:r=>g({...a,role:r}),options:[{value:"admin",label:"مدير"},{value:"cashier",label:"كاشير"}],icon:e.jsx(L,{})}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"حالة المستخدم"}),e.jsx("div",{className:"bg-white dark:bg-gray-800 p-3 rounded-xl border-2 border-gray-300 dark:border-gray-600 h-[50px] flex items-center",children:e.jsx(V,{id:"is_active",checked:a.is_active,onChange:r=>g({...a,is_active:r}),label:"المستخدم نشط"})})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsx("button",{type:"button",onClick:q,className:"px-6 py-2.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-medium",children:"إلغاء"}),e.jsx("button",{type:"submit",className:"px-6 py-2.5 bg-primary-600 text-white rounded-xl hover:bg-primary-700 dark:hover:bg-primary-500 transition-colors font-medium",children:C?"تحديث المستخدم":"إضافة المستخدم"})]})]})}),e.jsx(X,{isOpen:we,onClose:()=>D(!1),title:"تغيير كلمة المرور",size:"sm",children:e.jsxs("form",{onSubmit:ze,className:"space-y-5",children:[e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-100 dark:border-gray-600",children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[e.jsx(k,{className:"ml-2 text-primary-600 dark:text-primary-400"}),"كلمة المرور الجديدة"]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(w,{label:"كلمة المرور الجديدة",name:"newPassword",type:h?"text":"password",value:S,onChange:r=>{re(r),d.newPassword&&m({...d,newPassword:""})},placeholder:"أدخل كلمة المرور الجديدة",required:!0,error:d.newPassword,dir:"ltr",icon:e.jsx(k,{})}),e.jsx(w,{label:"تأكيد كلمة المرور الجديدة",name:"confirmNewPassword",type:h?"text":"password",value:se,onChange:r=>{ae(r),d.confirmNewPassword&&m({...d,confirmNewPassword:""})},placeholder:"أعد إدخال كلمة المرور الجديدة",required:!0,error:d.confirmNewPassword,dir:"ltr",icon:e.jsx(k,{})}),e.jsx("div",{className:"flex items-center",children:e.jsx("button",{type:"button",onClick:()=>ee(!h),className:"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 flex items-center",children:h?"إخفاء كلمة المرور":"عرض كلمة المرور"})})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700",children:[e.jsx("button",{type:"button",onClick:()=>D(!1),className:"px-6 py-2.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-medium",children:"إلغاء"}),e.jsx("button",{type:"submit",className:"px-6 py-2.5 bg-primary-600 text-white rounded-xl hover:bg-primary-700 dark:hover:bg-primary-500 transition-colors font-medium",children:"تغيير كلمة المرور"})]})]})}),e.jsx(cr,{isOpen:Ne,onClose:()=>I(!1),onConfirm:Be,title:"تأكيد حذف المستخدم",message:`هل أنت متأكد من حذف المستخدم "${f==null?void 0:f.full_name}"؟ لا يمكن التراجع عن هذا الإجراء.`,confirmText:"حذف المستخدم",cancelText:"إلغاء"})]})};export{ur as default};
