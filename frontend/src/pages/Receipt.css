/* Receipt.css - Estilos mejorados para la factura */

/* Estilos generales */
.receipt-container {
  font-family: 'Cairo', '<PERSON><PERSON>wal', 'Arial', sans-serif;
  direction: rtl;
  color: #333;
}

/* Tamaños de factura */
.receipt-size-selector {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.receipt-size-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 18px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.receipt-size-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.receipt-size-button.active {
  background-color: var(--color-primary-100);
  color: var(--color-primary-700);
  border: 1px solid var(--color-primary-300);
  box-shadow: 0 2px 4px rgba(var(--color-primary-rgb), 0.2);
}

/* Estilos para diferentes tamaños de factura */
.receipt-wrapper {
  margin: 0 auto 40px;
  background-color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #eaeaea;
}

/* Contenido de la factura */
.receipt-content {
  position: relative;
  font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif;
}

/* Asegurar que todos los elementos usen Cairo */
.receipt-content *,
.receipt-header *,
.receipt-info *,
.receipt-items *,
.receipt-totals *,
.receipt-payment *,
.receipt-footer *,
.receipt-notes * {
  font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif !important;
}

/* Marca de agua */
.receipt-content::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  width: 80%;
  height: 80%;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm17 8H4v8h16v-8zm0-2V5H4v4h16zm-5 4h4v2h-4v-2z' fill='rgba(0,0,0,0.03)'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 50%;
  opacity: 0.1;
  pointer-events: none;
  z-index: 0;
}

/* Tamaño pequeño (58mm) */
.receipt-size-small {
  width: 58mm;
  font-size: 10px;
  padding: 8mm;
  font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif;
}

.receipt-size-small .receipt-header h1 {
  font-size: 16px;
  margin-bottom: 8px;
}

.receipt-size-small .receipt-header p {
  font-size: 9px;
  margin-bottom: 3px;
}

.receipt-size-small table th,
.receipt-size-small table td {
  padding: 3px;
  font-size: 9px;
}

.receipt-size-small .qr-image {
  width: 80px;
  height: 80px;
}

.receipt-size-small .qr-container {
  padding: 4px;
}

/* Tamaño mediano (80mm) */
.receipt-size-medium {
  width: 80mm;
  font-size: 12px;
  padding: 10mm;
  font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif;
}

.receipt-size-medium .receipt-header h1 {
  font-size: 18px;
  margin-bottom: 10px;
}

.receipt-size-medium .receipt-header p {
  font-size: 11px;
  margin-bottom: 4px;
}

.receipt-size-medium table th,
.receipt-size-medium table td {
  padding: 4px;
  font-size: 11px;
}

.receipt-size-medium .qr-image {
  width: 100px;
  height: 100px;
}

.receipt-size-medium .qr-container {
  padding: 6px;
}

/* Tamaño A4 */
.receipt-size-a4 {
  width: 210mm;
  max-width: 100%;
  font-size: 14px;
  padding: 20mm;
  font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif;
}

.receipt-size-a4 .receipt-header h1 {
  font-size: 28px;
  margin-bottom: 12px;
}

.receipt-size-a4 .receipt-header p {
  font-size: 16px;
  margin-bottom: 6px;
}

.receipt-size-a4 table th,
.receipt-size-a4 table td {
  padding: 10px;
  font-size: 15px;
}

.receipt-size-a4 .qr-image {
  width: 140px;
  height: 140px;
}

.receipt-size-a4 .qr-container {
  padding: 10px;
}

/* Estilos para impresión */
@media print {
  body {
    background-color: white;
    margin: 0;
    padding: 0;
    font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif !important;
    color: #000000 !important;
  }

  .print-controls {
    display: none !important;
  }

  .receipt-size-selector {
    display: none !important;
  }

  .receipt-wrapper {
    box-shadow: none;
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin: 0;
    border: none;
    border-radius: 0;
    background-color: white !important;
  }

  /* Forzar colores negros para impresión térmica */
  .receipt-content,
  .receipt-content *,
  .receipt-header,
  .receipt-header *,
  .receipt-info,
  .receipt-info *,
  .receipt-customer,
  .receipt-customer *,
  .receipt-items,
  .receipt-items *,
  .receipt-totals,
  .receipt-totals *,
  .receipt-payment,
  .receipt-payment *,
  .receipt-footer,
  .receipt-footer *,
  .receipt-notes,
  .receipt-notes * {
    color: #000000 !important;
    background-color: transparent !important;
    border-color: #000000 !important;
  }

  /* إخفاء جميع النوافذ المودال والعناصر التفاعلية عند الطباعة */
  .modal-overlay,
  .modal-container,
  [role="dialog"],
  .print-controls,
  .receipt-size-selector,
  .print\\:hidden {
    display: none !important;
    visibility: hidden !important;
  }

  /* Ajustes específicos para cada tamaño al imprimir */
  .receipt-size-small {
    width: 58mm;
    font-size: 10px;
    font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif !important;
  }

  .receipt-size-medium {
    width: 80mm;
    font-size: 12px;
    font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif !important;
  }

  /* Eliminar separadores en impresión térmica para 58mm y 80mm */
  .receipt-size-small .receipt-section,
  .receipt-size-medium .receipt-section {
    border-bottom: none !important;
    margin-bottom: 8px !important;
    padding-bottom: 4px !important;
  }

  /* Mantener solo separadores esenciales */
  .receipt-size-small .receipt-items,
  .receipt-size-medium .receipt-items {
    border-top: 1px dashed #000000 !important;
    border-bottom: 1px dashed #000000 !important;
    margin: 8px 0 !important;
    padding: 8px 0 !important;
  }

  .receipt-size-small .receipt-totals,
  .receipt-size-medium .receipt-totals {
    border-top: 1px solid #000000 !important;
    border-bottom: none !important;
    margin: 8px 0 !important;
    padding: 8px 0 4px 0 !important;
  }

  .receipt-size-a4 {
    width: 210mm;
    font-size: 14px;
    font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif !important;
  }

  /* Asegurar que todos los elementos usen Cairo en impresión */
  .receipt-content *,
  .receipt-header *,
  .receipt-info *,
  .receipt-items *,
  .receipt-totals *,
  .receipt-payment *,
  .receipt-footer *,
  .receipt-notes * {
    font-family: 'Cairo', 'Tajawal', 'Arial', sans-serif !important;
  }

  /* Ocultar elementos no necesarios en impresión */
  .print\\:hidden {
    display: none !important;
  }

  /* Ajustar márgenes para impresión */
  .receipt-content {
    margin: 0;
    padding: 8px;
  }

  /* QR Code optimizado para impresión térmica */
  .receipt-qr {
    page-break-inside: avoid;
    margin-top: 12px;
    text-align: center;
  }

  .receipt-size-small .receipt-qr,
  .receipt-size-medium .receipt-qr {
    margin-top: 8px;
  }

  .qr-container {
    background-color: white !important;
    border: none !important;
    padding: 0 !important;
    display: inline-block;
    box-shadow: none !important;
  }

  .receipt-size-small .qr-container,
  .receipt-size-medium .qr-container {
    padding: 0 !important;
    border: none !important;
  }

  .qr-image {
    display: block !important;
    margin: 0 auto !important;
    border: none !important;
    box-shadow: none !important;
  }

  .receipt-size-small .qr-image {
    width: 120px !important;
    height: 120px !important;
  }

  .receipt-size-medium .qr-image {
    width: 140px !important;
    height: 140px !important;
  }

  .receipt-size-a4 .qr-image {
    width: 160px !important;
    height: 160px !important;
  }

  .qr-info {
    color: #000000 !important;
    font-size: 8px !important;
    margin-top: 4px !important;
  }

  .receipt-size-small .qr-info {
    font-size: 7px !important;
  }

  /* Evitar salto de página en elementos críticos */
  .receipt-header,
  .receipt-info,
  .receipt-customer,
  .receipt-totals,
  .receipt-payment {
    page-break-inside: avoid;
  }

  /* Asegurar que todo el contenido se mantenga junto */
  .receipt-size-small .receipt-content,
  .receipt-size-medium .receipt-content {
    page-break-inside: avoid;
  }
}

/* Estilos para la cabecera */
.receipt-header {
  position: relative;
  z-index: 1;
}

.receipt-header h1 {
  color: var(--color-primary-700);
  font-weight: 700;
}

.receipt-header h2 {
  color: var(--color-secondary-700);
  margin-top: 8px;
}

/* Estilos para la información de la factura */
.receipt-info {
  background-color: rgba(var(--color-primary-rgb), 0.03);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

/* Estilos para la información del cliente */
.receipt-customer {
  background-color: rgba(var(--color-secondary-rgb), 0.03);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  border: 1px solid rgba(var(--color-secondary-rgb), 0.1);
  position: relative;
  z-index: 1;
}

/* Estilos para la tabla de productos */
.receipt-items {
  position: relative;
  z-index: 1;
}

.receipt-items-table {
  width: 100%;
  border-collapse: collapse;
}

.receipt-items-table th {
  background-color: rgba(var(--color-secondary-rgb), 0.05);
  border-bottom: 2px solid var(--color-primary-300);
  padding: 8px 4px;
  font-weight: 700;
  color: var(--color-secondary-700);
}

.receipt-items-table td {
  border-bottom: 1px solid #f0f0f0;
  padding: 6px 4px;
}

.receipt-items-table tr:last-child td {
  border-bottom: none;
}

.receipt-items-table tr:hover td {
  background-color: rgba(var(--color-primary-rgb), 0.02);
}

/* Estilos para los totales */
.receipt-totals {
  margin-top: 16px;
  background-color: rgba(var(--color-secondary-rgb), 0.03);
  border-radius: 8px;
  padding: 12px;
  position: relative;
  z-index: 1;
}

.receipt-totals .total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  padding: 4px 0;
}

.receipt-totals .grand-total {
  font-weight: 700;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 2px dashed var(--color-primary-200);
  color: var(--color-primary-700);
  font-size: 1.1em;
}

/* Estilos para la información de pago */
.receipt-payment {
  margin-top: 16px;
  position: relative;
  z-index: 1;
}

/* Estilos para el pie de página */
.receipt-footer {
  margin-top: 24px;
  text-align: center;
  font-size: 0.9em;
  color: var(--color-secondary-600);
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  position: relative;
  z-index: 1;
}

/* Estilos para el código QR */
.receipt-qr {
  margin: 24px auto 0;
  text-align: center;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.qr-container {
  background-color: white;
  padding: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.qr-image {
  width: 140px;
  height: 140px;
  display: block;
}

/* أحجام QR Code حسب حجم الفاتورة في العرض العادي */
.receipt-size-small .qr-image {
  width: 120px;
  height: 120px;
}

.receipt-size-medium .qr-image {
  width: 140px;
  height: 140px;
}

.receipt-size-a4 .qr-image {
  width: 160px;
  height: 160px;
}

.qr-info {
  text-align: center;
  max-width: 200px;
}

/* Estilos para el código QR placeholder */
.receipt-qr .qr-placeholder {
  width: 120px;
  height: 120px;
  margin: 0 auto;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 1px dashed #ddd;
}

/* Estilos para el sello */
.receipt-stamp {
  position: absolute;
  bottom: 60px;
  right: 40px;
  width: 100px;
  height: 100px;
  background-color: rgba(var(--color-primary-rgb), 0.05);
  border: 2px solid rgba(var(--color-primary-rgb), 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(-15deg);
  font-weight: 700;
  color: rgba(var(--color-primary-rgb), 0.3);
  font-size: 14px;
  z-index: 1;
}

/* Estilos para modo oscuro */
.dark .receipt-wrapper {
  background-color: #1a1f2b;
  border-color: #2d3748;
  color: #e2e8f0;
}

.dark .receipt-content::before {
  opacity: 0.05;
}

.dark .receipt-header h1 {
  color: var(--color-primary-300);
}

.dark .receipt-header h2 {
  color: var(--color-secondary-300);
}

.dark .receipt-header p {
  color: #cbd5e0;
}

.dark .receipt-info {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border: 1px solid #2d3748;
}

.dark .receipt-info p {
  color: #cbd5e0;
}

.dark .receipt-info strong {
  color: #e2e8f0;
}

.dark .receipt-items {
  border-top: 1px dashed #2d3748;
  border-bottom: 1px dashed #2d3748;
}

.dark .receipt-items-table th {
  background-color: #2d3748;
  border-bottom-color: var(--color-primary-600);
  color: #e2e8f0;
}

.dark .receipt-items-table td {
  border-bottom-color: #2d3748;
  color: #cbd5e0;
}

.dark .receipt-items-table tr:last-child td {
  border-bottom: none;
}

.dark .receipt-items-table tr:hover td {
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.dark .receipt-totals {
  background-color: rgba(var(--color-secondary-rgb), 0.1);
  border: 1px solid #2d3748;
}

.dark .receipt-totals .total-row {
  color: #cbd5e0;
}

.dark .receipt-totals .grand-total {
  border-top-color: var(--color-primary-600);
  color: var(--color-primary-300);
}

.dark .receipt-payment {
  background-color: rgba(var(--color-primary-rgb), 0.05);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #2d3748;
}

.dark .receipt-payment .total-row {
  color: #cbd5e0;
}

.dark .receipt-notes {
  background-color: #2d3748 !important;
  border: 1px solid #4a5568;
}

.dark .receipt-notes p {
  color: #cbd5e0 !important;
}

.dark .receipt-footer {
  color: #a0aec0;
  border-top-color: #2d3748;
}

.dark .receipt-qr .qr-placeholder {
  background-color: #2d3748;
  border-color: #4a5568;
}

.dark .qr-container {
  background-color: #2d3748;
  border-color: #4a5568;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dark .receipt-qr p {
  color: #a0aec0 !important;
}

.dark .receipt-stamp {
  background-color: rgba(var(--color-primary-rgb), 0.15);
  border-color: rgba(var(--color-primary-rgb), 0.3);
  color: rgba(var(--color-primary-rgb), 0.5);
}

/* Separadores y mejoras adicionales */
.receipt-section {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px dashed #e2e8f0;
}

.dark .receipt-section {
  border-bottom-color: #2d3748;
}

.receipt-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

/* Estilos para descuentos */
.discount-badge {
  display: inline-block;
  background-color: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success-600);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.85em;
  margin-right: 4px;
}

.dark .discount-badge {
  background-color: rgba(var(--color-success-rgb), 0.2);
  color: var(--color-success-400);
}

.discount-row {
  color: var(--color-success-600);
}

.dark .discount-row {
  color: var(--color-success-400);
}

/* Estilos para información del cliente */
.receipt-customer h3 {
  color: var(--color-secondary-700);
  margin-bottom: 8px;
}

.dark .receipt-customer {
  background-color: rgba(var(--color-secondary-rgb), 0.1);
  border: 1px solid #2d3748;
}

.dark .receipt-customer h3 {
  color: var(--color-secondary-300);
}

.dark .receipt-customer p {
  color: #cbd5e0;
}

.dark .receipt-customer strong {
  color: #e2e8f0;
}

/* Estilos para monto de deuda */
.debt-row {
  background-color: rgba(220, 38, 38, 0.05) !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  margin: 4px 0 !important;
  border: 1px solid rgba(220, 38, 38, 0.1) !important;
}

.dark .debt-row {
  background-color: rgba(220, 38, 38, 0.15) !important;
  border-color: rgba(220, 38, 38, 0.3) !important;
}

/* Estilos para estado de pago */
.payment-status-paid {
  color: #16a34a !important;
  font-weight: 600;
}

.payment-status-partial {
  color: #ea580c !important;
  font-weight: 600;
}

.payment-status-credit {
  color: #dc2626 !important;
  font-weight: 600;
}

.dark .payment-status-paid {
  color: #22c55e !important;
}

.dark .payment-status-partial {
  color: #fb923c !important;
}

.dark .payment-status-credit {
  color: #ef4444 !important;
}

/* Optimizaciones adicionales para impresión térmica */
@media print {
  /* Asegurar contraste máximo para impresoras térmicas */
  .receipt-size-small *,
  .receipt-size-medium * {
    color: #000000 !important;
    background-color: transparent !important;
    text-shadow: none !important;
    box-shadow: none !important;
  }

  /* Bordes más gruesos para mejor visibilidad */
  .receipt-size-small .receipt-items-table th,
  .receipt-size-medium .receipt-items-table th {
    border-bottom: 2px solid #000000 !important;
  }

  .receipt-size-small .receipt-totals .grand-total,
  .receipt-size-medium .receipt-totals .grand-total {
    border-top: 2px solid #000000 !important;
  }

  /* Espaciado optimizado para impresión térmica */
  .receipt-size-small .receipt-section,
  .receipt-size-medium .receipt-section {
    margin-bottom: 6px !important;
    padding-bottom: 2px !important;
  }

  .receipt-size-small .receipt-items-table td,
  .receipt-size-medium .receipt-items-table td {
    padding: 3px 2px !important;
  }

  .receipt-size-small .receipt-totals .total-row,
  .receipt-size-medium .receipt-totals .total-row {
    padding: 2px 0 !important;
    margin-bottom: 3px !important;
  }

  /* Texto más grueso para mejor legibilidad */
  .receipt-size-small strong,
  .receipt-size-medium strong,
  .receipt-size-small .grand-total,
  .receipt-size-medium .grand-total {
    font-weight: 900 !important;
  }

  /* Eliminar efectos visuales innecesarios */
  .receipt-size-small .receipt-info,
  .receipt-size-medium .receipt-info,
  .receipt-size-small .receipt-customer,
  .receipt-size-medium .receipt-customer,
  .receipt-size-small .receipt-totals,
  .receipt-size-medium .receipt-totals,
  .receipt-size-small .receipt-payment,
  .receipt-size-medium .receipt-payment {
    background-color: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
  }

  /* Optimización específica para QR Code en impresión térmica */
  .receipt-size-small .qr-info,
  .receipt-size-medium .qr-info {
    font-size: 6px !important;
    line-height: 1.2 !important;
    margin-top: 2px !important;
  }

  /* Asegurar que el QR Code mantenga su calidad */
  .qr-image {
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
    image-rendering: pixelated !important;
  }

  /* Espaciado mínimo para QR Code */
  .receipt-size-small .receipt-qr,
  .receipt-size-medium .receipt-qr {
    margin-top: 6px !important;
    margin-bottom: 4px !important;
  }

  /* دعم النسخ المتعددة */
  .receipt-copies-container {
    display: none;
  }

  .receipt-copy {
    page-break-before: always;
    page-break-after: always;
  }

  .receipt-copy:first-child {
    page-break-before: auto;
  }

  .receipt-copy:last-child {
    page-break-after: auto;
  }

  /* إخفاء النسخ الإضافية في العرض العادي */
  @media screen {
    .receipt-copies-container {
      display: none !important;
    }
  }

  /* إظهار النسخ الإضافية عند الطباعة */
  @media print {
    .receipt-copies-container {
      display: block !important;
    }
  }
}
