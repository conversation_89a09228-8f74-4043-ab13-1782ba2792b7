# إعداد SmartPOS للوصول من الشبكة المحلية

## المشكلة
عند محاولة الوصول للتطبيق من جهاز آخر في الشبكة المحلية، يظهر التطبيق ولكن تسجيل الدخول لا يعمل.

## الحل
تم تحديث إعدادات التطبيق لدعم الوصول من الشبكة المحلية:

### 1. إعدادات Backend (الخادم الخلفي)
- ✅ تم تحديث CORS لدعم عناوين IP المحلية
- ✅ تم تغيير host من `localhost` إلى `0.0.0.0`
- ✅ دعم نطاقات الشبكة المحلية: 192.168.x.x, 10.x.x.x, 172.16.x.x

### 2. إعدادات Frontend (الواجهة الأمامية)
- ✅ تم إضافة كشف تلقائي لعنوان IP
- ✅ تم تحديث Vite للعمل على `0.0.0.0`
- ✅ تم تحسين إعدادات CORS

## كيفية الاستخدام

### 1. تشغيل الخادم الخلفي
```bash
cd backend
python main.py
```
الخادم سيعمل على: `http://0.0.0.0:8002`

### 2. تشغيل الواجهة الأمامية
```bash
cd frontend
npm run dev
```
الواجهة ستعمل على: `http://0.0.0.0:5175`

### 3. الوصول من أجهزة أخرى
1. اكتشف عنوان IP للجهاز المضيف:
   ```bash
   # في Windows
   ipconfig
   
   # في Linux/Mac
   ifconfig
   # أو
   ip addr show
   ```

2. افتح المتصفح في الجهاز الآخر واذهب إلى:
   ```
   http://[IP_ADDRESS]:5175
   ```
   مثال: `http://*************:5175`

## مثال عملي
إذا كان عنوان IP للجهاز المضيف هو `*************`:

- **الخادم الخلفي**: `http://*************:8002`
- **الواجهة الأمامية**: `http://*************:5175`

التطبيق سيكتشف تلقائياً العنوان الصحيح ويتصل بالخادم الخلفي.

## استكشاف الأخطاء

### 1. إذا لم يعمل تسجيل الدخول:
- تأكد من أن الخادم الخلفي يعمل على `0.0.0.0:8002`
- تحقق من إعدادات الجدار الناري (Firewall)
- تأكد من أن المنافذ 8002 و 5175 مفتوحة

### 2. إذا لم يظهر التطبيق:
- تحقق من عنوان IP الصحيح
- تأكد من أن الأجهزة على نفس الشبكة
- جرب إعادة تشغيل الخادمين

### 3. مشاكل CORS:
- تحقق من console المتصفح للأخطاء
- تأكد من أن إعدادات CORS محدثة في `backend/main.py`

## الأمان
⚠️ **تحذير**: هذه الإعدادات مخصصة للتطوير والشبكة المحلية فقط. 
لا تستخدمها في بيئة الإنتاج بدون إعدادات أمان إضافية.

## دعم إضافي
إذا واجهت مشاكل، تحقق من:
1. إعدادات الشبكة
2. إعدادات الجدار الناري
3. سجلات الأخطاء في console المتصفح
4. سجلات الخادم الخلفي
