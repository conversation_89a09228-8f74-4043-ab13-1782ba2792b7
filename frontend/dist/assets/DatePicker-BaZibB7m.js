import{r as g,j as e}from"./index-CWk4ynxp.js";import{y as R,a1 as L,a2 as A}from"./index-DcZcZtna.js";const P=({value:c,onChange:y,name:D,placeholder:k="اختر تاريخ",className:w="",label:h})=>{const[u,l]=g.useState(!1),[s,p]=g.useState(c?new Date(c):new Date),x=g.useRef(null);g.useEffect(()=>{const t=r=>{x.current&&!x.current.contains(r.target)&&l(!1)};return document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}},[]);const j=["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سب<PERSON>م<PERSON><PERSON>","أكتوبر","نوفمبر","ديسمبر"],v=["أحد","اثنين","ثلاثاء","أربعاء","خميس","جمعة","سبت"],N=(t,r)=>new Date(t,r+1,0).getDate(),M=(t,r)=>new Date(t,r,1).getDay(),f=t=>{const r=t.getFullYear(),n=String(t.getMonth()+1).padStart(2,"0"),o=String(t.getDate()).padStart(2,"0");return`${r}-${n}-${o}`},b=t=>{if(!t)return null;const[r,n,o]=t.split("-").map(Number);return new Date(r,n-1,o)},S=t=>{const r=new Date(s);r.setDate(t),y(f(r)),l(!1)},C=()=>{p(new Date(s.getFullYear(),s.getMonth()-1,1))},F=()=>{p(new Date(s.getFullYear(),s.getMonth()+1,1))},$=()=>{const t=s.getFullYear(),r=s.getMonth(),n=N(t,r),o=M(t,r),m=[];for(let a=0;a<o;a++)m.push(e.jsx("div",{className:"h-8 w-8"},`empty-${a}`));const i=b(c);for(let a=1;a<=n;a++){const d=new Date(t,r,a),E=i&&d.getDate()===i.getDate()&&d.getMonth()===i.getMonth()&&d.getFullYear()===i.getFullYear(),O=new Date().toDateString()===d.toDateString();m.push(e.jsx("button",{type:"button",onClick:()=>S(a),className:`h-8 w-8 rounded-full flex items-center justify-center text-sm transition-colors
            ${E?"bg-primary-600 text-white":O?"bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-300":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"}`,children:a},a))}return m},Y=()=>{if(!c)return"";const t=b(c);if(!t)return"";const r=String(t.getDate()).padStart(2,"0"),n=String(t.getMonth()+1).padStart(2,"0"),o=t.getFullYear();return`${r}/${n}/${o}`};return e.jsxs("div",{className:"relative",ref:x,children:[h&&e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:h}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",readOnly:!0,value:Y(),placeholder:k,onClick:()=>l(!u),className:`w-full border border-gray-300 dark:border-gray-600 rounded-lg py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 cursor-pointer ${w}`}),e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",onClick:()=>l(!u),children:e.jsx(R,{className:"text-gray-400 dark:text-gray-500"})}),e.jsx("input",{type:"hidden",name:D,value:c})]}),u&&e.jsxs("div",{className:"absolute z-10 mt-1 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 right-0",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("button",{type:"button",onClick:C,className:"p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400",children:e.jsx(L,{className:"h-4 w-4"})}),e.jsxs("div",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[j[s.getMonth()]," ",s.getFullYear()]}),e.jsx("button",{type:"button",onClick:F,className:"p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400",children:e.jsx(A,{className:"h-4 w-4"})})]}),e.jsx("div",{className:"grid grid-cols-7 gap-1 mb-2",children:v.map((t,r)=>e.jsx("div",{className:"h-8 w-8 flex items-center justify-center text-xs text-gray-500 dark:text-gray-400",children:t.charAt(0)},r))}),e.jsx("div",{className:"grid grid-cols-7 gap-1",children:$()}),e.jsxs("div",{className:"mt-3 flex justify-between",children:[e.jsx("button",{type:"button",onClick:()=>{y(""),l(!1)},className:"text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300",children:"مسح"}),e.jsx("button",{type:"button",onClick:()=>{y(f(new Date)),l(!1)},className:"text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300",children:"اليوم"})]})]})]})};export{P as D};
