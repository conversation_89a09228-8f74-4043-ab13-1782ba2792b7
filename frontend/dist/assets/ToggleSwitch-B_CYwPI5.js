import{j as r}from"./index-B6Ptcst5.js";const f=({id:t,checked:e,onChange:s,label:a,className:n=""})=>r.jsx("div",{className:`flex items-center ${n}`,children:r.jsxs("label",{className:"inline-flex items-center cursor-pointer",children:[r.jsx("input",{type:"checkbox",id:t,checked:e,onChange:i=>s(i.target.checked),className:"sr-only peer"}),r.jsx("div",{className:`
          relative w-11 h-6 rounded-full transition-all duration-300 ease-in-out
          ${e?"bg-primary-600 dark:bg-primary-500":"bg-gray-200 dark:bg-gray-600"}
          peer-focus:outline-none peer-focus:ring-4
          ${e?"peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800":"peer-focus:ring-gray-300 dark:peer-focus:ring-gray-600"}
          after:content-[''] after:absolute after:top-[2px] after:start-[2px]
          after:bg-white dark:after:bg-gray-100 after:border after:rounded-full
          after:h-5 after:w-5 after:transition-all after:duration-300 after:ease-in-out
          ${e?"after:translate-x-full rtl:after:-translate-x-full after:border-white dark:after:border-gray-100":"after:border-gray-300 dark:after:border-gray-500"}
        `}),a&&r.jsx("span",{className:"mr-3 text-sm font-medium text-gray-700 dark:text-gray-300",children:a})]})});export{f as T};
