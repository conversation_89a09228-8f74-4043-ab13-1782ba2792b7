import { create } from 'zustand';
import api from '../lib/axios';
import { getCurrentTripoliDateTime } from '../services/dateTimeService';
import { currentPeriodService } from '../services/currentPeriodService';
import { previousPeriodService } from '../services/previousPeriodService';

// تعريف أنواع البيانات
export interface SalesTrend {
  date: string;
  amount: number;
}

export interface ProductCategorySales {
  name: string;
  value: number;
  count?: number;
}

export interface ProductCategoriesResponse {
  categories: ProductCategorySales[];
  inactive_products: number;
}

export interface InventoryStatus {
  name: string;
  quantity: number;
  total_quantity?: number;
  value: number;
}

export interface TopProduct {
  id: number;
  name: string;
  quantity: number;
  total: number;
}

export interface RecentSale {
  id: number;
  total: number;
  createdAt: string;
  items: number;
  user?: {
    id: number;
    username: string;
    full_name: string;
  };
}

export interface SystemStats {
  totalUsers: number;
  activeUsers: number;
  lastLogin: string;
  systemUptime: string;
  lastBackup: string;
  databaseSize: string;
}

export interface ReportSummary {
  totalSales: number;
  totalItems: number;
  averageSale: number;
  totalRevenue: number;
  todaySales: number;
  productCount: number;
  lowStockCount: number;
}

export type ReportPeriod = 'day' | 'week' | 'month' | 'year';
export type ReportType = 'sales' | 'products' | 'inventory' | 'customers' | 'system' | 'daily-users';

interface ReportsStore {
  // بيانات التقارير
  salesTrends: SalesTrend[];
  previousPeriodSales: SalesTrend[];
  previousPeriodTotal: number;
  productCategories: ProductCategorySales[];
  inactiveProducts: number;
  inventoryStatus: InventoryStatus[];
  reportSummary: ReportSummary;
  topProducts: TopProduct[];
  recentSales: RecentSale[];
  systemStats: SystemStats;

  // حالة التحميل والأخطاء
  isLoading: boolean;
  error: string | null;

  // الفلاتر والإعدادات
  selectedPeriod: ReportPeriod;
  selectedReportType: ReportType;

  // الوظائف
  fetchSalesTrends: (period: ReportPeriod) => Promise<void>;
  fetchPreviousPeriodTotal: (period: ReportPeriod) => Promise<void>;
  fetchProductCategories: () => Promise<void>;
  fetchInventoryStatus: () => Promise<void>;
  fetchSystemStats: () => Promise<void>;
  fetchDashboardStats: () => Promise<void>;
  setReportType: (type: ReportType) => void;
  setPeriod: (period: ReportPeriod) => void;
}

// القيم الافتراضية
const initialSummary: ReportSummary = {
  totalSales: 0,
  totalItems: 0,
  averageSale: 0,
  totalRevenue: 0,
  todaySales: 0,
  productCount: 0,
  lowStockCount: 0
};

const initialSystemStats: SystemStats = {
  totalUsers: 0,
  activeUsers: 0,
  lastLogin: 'غير متاح',
  systemUptime: '0 أيام',
  lastBackup: 'لم يتم النسخ الاحتياطي بعد',
  databaseSize: '0 MB'
};

// إنشاء متجر البيانات
export const useReportsStore = create<ReportsStore>((set, get) => ({
  // البيانات الافتراضية
  salesTrends: [],
  previousPeriodSales: [],
  previousPeriodTotal: 0,
  productCategories: [],
  inactiveProducts: 0,
  inventoryStatus: [],
  reportSummary: initialSummary,
  topProducts: [],
  recentSales: [],
  systemStats: initialSystemStats,

  // حالة التحميل والأخطاء
  isLoading: false,
  error: null,

  // الفلاتر والإعدادات الافتراضية
  selectedPeriod: 'day',
  selectedReportType: 'sales',

  // وظيفة جلب بيانات اتجاهات المبيعات باستخدام الخدمات المنفصلة
  fetchSalesTrends: async (period: ReportPeriod) => {
    try {
      set({ isLoading: true, error: null });

      console.log(`جلب بيانات المبيعات للفترة: ${period} باستخدام الخدمات المنفصلة`);

      // جلب بيانات الفترة الحالية والسابقة بشكل متوازي باستخدام الخدمات المنفصلة
      const [currentData, previousData] = await Promise.all([
        currentPeriodService.getSalesByPeriod(period),
        previousPeriodService.getSalesByPeriod(period)
      ]);

      console.log('تم جلب البيانات من الخدمات المنفصلة بنجاح');
      console.log(`بيانات الفترة الحالية (${period}):`, currentData);
      console.log(`بيانات الفترة السابقة (${period}):`, previousData);

      // التحقق من صحة البيانات وتنسيقها
      const normalizedCurrentData = currentPeriodService.formatDataForChart(currentData);
      const normalizedPreviousData = previousPeriodService.formatDataForChart(previousData);

      console.log(`تم تنسيق ${normalizedCurrentData.length} عنصر للفترة الحالية`);
      console.log(`تم تنسيق ${normalizedPreviousData.length} عنصر للفترة السابقة`);

      // حساب ملخص التقرير
      const totalSales = normalizedCurrentData.length;
      const totalAmount = normalizedCurrentData.reduce((sum: number, item: SalesTrend) => sum + item.amount, 0);
      const averageSale = totalSales > 0 ? totalAmount / totalSales : 0;

      // الحصول على بيانات لوحة المعلومات الحالية
      const currentSummary = get().reportSummary;

      set({
        salesTrends: normalizedCurrentData,
        previousPeriodSales: normalizedPreviousData,
        isLoading: false,
        selectedPeriod: period,
        reportSummary: {
          ...currentSummary,
          totalSales,
          totalItems: totalSales,
          averageSale,
          totalRevenue: totalAmount
        }
      });

    } catch (error) {
      console.error('خطأ في جلب بيانات المبيعات باستخدام الخدمات المنفصلة:', error);

      // في حالة فشل جلب البيانات، استخدم الخدمات لإنشاء بيانات فارغة
      console.log('محاولة إنشاء بيانات فارغة باستخدام الخدمات...');

      try {
        // محاولة الحصول على بيانات فارغة من الخدمات
        const emptyCurrentData = await currentPeriodService.getSalesByPeriod(period).catch(() => []);
        const emptyPreviousData = await previousPeriodService.getSalesByPeriod(period).catch(() => []);

        // إذا فشلت الخدمات، أنشئ بيانات فارغة يدوياً
        let finalCurrentData = emptyCurrentData;
        let finalPreviousData = emptyPreviousData;

        if (emptyCurrentData.length === 0 || emptyPreviousData.length === 0) {
          console.log('إنشاء بيانات فارغة يدوياً للفترة:', period);

          if (period === 'day') {
            // 24 ساعة بقيم صفر
            finalCurrentData = Array.from({ length: 24 }, (_, hour) => ({
              date: `${hour.toString().padStart(2, '0')}:00`,
              amount: 0
            }));
            finalPreviousData = [...finalCurrentData];
          } else if (period === 'week') {
            // 7 أيام بقيم صفر
            finalCurrentData = Array.from({ length: 7 }, (_, day) => {
              const date = new Date();
              date.setDate(date.getDate() - (6 - day));
              return {
                date: date.toISOString().split('T')[0],
                amount: 0
              };
            });
            finalPreviousData = [...finalCurrentData];
          } else if (period === 'month') {
            // 30 يوم بقيم صفر
            finalCurrentData = Array.from({ length: 30 }, (_, day) => {
              const date = new Date();
              date.setDate(date.getDate() - (29 - day));
              return {
                date: date.toISOString().split('T')[0],
                amount: 0
              };
            });
            finalPreviousData = [...finalCurrentData];
          } else if (period === 'year') {
            // 12 شهر بقيم صفر
            finalCurrentData = Array.from({ length: 12 }, (_, month) => {
              const date = new Date();
              date.setMonth(date.getMonth() - (11 - month));
              return {
                date: `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`,
                amount: 0
              };
            });
            finalPreviousData = [...finalCurrentData];
          }
        }

        set({
          salesTrends: finalCurrentData,
          previousPeriodSales: finalPreviousData,
          isLoading: false,
          selectedPeriod: period,
          reportSummary: {
            totalSales: 0,
            totalItems: 0,
            averageSale: 0,
            totalRevenue: 0,
            todaySales: 0,
            productCount: 0,
            lowStockCount: 0
          },
          error: 'فشل في جلب البيانات من الخادم - تم عرض بيانات فارغة'
        });

      } catch (fallbackError) {
        console.error('خطأ في إنشاء البيانات الفارغة:', fallbackError);

        // في حالة فشل كل شيء، استخدم بيانات فارغة أساسية
        set({
          salesTrends: [],
          previousPeriodSales: [],
          isLoading: false,
          selectedPeriod: period,
          reportSummary: {
            totalSales: 0,
            totalItems: 0,
            averageSale: 0,
            totalRevenue: 0,
            todaySales: 0,
            productCount: 0,
            lowStockCount: 0
          },
          error: 'خطأ شديد في جلب البيانات'
        });
      }
    }
  },

  // وظيفة جلب إجمالي الفترة السابقة باستخدام الخدمة المنفصلة
  fetchPreviousPeriodTotal: async (period: ReportPeriod) => {
    try {
      console.log(`جلب إجمالي الفترة السابقة للفترة: ${period} باستخدام الخدمة المنفصلة`);

      // استخدام خدمة الفترة السابقة لجلب الإجمالي
      const previousTotal = await previousPeriodService.getPreviousPeriodTotal(period);

      console.log(`إجمالي الفترة السابقة للفترة ${period}: ${previousTotal}`);

      set({
        previousPeriodTotal: previousTotal
      });

    } catch (error) {
      console.error('خطأ في جلب إجمالي الفترة السابقة:', error);

      // في حالة الخطأ، استخدم قيمة 0
      set({
        previousPeriodTotal: 0
      });
    }
  },

  // وظيفة جلب بيانات فئات المنتجات
  fetchProductCategories: async () => {
    try {
      set({ isLoading: true, error: null });

      // استخدام نقطة نهاية API الجديدة لفئات المنتجات
      const response = await api.get('/api/dashboard/product-categories');

      set({
        productCategories: response.data.categories || response.data,
        inactiveProducts: response.data.inactive_products || 0,
        isLoading: false
      });

    } catch (error) {
      console.error('Error fetching product categories:', error);

      // في حالة عدم وجود بيانات، استخدم بيانات فارغة
      set({
        productCategories: [],
        isLoading: false,
        error: 'فشل في جلب بيانات فئات المنتجات'
      });
    }
  },

  // وظيفة جلب بيانات حالة المخزون
  fetchInventoryStatus: async () => {
    try {
      set({ isLoading: true, error: null });

      // استخدام نقطة نهاية API الجديدة لحالة المخزون
      const response = await api.get('/api/dashboard/inventory-status');

      console.log('Fetched inventory status:', response.data);

      set({
        inventoryStatus: response.data,
        isLoading: false
      });

    } catch (error) {
      console.error('Error fetching inventory status:', error);

      // في حالة عدم وجود بيانات، استخدم بيانات فارغة
      set({
        inventoryStatus: [],
        isLoading: false,
        error: 'فشل في جلب بيانات حالة المخزون'
      });
    }
  },

  // وظيفة جلب بيانات النظام
  fetchSystemStats: async () => {
    try {
      set({ isLoading: true, error: null });

      // استخدام نقطة نهاية API الجديدة لبيانات النظام
      const response = await api.get('/api/dashboard/system-stats');

      set({
        systemStats: response.data,
        isLoading: false
      });

    } catch (error) {
      console.error('Error fetching system stats:', error);

      // في حالة عدم وجود بيانات، استخدم بيانات فارغة
      const mockData = {
        totalUsers: 0,
        activeUsers: 0,
        lastLogin: 'غير متاح',
        systemUptime: 'غير متاح',
        lastBackup: 'غير متاح',
        databaseSize: '0 MB'
      };

      set({
        systemStats: mockData,
        isLoading: false,
        error: null
      });
    }
  },

  // وظيفة جلب بيانات لوحة المعلومات
  fetchDashboardStats: async () => {
    try {
      set({ isLoading: true, error: null });

      // استخدام نقطة نهاية API لبيانات لوحة المعلومات
      const response = await api.get('/api/dashboard/stats');
      const data = response.data;

      // تحديث ملخص التقرير
      const currentSummary = get().reportSummary;

      set({
        reportSummary: {
          ...currentSummary,
          totalRevenue: data.totalRevenue || 0,
          todaySales: data.todaySales || 0,
          productCount: data.productCount || 0,
          lowStockCount: data.lowStockCount || 0
        },
        topProducts: data.topProducts || [],
        recentSales: data.recentSales || [],
        isLoading: false
      });

    } catch (error) {
      console.error('Error fetching dashboard stats:', error);

      // في حالة عدم وجود نقطة نهاية API، استخدم بيانات وهمية
      const mockTopProducts = [
        { id: 1, name: 'هاتف ذكي', quantity: 15, total: 7500 },
        { id: 2, name: 'لابتوب', quantity: 8, total: 12000 },
        { id: 3, name: 'سماعات لاسلكية', quantity: 25, total: 2500 },
        { id: 4, name: 'شاحن متنقل', quantity: 30, total: 1800 },
        { id: 5, name: 'ساعة ذكية', quantity: 12, total: 3600 }
      ];

      const mockRecentSales = [
        { id: 101, total: 1250, createdAt: getCurrentTripoliDateTime().toISOString(), items: 3 },
        { id: 102, total: 850, createdAt: getCurrentTripoliDateTime().toISOString(), items: 2 },
        { id: 103, total: 2100, createdAt: getCurrentTripoliDateTime().toISOString(), items: 5 },
        { id: 104, total: 450, createdAt: getCurrentTripoliDateTime().toISOString(), items: 1 },
        { id: 105, total: 1800, createdAt: getCurrentTripoliDateTime().toISOString(), items: 4 }
      ];

      set({
        topProducts: mockTopProducts,
        recentSales: mockRecentSales,
        isLoading: false,
        error: null
      });
    }
  },

  // وظائف تعيين نوع التقرير والفترة
  setReportType: (type: ReportType) => {
    set({ selectedReportType: type });

    // جلب البيانات المناسبة بناءً على نوع التقرير
    const store = get();
    if (type === 'sales') {
      store.fetchSalesTrends(store.selectedPeriod);
      store.fetchDashboardStats();
    } else if (type === 'products') {
      store.fetchProductCategories();
    } else if (type === 'inventory') {
      store.fetchInventoryStatus();
    } else if (type === 'system') {
      store.fetchSystemStats();
    } else if (type === 'daily-users') {
      // لا نحتاج لجلب بيانات إضافية هنا لأن البيانات تُجلب في الصفحة مباشرة
    }
  },

  setPeriod: (period: ReportPeriod) => {
    set({ selectedPeriod: period });
    get().fetchSalesTrends(period);
  }
}));
