import{j as e}from"./index-B6Ptcst5.js";import{I as i}from"./index-CD3RUED3.js";const f=({isOpen:r,onClose:a,title:o,children:s,size:l="md",zIndex:t="base"})=>{if(!r)return null;const d={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-none w-full h-full","mobile-full":"w-full h-full md:max-w-6xl md:h-auto"},m={base:"z-50",high:"z-60",highest:"z-70"};return e.jsxs("div",{className:`fixed inset-0 ${m[t]} overflow-y-auto modal-scrollbar`,children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:a}),e.jsx("div",{className:`flex min-h-full items-center justify-center ${l==="full"||l==="mobile-full"?"p-2 md:p-4":"p-4"}`,children:e.jsxs("div",{className:`relative w-full ${d[l]} transform overflow-hidden ${l==="full"||l==="mobile-full"?"rounded-lg md:rounded-2xl":"rounded-lg"} bg-white dark:bg-gray-800 shadow-xl transition-all ${l==="full"||l==="mobile-full"?"flex flex-col":""}`,children:[e.jsxs("div",{className:`flex items-center justify-between ${l==="full"||l==="mobile-full"?"p-4 md:p-6":"p-6"} border-b border-gray-200 dark:border-gray-700 ${l==="full"||l==="mobile-full"?"flex-shrink-0":""}`,children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:o}),e.jsx("button",{onClick:a,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700",children:e.jsx(i,{className:"h-5 w-5"})})]}),e.jsx("div",{className:`${l==="full"||l==="mobile-full"?"p-4 md:p-6 flex-1 overflow-hidden":"p-6"}`,children:s})]})})]})};export{f as M};
