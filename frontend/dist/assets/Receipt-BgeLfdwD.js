import{f as Lt,a as Dt,r as R,j as l,b as K}from"./index-BtFmrjEm.js";import{O as mt,e as X,D as Ft,an as kt}from"./index-BEpVSoxh.js";var V={},Ut=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},xt={},B={};let lt;const zt=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];B.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return t*4+17};B.getSymbolTotalCodewords=function(t){return zt[t]};B.getBCHDigit=function(e){let t=0;for(;e!==0;)t++,e>>>=1;return t};B.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');lt=t};B.isKanjiModeEnabled=function(){return typeof lt<"u"};B.toSJIS=function(t){return lt(t)};var Q={};(function(e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2};function t(r){if(typeof r!="string")throw new Error("Param is not a string");switch(r.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+r)}}e.isValid=function(o){return o&&typeof o.bit<"u"&&o.bit>=0&&o.bit<4},e.from=function(o,n){if(e.isValid(o))return o;try{return t(o)}catch{return n}}})(Q);function Nt(){this.buffer=[],this.length=0}Nt.prototype={get:function(e){const t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)===1},put:function(e,t){for(let r=0;r<t;r++)this.putBit((e>>>t-r-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(e){const t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};var $t=Nt;function H(e){if(!e||e<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}H.prototype.set=function(e,t,r,o){const n=e*this.size+t;this.data[n]=r,o&&(this.reservedBit[n]=!0)};H.prototype.get=function(e,t){return this.data[e*this.size+t]};H.prototype.xor=function(e,t,r){this.data[e*this.size+t]^=r};H.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]};var Vt=H,bt={};(function(e){const t=B.getSymbolSize;e.getRowColCoords=function(o){if(o===1)return[];const n=Math.floor(o/7)+2,s=t(o),i=s===145?26:Math.ceil((s-13)/(2*n-2))*2,c=[s-7];for(let a=1;a<n-1;a++)c[a]=c[a-1]-i;return c.push(6),c.reverse()},e.getPositions=function(o){const n=[],s=e.getRowColCoords(o),i=s.length;for(let c=0;c<i;c++)for(let a=0;a<i;a++)c===0&&a===0||c===0&&a===i-1||c===i-1&&a===0||n.push([s[c],s[a]]);return n}})(bt);var Et={};const Ht=B.getSymbolSize,pt=7;Et.getPositions=function(t){const r=Ht(t);return[[0,0],[r-pt,0],[0,r-pt]]};var Ct={};(function(e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t={N1:3,N2:3,N3:40,N4:10};e.isValid=function(n){return n!=null&&n!==""&&!isNaN(n)&&n>=0&&n<=7},e.from=function(n){return e.isValid(n)?parseInt(n,10):void 0},e.getPenaltyN1=function(n){const s=n.size;let i=0,c=0,a=0,h=null,d=null;for(let E=0;E<s;E++){c=a=0,h=d=null;for(let y=0;y<s;y++){let p=n.get(E,y);p===h?c++:(c>=5&&(i+=t.N1+(c-5)),h=p,c=1),p=n.get(y,E),p===d?a++:(a>=5&&(i+=t.N1+(a-5)),d=p,a=1)}c>=5&&(i+=t.N1+(c-5)),a>=5&&(i+=t.N1+(a-5))}return i},e.getPenaltyN2=function(n){const s=n.size;let i=0;for(let c=0;c<s-1;c++)for(let a=0;a<s-1;a++){const h=n.get(c,a)+n.get(c,a+1)+n.get(c+1,a)+n.get(c+1,a+1);(h===4||h===0)&&i++}return i*t.N2},e.getPenaltyN3=function(n){const s=n.size;let i=0,c=0,a=0;for(let h=0;h<s;h++){c=a=0;for(let d=0;d<s;d++)c=c<<1&2047|n.get(h,d),d>=10&&(c===1488||c===93)&&i++,a=a<<1&2047|n.get(d,h),d>=10&&(a===1488||a===93)&&i++}return i*t.N3},e.getPenaltyN4=function(n){let s=0;const i=n.data.length;for(let a=0;a<i;a++)s+=n.data[a];return Math.abs(Math.ceil(s*100/i/5)-10)*t.N4};function r(o,n,s){switch(o){case e.Patterns.PATTERN000:return(n+s)%2===0;case e.Patterns.PATTERN001:return n%2===0;case e.Patterns.PATTERN010:return s%3===0;case e.Patterns.PATTERN011:return(n+s)%3===0;case e.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(s/3))%2===0;case e.Patterns.PATTERN101:return n*s%2+n*s%3===0;case e.Patterns.PATTERN110:return(n*s%2+n*s%3)%2===0;case e.Patterns.PATTERN111:return(n*s%3+(n+s)%2)%2===0;default:throw new Error("bad maskPattern:"+o)}}e.applyMask=function(n,s){const i=s.size;for(let c=0;c<i;c++)for(let a=0;a<i;a++)s.isReserved(a,c)||s.xor(a,c,r(n,a,c))},e.getBestMask=function(n,s){const i=Object.keys(e.Patterns).length;let c=0,a=1/0;for(let h=0;h<i;h++){s(h),e.applyMask(h,n);const d=e.getPenaltyN1(n)+e.getPenaltyN2(n)+e.getPenaltyN3(n)+e.getPenaltyN4(n);e.applyMask(h,n),d<a&&(a=d,c=h)}return c}})(Ct);var G={};const v=Q,O=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],J=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];G.getBlocksCount=function(t,r){switch(r){case v.L:return O[(t-1)*4+0];case v.M:return O[(t-1)*4+1];case v.Q:return O[(t-1)*4+2];case v.H:return O[(t-1)*4+3];default:return}};G.getTotalCodewordsCount=function(t,r){switch(r){case v.L:return J[(t-1)*4+0];case v.M:return J[(t-1)*4+1];case v.Q:return J[(t-1)*4+2];case v.H:return J[(t-1)*4+3];default:return}};var jt={},W={};const z=new Uint8Array(512),Y=new Uint8Array(256);(function(){let t=1;for(let r=0;r<255;r++)z[r]=t,Y[t]=r,t<<=1,t&256&&(t^=285);for(let r=255;r<512;r++)z[r]=z[r-255]})();W.log=function(t){if(t<1)throw new Error("log("+t+")");return Y[t]};W.exp=function(t){return z[t]};W.mul=function(t,r){return t===0||r===0?0:z[Y[t]+Y[r]]};(function(e){const t=W;e.mul=function(o,n){const s=new Uint8Array(o.length+n.length-1);for(let i=0;i<o.length;i++)for(let c=0;c<n.length;c++)s[i+c]^=t.mul(o[i],n[c]);return s},e.mod=function(o,n){let s=new Uint8Array(o);for(;s.length-n.length>=0;){const i=s[0];for(let a=0;a<n.length;a++)s[a]^=t.mul(n[a],i);let c=0;for(;c<s.length&&s[c]===0;)c++;s=s.slice(c)}return s},e.generateECPolynomial=function(o){let n=new Uint8Array([1]);for(let s=0;s<o;s++)n=e.mul(n,new Uint8Array([1,t.exp(s)]));return n}})(jt);const _t=jt;function ut(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}ut.prototype.initialize=function(t){this.degree=t,this.genPoly=_t.generateECPolynomial(this.degree)};ut.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const r=new Uint8Array(t.length+this.degree);r.set(t);const o=_t.mod(r,this.genPoly),n=this.degree-o.length;if(n>0){const s=new Uint8Array(this.degree);return s.set(o,n),s}return o};var Kt=ut,At={},L={},dt={};dt.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40};var P={};const Bt="[0-9]+",Ot="[A-Z $%*+\\-./:]+";let $="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";$=$.replace(/u/g,"\\u");const Jt="(?:(?![A-Z0-9 $%*+\\-./:]|"+$+`)(?:.|[\r
]))+`;P.KANJI=new RegExp($,"g");P.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");P.BYTE=new RegExp(Jt,"g");P.NUMERIC=new RegExp(Bt,"g");P.ALPHANUMERIC=new RegExp(Ot,"g");const Yt=new RegExp("^"+$+"$"),qt=new RegExp("^"+Bt+"$"),Qt=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");P.testKanji=function(t){return Yt.test(t)};P.testNumeric=function(t){return qt.test(t)};P.testAlphanumeric=function(t){return Qt.test(t)};(function(e){const t=dt,r=P;e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(s,i){if(!s.ccBits)throw new Error("Invalid mode: "+s);if(!t.isValid(i))throw new Error("Invalid version: "+i);return i>=1&&i<10?s.ccBits[0]:i<27?s.ccBits[1]:s.ccBits[2]},e.getBestModeForData=function(s){return r.testNumeric(s)?e.NUMERIC:r.testAlphanumeric(s)?e.ALPHANUMERIC:r.testKanji(s)?e.KANJI:e.BYTE},e.toString=function(s){if(s&&s.id)return s.id;throw new Error("Invalid mode")},e.isValid=function(s){return s&&s.bit&&s.ccBits};function o(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+n)}}e.from=function(s,i){if(e.isValid(s))return s;try{return o(s)}catch{return i}}})(L);(function(e){const t=B,r=G,o=Q,n=L,s=dt,i=7973,c=t.getBCHDigit(i);function a(y,p,w){for(let x=1;x<=40;x++)if(p<=e.getCapacity(x,w,y))return x}function h(y,p){return n.getCharCountIndicator(y,p)+4}function d(y,p){let w=0;return y.forEach(function(x){const A=h(x.mode,p);w+=A+x.getBitsLength()}),w}function E(y,p){for(let w=1;w<=40;w++)if(d(y,w)<=e.getCapacity(w,p,n.MIXED))return w}e.from=function(p,w){return s.isValid(p)?parseInt(p,10):w},e.getCapacity=function(p,w,x){if(!s.isValid(p))throw new Error("Invalid QR Code version");typeof x>"u"&&(x=n.BYTE);const A=t.getSymbolTotalCodewords(p),f=r.getTotalCodewordsCount(p,w),g=(A-f)*8;if(x===n.MIXED)return g;const m=g-h(x,p);switch(x){case n.NUMERIC:return Math.floor(m/10*3);case n.ALPHANUMERIC:return Math.floor(m/11*2);case n.KANJI:return Math.floor(m/13);case n.BYTE:default:return Math.floor(m/8)}},e.getBestVersionForData=function(p,w){let x;const A=o.from(w,o.M);if(Array.isArray(p)){if(p.length>1)return E(p,A);if(p.length===0)return 1;x=p[0]}else x=p;return a(x.mode,x.getLength(),A)},e.getEncodedBits=function(p){if(!s.isValid(p)||p<7)throw new Error("Invalid QR Code version");let w=p<<12;for(;t.getBCHDigit(w)-c>=0;)w^=i<<t.getBCHDigit(w)-c;return p<<12|w}})(At);var St={};const st=B,Tt=1335,Gt=21522,yt=st.getBCHDigit(Tt);St.getEncodedBits=function(t,r){const o=t.bit<<3|r;let n=o<<10;for(;st.getBCHDigit(n)-yt>=0;)n^=Tt<<st.getBCHDigit(n)-yt;return(o<<10|n)^Gt};var It={};const Wt=L;function D(e){this.mode=Wt.NUMERIC,this.data=e.toString()}D.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)};D.prototype.getLength=function(){return this.data.length};D.prototype.getBitsLength=function(){return D.getBitsLength(this.data.length)};D.prototype.write=function(t){let r,o,n;for(r=0;r+3<=this.data.length;r+=3)o=this.data.substr(r,3),n=parseInt(o,10),t.put(n,10);const s=this.data.length-r;s>0&&(o=this.data.substr(r),n=parseInt(o,10),t.put(n,s*3+1))};var Zt=D;const Xt=L,tt=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function F(e){this.mode=Xt.ALPHANUMERIC,this.data=e}F.getBitsLength=function(t){return 11*Math.floor(t/2)+6*(t%2)};F.prototype.getLength=function(){return this.data.length};F.prototype.getBitsLength=function(){return F.getBitsLength(this.data.length)};F.prototype.write=function(t){let r;for(r=0;r+2<=this.data.length;r+=2){let o=tt.indexOf(this.data[r])*45;o+=tt.indexOf(this.data[r+1]),t.put(o,11)}this.data.length%2&&t.put(tt.indexOf(this.data[r]),6)};var te=F;const ee=L;function k(e){this.mode=ee.BYTE,typeof e=="string"?this.data=new TextEncoder().encode(e):this.data=new Uint8Array(e)}k.getBitsLength=function(t){return t*8};k.prototype.getLength=function(){return this.data.length};k.prototype.getBitsLength=function(){return k.getBitsLength(this.data.length)};k.prototype.write=function(e){for(let t=0,r=this.data.length;t<r;t++)e.put(this.data[t],8)};var ne=k;const re=L,oe=B;function U(e){this.mode=re.KANJI,this.data=e}U.getBitsLength=function(t){return t*13};U.prototype.getLength=function(){return this.data.length};U.prototype.getBitsLength=function(){return U.getBitsLength(this.data.length)};U.prototype.write=function(e){let t;for(t=0;t<this.data.length;t++){let r=oe.toSJIS(this.data[t]);if(r>=33088&&r<=40956)r-=33088;else if(r>=57408&&r<=60351)r-=49472;else throw new Error("Invalid SJIS character: "+this.data[t]+`
Make sure your charset is UTF-8`);r=(r>>>8&255)*192+(r&255),e.put(r,13)}};var se=U,Pt={exports:{}};(function(e){var t={single_source_shortest_paths:function(r,o,n){var s={},i={};i[o]=0;var c=t.PriorityQueue.make();c.push(o,0);for(var a,h,d,E,y,p,w,x,A;!c.empty();){a=c.pop(),h=a.value,E=a.cost,y=r[h]||{};for(d in y)y.hasOwnProperty(d)&&(p=y[d],w=E+p,x=i[d],A=typeof i[d]>"u",(A||x>w)&&(i[d]=w,c.push(d,w),s[d]=h))}if(typeof n<"u"&&typeof i[n]>"u"){var f=["Could not find a path from ",o," to ",n,"."].join("");throw new Error(f)}return s},extract_shortest_path_from_predecessor_list:function(r,o){for(var n=[],s=o;s;)n.push(s),r[s],s=r[s];return n.reverse(),n},find_path:function(r,o,n){var s=t.single_source_shortest_paths(r,o,n);return t.extract_shortest_path_from_predecessor_list(s,n)},PriorityQueue:{make:function(r){var o=t.PriorityQueue,n={},s;r=r||{};for(s in o)o.hasOwnProperty(s)&&(n[s]=o[s]);return n.queue=[],n.sorter=r.sorter||o.default_sorter,n},default_sorter:function(r,o){return r.cost-o.cost},push:function(r,o){var n={value:r,cost:o};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};e.exports=t})(Pt);var ie=Pt.exports;(function(e){const t=L,r=Zt,o=te,n=ne,s=se,i=P,c=B,a=ie;function h(f){return unescape(encodeURIComponent(f)).length}function d(f,g,m){const u=[];let N;for(;(N=f.exec(m))!==null;)u.push({data:N[0],index:N.index,mode:g,length:N[0].length});return u}function E(f){const g=d(i.NUMERIC,t.NUMERIC,f),m=d(i.ALPHANUMERIC,t.ALPHANUMERIC,f);let u,N;return c.isKanjiModeEnabled()?(u=d(i.BYTE,t.BYTE,f),N=d(i.KANJI,t.KANJI,f)):(u=d(i.BYTE_KANJI,t.BYTE,f),N=[]),g.concat(m,u,N).sort(function(j,S){return j.index-S.index}).map(function(j){return{data:j.data,mode:j.mode,length:j.length}})}function y(f,g){switch(g){case t.NUMERIC:return r.getBitsLength(f);case t.ALPHANUMERIC:return o.getBitsLength(f);case t.KANJI:return s.getBitsLength(f);case t.BYTE:return n.getBitsLength(f)}}function p(f){return f.reduce(function(g,m){const u=g.length-1>=0?g[g.length-1]:null;return u&&u.mode===m.mode?(g[g.length-1].data+=m.data,g):(g.push(m),g)},[])}function w(f){const g=[];for(let m=0;m<f.length;m++){const u=f[m];switch(u.mode){case t.NUMERIC:g.push([u,{data:u.data,mode:t.ALPHANUMERIC,length:u.length},{data:u.data,mode:t.BYTE,length:u.length}]);break;case t.ALPHANUMERIC:g.push([u,{data:u.data,mode:t.BYTE,length:u.length}]);break;case t.KANJI:g.push([u,{data:u.data,mode:t.BYTE,length:h(u.data)}]);break;case t.BYTE:g.push([{data:u.data,mode:t.BYTE,length:h(u.data)}])}}return g}function x(f,g){const m={},u={start:{}};let N=["start"];for(let b=0;b<f.length;b++){const j=f[b],S=[];for(let I=0;I<j.length;I++){const T=j[I],C=""+b+I;S.push(C),m[C]={node:T,lastCount:0},u[C]={};for(let M=0;M<N.length;M++){const _=N[M];m[_]&&m[_].node.mode===T.mode?(u[_][C]=y(m[_].lastCount+T.length,T.mode)-y(m[_].lastCount,T.mode),m[_].lastCount+=T.length):(m[_]&&(m[_].lastCount=T.length),u[_][C]=y(T.length,T.mode)+4+t.getCharCountIndicator(T.mode,g))}}N=S}for(let b=0;b<N.length;b++)u[N[b]].end=0;return{map:u,table:m}}function A(f,g){let m;const u=t.getBestModeForData(f);if(m=t.from(g,u),m!==t.BYTE&&m.bit<u.bit)throw new Error('"'+f+'" cannot be encoded with mode '+t.toString(m)+`.
 Suggested mode is: `+t.toString(u));switch(m===t.KANJI&&!c.isKanjiModeEnabled()&&(m=t.BYTE),m){case t.NUMERIC:return new r(f);case t.ALPHANUMERIC:return new o(f);case t.KANJI:return new s(f);case t.BYTE:return new n(f)}}e.fromArray=function(g){return g.reduce(function(m,u){return typeof u=="string"?m.push(A(u,null)):u.data&&m.push(A(u.data,u.mode)),m},[])},e.fromString=function(g,m){const u=E(g,c.isKanjiModeEnabled()),N=w(u),b=x(N,m),j=a.find_path(b.map,"start","end"),S=[];for(let I=1;I<j.length-1;I++)S.push(b.table[j[I]].node);return e.fromArray(p(S))},e.rawSplit=function(g){return e.fromArray(E(g,c.isKanjiModeEnabled()))}})(It);const Z=B,et=Q,ae=$t,ce=Vt,le=bt,ue=Et,it=Ct,at=G,de=Kt,q=At,fe=St,he=L,nt=It;function ge(e,t){const r=e.size,o=ue.getPositions(t);for(let n=0;n<o.length;n++){const s=o[n][0],i=o[n][1];for(let c=-1;c<=7;c++)if(!(s+c<=-1||r<=s+c))for(let a=-1;a<=7;a++)i+a<=-1||r<=i+a||(c>=0&&c<=6&&(a===0||a===6)||a>=0&&a<=6&&(c===0||c===6)||c>=2&&c<=4&&a>=2&&a<=4?e.set(s+c,i+a,!0,!0):e.set(s+c,i+a,!1,!0))}}function me(e){const t=e.size;for(let r=8;r<t-8;r++){const o=r%2===0;e.set(r,6,o,!0),e.set(6,r,o,!0)}}function pe(e,t){const r=le.getPositions(t);for(let o=0;o<r.length;o++){const n=r[o][0],s=r[o][1];for(let i=-2;i<=2;i++)for(let c=-2;c<=2;c++)i===-2||i===2||c===-2||c===2||i===0&&c===0?e.set(n+i,s+c,!0,!0):e.set(n+i,s+c,!1,!0)}}function ye(e,t){const r=e.size,o=q.getEncodedBits(t);let n,s,i;for(let c=0;c<18;c++)n=Math.floor(c/3),s=c%3+r-8-3,i=(o>>c&1)===1,e.set(n,s,i,!0),e.set(s,n,i,!0)}function rt(e,t,r){const o=e.size,n=fe.getEncodedBits(t,r);let s,i;for(s=0;s<15;s++)i=(n>>s&1)===1,s<6?e.set(s,8,i,!0):s<8?e.set(s+1,8,i,!0):e.set(o-15+s,8,i,!0),s<8?e.set(8,o-s-1,i,!0):s<9?e.set(8,15-s-1+1,i,!0):e.set(8,15-s-1,i,!0);e.set(o-8,8,1,!0)}function we(e,t){const r=e.size;let o=-1,n=r-1,s=7,i=0;for(let c=r-1;c>0;c-=2)for(c===6&&c--;;){for(let a=0;a<2;a++)if(!e.isReserved(n,c-a)){let h=!1;i<t.length&&(h=(t[i]>>>s&1)===1),e.set(n,c-a,h),s--,s===-1&&(i++,s=7)}if(n+=o,n<0||r<=n){n-=o,o=-o;break}}}function xe(e,t,r){const o=new ae;r.forEach(function(a){o.put(a.mode.bit,4),o.put(a.getLength(),he.getCharCountIndicator(a.mode,e)),a.write(o)});const n=Z.getSymbolTotalCodewords(e),s=at.getTotalCodewordsCount(e,t),i=(n-s)*8;for(o.getLengthInBits()+4<=i&&o.put(0,4);o.getLengthInBits()%8!==0;)o.putBit(0);const c=(i-o.getLengthInBits())/8;for(let a=0;a<c;a++)o.put(a%2?17:236,8);return Ne(o,e,t)}function Ne(e,t,r){const o=Z.getSymbolTotalCodewords(t),n=at.getTotalCodewordsCount(t,r),s=o-n,i=at.getBlocksCount(t,r),c=o%i,a=i-c,h=Math.floor(o/i),d=Math.floor(s/i),E=d+1,y=h-d,p=new de(y);let w=0;const x=new Array(i),A=new Array(i);let f=0;const g=new Uint8Array(e.buffer);for(let j=0;j<i;j++){const S=j<a?d:E;x[j]=g.slice(w,w+S),A[j]=p.encode(x[j]),w+=S,f=Math.max(f,S)}const m=new Uint8Array(o);let u=0,N,b;for(N=0;N<f;N++)for(b=0;b<i;b++)N<x[b].length&&(m[u++]=x[b][N]);for(N=0;N<y;N++)for(b=0;b<i;b++)m[u++]=A[b][N];return m}function be(e,t,r,o){let n;if(Array.isArray(e))n=nt.fromArray(e);else if(typeof e=="string"){let h=t;if(!h){const d=nt.rawSplit(e);h=q.getBestVersionForData(d,r)}n=nt.fromString(e,h||40)}else throw new Error("Invalid data");const s=q.getBestVersionForData(n,r);if(!s)throw new Error("The amount of data is too big to be stored in a QR Code");if(!t)t=s;else if(t<s)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+s+`.
`);const i=xe(t,r,n),c=Z.getSymbolSize(t),a=new ce(c);return ge(a,t),me(a),pe(a,t),rt(a,r,0),t>=7&&ye(a,t),we(a,i),isNaN(o)&&(o=it.getBestMask(a,rt.bind(null,a,r))),it.applyMask(o,a),rt(a,r,o),{modules:a,version:t,errorCorrectionLevel:r,maskPattern:o,segments:n}}xt.create=function(t,r){if(typeof t>"u"||t==="")throw new Error("No input text");let o=et.M,n,s;return typeof r<"u"&&(o=et.from(r.errorCorrectionLevel,et.M),n=q.from(r.version),s=it.from(r.maskPattern),r.toSJISFunc&&Z.setToSJISFunction(r.toSJISFunc)),be(t,n,o,s)};var Mt={},ft={};(function(e){function t(r){if(typeof r=="number"&&(r=r.toString()),typeof r!="string")throw new Error("Color should be defined as hex string");let o=r.slice().replace("#","").split("");if(o.length<3||o.length===5||o.length>8)throw new Error("Invalid hex color: "+r);(o.length===3||o.length===4)&&(o=Array.prototype.concat.apply([],o.map(function(s){return[s,s]}))),o.length===6&&o.push("F","F");const n=parseInt(o.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:n&255,hex:"#"+o.slice(0,6).join("")}}e.getOptions=function(o){o||(o={}),o.color||(o.color={});const n=typeof o.margin>"u"||o.margin===null||o.margin<0?4:o.margin,s=o.width&&o.width>=21?o.width:void 0,i=o.scale||4;return{width:s,scale:s?4:i,margin:n,color:{dark:t(o.color.dark||"#000000ff"),light:t(o.color.light||"#ffffffff")},type:o.type,rendererOpts:o.rendererOpts||{}}},e.getScale=function(o,n){return n.width&&n.width>=o+n.margin*2?n.width/(o+n.margin*2):n.scale},e.getImageWidth=function(o,n){const s=e.getScale(o,n);return Math.floor((o+n.margin*2)*s)},e.qrToImageData=function(o,n,s){const i=n.modules.size,c=n.modules.data,a=e.getScale(i,s),h=Math.floor((i+s.margin*2)*a),d=s.margin*a,E=[s.color.light,s.color.dark];for(let y=0;y<h;y++)for(let p=0;p<h;p++){let w=(y*h+p)*4,x=s.color.light;if(y>=d&&p>=d&&y<h-d&&p<h-d){const A=Math.floor((y-d)/a),f=Math.floor((p-d)/a);x=E[c[A*i+f]?1:0]}o[w++]=x.r,o[w++]=x.g,o[w++]=x.b,o[w]=x.a}}})(ft);(function(e){const t=ft;function r(n,s,i){n.clearRect(0,0,s.width,s.height),s.style||(s.style={}),s.height=i,s.width=i,s.style.height=i+"px",s.style.width=i+"px"}function o(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}e.render=function(s,i,c){let a=c,h=i;typeof a>"u"&&(!i||!i.getContext)&&(a=i,i=void 0),i||(h=o()),a=t.getOptions(a);const d=t.getImageWidth(s.modules.size,a),E=h.getContext("2d"),y=E.createImageData(d,d);return t.qrToImageData(y.data,s,a),r(E,h,d),E.putImageData(y,0,0),h},e.renderToDataURL=function(s,i,c){let a=c;typeof a>"u"&&(!i||!i.getContext)&&(a=i,i=void 0),a||(a={});const h=e.render(s,i,a),d=a.type||"image/png",E=a.rendererOpts||{};return h.toDataURL(d,E.quality)}})(Mt);var Rt={};const Ee=ft;function wt(e,t){const r=e.a/255,o=t+'="'+e.hex+'"';return r<1?o+" "+t+'-opacity="'+r.toFixed(2).slice(1)+'"':o}function ot(e,t,r){let o=e+t;return typeof r<"u"&&(o+=" "+r),o}function Ce(e,t,r){let o="",n=0,s=!1,i=0;for(let c=0;c<e.length;c++){const a=Math.floor(c%t),h=Math.floor(c/t);!a&&!s&&(s=!0),e[c]?(i++,c>0&&a>0&&e[c-1]||(o+=s?ot("M",a+r,.5+h+r):ot("m",n,0),n=0,s=!1),a+1<t&&e[c+1]||(o+=ot("h",i),i=0)):n++}return o}Rt.render=function(t,r,o){const n=Ee.getOptions(r),s=t.modules.size,i=t.modules.data,c=s+n.margin*2,a=n.color.light.a?"<path "+wt(n.color.light,"fill")+' d="M0 0h'+c+"v"+c+'H0z"/>':"",h="<path "+wt(n.color.dark,"stroke")+' d="'+Ce(i,s,n.margin)+'"/>',d='viewBox="0 0 '+c+" "+c+'"',y='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+d+' shape-rendering="crispEdges">'+a+h+`</svg>
`;return typeof o=="function"&&o(null,y),y};const je=Ut,ct=xt,vt=Mt,_e=Rt;function ht(e,t,r,o,n){const s=[].slice.call(arguments,1),i=s.length,c=typeof s[i-1]=="function";if(!c&&!je())throw new Error("Callback required as last argument");if(c){if(i<2)throw new Error("Too few arguments provided");i===2?(n=r,r=t,t=o=void 0):i===3&&(t.getContext&&typeof n>"u"?(n=o,o=void 0):(n=o,o=r,r=t,t=void 0))}else{if(i<1)throw new Error("Too few arguments provided");return i===1?(r=t,t=o=void 0):i===2&&!t.getContext&&(o=r,r=t,t=void 0),new Promise(function(a,h){try{const d=ct.create(r,o);a(e(d,t,o))}catch(d){h(d)}})}try{const a=ct.create(r,o);n(null,e(a,t,o))}catch(a){n(a)}}V.create=ct.create;V.toCanvas=ht.bind(null,vt.render);V.toDataURL=ht.bind(null,vt.renderToDataURL);V.toString=ht.bind(null,function(e,t,r){return _e.render(e,r)});const Se=()=>{const{id:e}=Lt(),t=Dt(),[r,o]=R.useState(null),[n,s]=R.useState({name:"",address:"",phone:"",email:"",tax_rate:"0",receipt_header:"",receipt_footer:"",currency_symbol:"د.ل"}),[i,c]=R.useState(!0),[a,h]=R.useState(null),[d,E]=R.useState("medium"),[y,p]=R.useState("");R.useEffect(()=>{(async()=>{c(!0),h(null);try{const g=parseInt(e||"0");if(isNaN(g)||g<=0)throw new Error("رقم الفاتورة غير صالح");const u=(await K.get(`/api/sales/${g}`)).data;if(!u||!u.items||!Array.isArray(u.items))throw new Error("بيانات الفاتورة غير صالحة");const N=await Promise.all(u.items.map(async C=>{try{const M=await K.get(`/api/products/${C.product_id}`);return{...C,product_name:M.data.name||`منتج #${C.product_id}`,price:C.unit_price}}catch{return{...C,product_name:`منتج #${C.product_id}`,price:C.unit_price}}}));let b="الكاشير";if(u.user_id)try{const C=await K.get(`/api/users/${u.user_id}`);b=C.data.full_name||C.data.username||"الكاشير"}catch{console.log("Could not fetch user data, using default cashier name"),u.user&&u.user.full_name?b=u.user.full_name:u.user&&u.user.username&&(b=u.user.username)}else u.user&&(b=u.user.full_name||u.user.username||"الكاشير");const j=u.discount_amount||0,I=u.total_amount-j+(u.tax_amount||0),T={...u,items:N,payment_amount:I,change_amount:0,cashier_name:b};o(T),s({name:"Smart POS",address:"ليبيا، طرابلس",phone:"+218 91-234-5678",email:"<EMAIL>",tax_rate:"0",receipt_header:"شكراً لاختيارك Smart POS",receipt_footer:"نتمنى لك يوماً سعيداً",currency_symbol:"د.ل"});try{const C=await K.get("/api/settings");if(C.data&&Array.isArray(C.data)){const M=C.data,_={};M.forEach(gt=>{_[gt.key]=gt.value}),Object.keys(_).length>0&&s({name:_.store_name||"Smart POS",address:_.store_address||"ليبيا، طرابلس",phone:_.store_phone||"+218 91-234-5678",email:_.store_email||"<EMAIL>",tax_rate:_.tax_rate||"0",receipt_header:_.receipt_header||"شكراً لاختيارك Smart POS",receipt_footer:_.receipt_footer||"نتمنى لك يوماً سعيداً",currency_symbol:_.currency_symbol||"د.ل"})}}catch{console.log("Settings API not available, using default store info")}c(!1)}catch(g){console.error("Error fetching receipt data:",g),h("فشل في تحميل بيانات الفاتورة"),c(!1)}})()},[e]),R.useEffect(()=>{(async()=>{if(r&&n.phone)try{const g=r.discount_amount||0,u=r.total_amount-g+(r.tax_amount||0),N=`هاتف المتجر: ${n.phone}
فاتورة رقم: ${r.id}
المبلغ: ${u.toFixed(2)} ${n.currency_symbol}${g>0?`
الخصم: ${g.toFixed(2)} ${n.currency_symbol}`:""}
التاريخ: ${x(r.created_at)}
الكاشير: ${r.cashier_name||"غير محدد"}`,b=await V.toDataURL(N,{width:120,margin:2,color:{dark:"#000000",light:"#FFFFFF"}});p(b)}catch(g){console.error("Error generating QR code:",g)}})()},[r,n]);const w=()=>{window.print()},x=f=>{const g=new Date(f),m=String(g.getDate()).padStart(2,"0"),u=String(g.getMonth()+1).padStart(2,"0"),N=g.getFullYear();return`${m}/${u}/${N}`},A=f=>new Date(f).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!1});return i?l.jsx("div",{className:"flex justify-center items-center h-screen",children:l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"})}):a||!r?l.jsxs("div",{className:"container mx-auto px-4 py-6 flex flex-col items-center justify-center min-h-[70vh]",children:[l.jsxs("div",{className:"bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-lg mb-4 max-w-md w-full text-center",children:[l.jsx("h2",{className:"text-xl font-bold mb-2",children:"عذراً!"}),l.jsx("p",{children:a||"حدث خطأ في تحميل الفاتورة"}),l.jsx("p",{className:"mt-2 text-sm",children:"يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى."})]}),l.jsxs("button",{onClick:()=>t(-1),className:"mt-4 flex items-center bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors",children:[l.jsx(mt,{className:"ml-2"})," العودة للصفحة السابقة"]})]}):l.jsxs("div",{className:"receipt-container",children:[l.jsxs("div",{className:"print-controls print:hidden container mx-auto px-4 py-6 mb-6",children:[l.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden",children:l.jsx("div",{className:"bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/30 dark:to-primary-800/30 border-b border-gray-200 dark:border-gray-600",children:l.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center p-6 gap-4",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx("button",{onClick:()=>t(-1),className:"bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md p-2 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors shadow-sm",children:l.jsx(mt,{})}),l.jsxs("h1",{className:"text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100 mr-4 flex items-center",children:[l.jsx(X,{className:"ml-3 text-primary-600 dark:text-primary-400"}),"فاتورة رقم #",r.id]})]}),l.jsx("div",{className:"flex items-center gap-2 flex-wrap",children:l.jsxs("button",{onClick:w,className:"bg-primary-600 text-white rounded-md py-2 px-3 sm:px-4 hover:bg-primary-700 flex items-center transition-all shadow-md hover:shadow-lg",children:[l.jsx(Ft,{className:"ml-2"}),l.jsx("span",{className:"hidden sm:inline",children:"طباعة الفاتورة"}),l.jsx("span",{className:"sm:hidden",children:"طباعة"})]})})]})})}),l.jsxs("div",{className:"receipt-size-selector",children:[l.jsx("h2",{className:"text-lg font-medium ml-4",children:"حجم الفاتورة:"}),l.jsxs("button",{onClick:()=>E("small"),className:`receipt-size-button ${d==="small"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[l.jsx(X,{}),l.jsx("span",{children:"صغير (58mm)"})]}),l.jsxs("button",{onClick:()=>E("medium"),className:`receipt-size-button ${d==="medium"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[l.jsx(X,{}),l.jsx("span",{children:"متوسط (80mm)"})]}),l.jsxs("button",{onClick:()=>E("a4"),className:`receipt-size-button ${d==="a4"?"active":"bg-gray-100 hover:bg-gray-200"}`,children:[l.jsx(kt,{}),l.jsx("span",{children:"A4"})]})]})]}),l.jsx("div",{className:`receipt-wrapper receipt-size-${d}`,children:l.jsxs("div",{className:"receipt-content",children:[l.jsxs("div",{className:"receipt-header text-center receipt-section",children:[l.jsx("h1",{children:n.name}),n.address&&l.jsx("p",{className:"mb-1",children:n.address}),n.phone&&l.jsxs("p",{className:"mb-1",children:["هاتف: ",n.phone]}),n.email&&l.jsx("p",{className:"mb-1",children:n.email}),n.receipt_header&&l.jsx("p",{className:"mt-2 italic",children:n.receipt_header}),l.jsx("div",{className:"border-t border-gray-300 dark:border-gray-700 my-3"}),l.jsx("h2",{children:"فاتورة مبيعات"})]}),l.jsxs("div",{className:"receipt-info grid grid-cols-2 gap-2 receipt-section",children:[l.jsxs("div",{children:[l.jsxs("p",{children:[l.jsx("strong",{children:"رقم الفاتورة:"})," #",r.id]}),l.jsxs("p",{children:[l.jsx("strong",{children:"التاريخ:"})," ",x(r.created_at)]}),r.customer_name&&l.jsxs("p",{children:[l.jsx("strong",{children:"العميل:"})," ",r.customer_name]})]}),l.jsxs("div",{className:"text-left",children:[l.jsxs("p",{children:[l.jsx("strong",{children:"الوقت:"})," ",A(r.created_at)]}),l.jsxs("p",{children:[l.jsx("strong",{children:"الكاشير:"})," ",r.cashier_name||"-"]}),l.jsxs("p",{children:[l.jsx("strong",{children:"رقم الفاتورة الضريبي:"})," INV-",r.id,"-",new Date(r.created_at).getFullYear()]})]})]}),l.jsxs("div",{className:"receipt-items py-2 receipt-section",children:[l.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"تفاصيل المنتجات"}),l.jsxs("table",{className:"receipt-items-table",children:[l.jsx("thead",{children:l.jsxs("tr",{children:[l.jsx("th",{className:"text-right",children:"المنتج"}),l.jsx("th",{className:"text-center",children:"الكمية"}),l.jsx("th",{className:"text-center",children:"السعر"}),l.jsx("th",{className:"text-left",children:"المجموع"})]})}),l.jsx("tbody",{children:r.items.map(f=>l.jsxs("tr",{children:[l.jsx("td",{className:"text-right",children:f.product_name||`منتج #${f.product_id}`}),l.jsx("td",{className:"text-center",children:f.quantity}),l.jsx("td",{className:"text-center",children:(f.price||f.unit_price).toFixed(2)}),l.jsx("td",{className:"text-left",children:f.subtotal.toFixed(2)})]},f.id))})]})]}),l.jsxs("div",{className:"receipt-totals receipt-section",children:[l.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"ملخص الفاتورة"}),(()=>{const f=r.discount_amount||0,g=r.total_amount,m=g-f,u=r.tax_amount||0,N=m+u;return l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:"total-row",children:[l.jsx("span",{children:"المجموع قبل الخصم:"}),l.jsxs("span",{children:[g.toFixed(2)," ",n.currency_symbol]})]}),f>0&&l.jsxs("div",{className:"total-row discount-row",children:[l.jsxs("span",{children:["الخصم (",r.discount_type==="percentage"?"نسبة مئوية":"مبلغ ثابت","):"]}),l.jsxs("span",{children:["-",f.toFixed(2)," ",n.currency_symbol,r.discount_type==="percentage"&&l.jsxs("span",{className:"text-xs mr-1",children:["(",(f/g*100).toFixed(1),"%)"]})]})]}),l.jsxs("div",{className:"total-row",children:[l.jsx("span",{children:"المجموع بعد الخصم:"}),l.jsxs("span",{children:[m.toFixed(2)," ",n.currency_symbol]})]}),u>0&&l.jsxs("div",{className:"total-row",children:[l.jsx("span",{children:"الضريبة:"}),l.jsxs("span",{children:[u.toFixed(2)," ",n.currency_symbol]})]}),l.jsxs("div",{className:"total-row grand-total",children:[l.jsx("span",{children:"المجموع النهائي:"}),l.jsxs("span",{children:[N.toFixed(2)," ",n.currency_symbol]})]})]})})()]}),l.jsxs("div",{className:"receipt-payment receipt-section",children:[l.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"معلومات الدفع"}),(()=>{const f=r.discount_amount||0,g=r.total_amount-f,m=r.tax_amount||0,N=g+m,b=r.change_amount||0;return l.jsxs(l.Fragment,{children:[l.jsxs("div",{className:"total-row",children:[l.jsx("span",{children:"طريقة الدفع:"}),l.jsx("span",{children:r.payment_method==="cash"?"نقدي":"بطاقة"})]}),l.jsxs("div",{className:"total-row",children:[l.jsx("span",{children:"المبلغ المدفوع:"}),l.jsxs("span",{children:[N.toFixed(2)," ",n.currency_symbol]})]}),r.payment_method==="cash"&&b>0&&l.jsxs("div",{className:"total-row",children:[l.jsx("span",{children:"المتبقي:"}),l.jsxs("span",{children:[b.toFixed(2)," ",n.currency_symbol]})]})]})})()]}),r.notes&&l.jsxs("div",{className:"receipt-notes mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md receipt-section",children:[l.jsx("h3",{className:"font-bold text-secondary-700 dark:text-secondary-300 mb-2",children:"ملاحظات"}),l.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:r.notes})]}),l.jsxs("div",{className:"receipt-footer receipt-section",children:[n.receipt_footer&&l.jsx("p",{className:"mb-3",children:n.receipt_footer}),l.jsx("p",{className:"mt-1",children:"** نسخة العميل **"})]}),(d==="a4"||d==="medium")&&y&&l.jsxs("div",{className:"receipt-qr",children:[l.jsx("div",{className:"qr-container",children:l.jsx("img",{src:y,alt:"QR Code",className:"qr-image"})}),l.jsxs("div",{className:"qr-info",children:[l.jsx("p",{className:"text-xs text-gray-600 dark:text-gray-400 mb-1",children:"امسح الرمز للحصول على معلومات الفاتورة"}),l.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:["هاتف المتجر: ",n.phone]})]})]}),d==="a4"&&l.jsx("div",{className:"receipt-stamp",children:"مدفوع"})]})})]})};export{Se as default};
